#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试充电分析修复效果
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_charging_data():
    """创建测试用的充电基准数据"""
    # 创建一个简单的充电基准数据表
    data = []
    
    # 温度范围: 0-50度，每5度一个点
    temps = [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50]
    
    # 电压范围: 3000-4000mV，每100mV一个点
    voltages = [3000, 3100, 3200, 3300, 3400, 3500, 3600, 3700, 3800, 3900, 4000]
    
    for temp in temps:
        for voltage in voltages:
            # 模拟电流计算：电流随温度和电压变化
            # 简化公式：电流 = 基础值 + 温度系数 * 温度 + 电压系数 * (电压-3500)
            base_current = 3.5
            temp_coeff = 0.01  # 温度系数
            voltage_coeff = 0.0001  # 电压系数
            
            current = base_current + temp_coeff * temp + voltage_coeff * (voltage - 3500)
            # 添加一些随机噪声
            current += np.random.normal(0, 0.05)
            
            data.append({
                'temp': temp,
                'voltage': voltage,
                'current': round(current, 6)
            })
    
    return pd.DataFrame(data)

def create_test_input_data():
    """创建测试用的输入数据"""
    return pd.DataFrame({
        'MinT': [20, 25, 30],
        'MaxT': [25, 30, 35],
        'MinV': [3500, 3600, 3700],
        'MaxV': [3600, 3700, 3800],
        'time': [0, 1, 2]
    })

def test_calculate_current():
    """测试电流计算函数"""
    print("=== 测试电流计算函数 ===")
    
    # 创建测试数据
    charging_data = create_test_charging_data()
    print(f"充电基准数据: {len(charging_data)} 行")
    print("基准数据样本:")
    print(charging_data.head(10))
    
    # 模拟ChargingAnalysisWorker的calculate_current方法
    class TestWorker:
        def __init__(self, charging_data):
            self.charging_data = charging_data
        
        def calculate_current(self, temp, voltage):
            """简化版的电流计算方法"""
            try:
                temp = float(temp)
                voltage = float(voltage)
                
                print(f"  计算输入: temp={temp}, voltage={voltage}")
                
                # 1. 温度层选择 - 找到最接近输入温度的温度层
                temp_filtered = self.charging_data[self.charging_data['temp'] <= temp]
                if len(temp_filtered) < 5:  # 简化版本，只需要5个点
                    temp_filtered = self.charging_data.nsmallest(5, 'temp')
                
                if temp_filtered.empty:
                    raise ValueError("在基准数据中找不到合适的温度层")

                temp_sorted = temp_filtered.iloc[(temp_filtered['temp'] - temp).abs().argsort()[:5]]
                common_temp = temp_sorted['temp'].mode()[0]
                temp_df = temp_sorted[temp_sorted['temp'] == common_temp].sort_values('voltage')
                
                print(f"    选择的温度层: {common_temp}°C")
                print(f"    该温度层的电压范围: {temp_df['voltage'].min()}-{temp_df['voltage'].max()}mV")
                
                # 2. 电压筛选 - 修复版本
                # 首先尝试精确匹配
                exact_match = temp_df[temp_df['voltage'] == voltage]
                if not exact_match.empty:
                    voltage_used = voltage
                    current_at_voltage = exact_match['current'].iloc[0]
                    print(f"    精确匹配: voltage_used={voltage_used}, current={current_at_voltage}")
                    return current_at_voltage, voltage_used
                
                # 如果没有精确匹配，查找最接近且大于等于输入电压的值
                voltage_candidates = temp_df[temp_df['voltage'] >= voltage]
                if not voltage_candidates.empty:
                    voltage_used = voltage_candidates['voltage'].iloc[0]
                    print(f"    向上查找: voltage_used={voltage_used}")
                else:
                    # 如果所有电压都小于输入电压，使用最大电压
                    voltage_used = temp_df['voltage'].max()
                    print(f"    使用最大电压: voltage_used={voltage_used}")
                
                # 获取对应的电流值
                current_at_voltage_used = temp_df[temp_df['voltage'] == voltage_used]['current'].iloc[0]
                print(f"    最终结果: current={current_at_voltage_used}")
                
                return current_at_voltage_used, voltage_used
                
            except Exception as e:
                raise RuntimeError(f"电流计算失败 (输入 temp={temp}, voltage={voltage}): {str(e)}")
    
    # 创建测试工作器
    worker = TestWorker(charging_data)
    
    # 测试几个具体的计算
    test_cases = [
        (25, 3600),  # 精确匹配
        (22, 3610),  # 需要插值的情况
        (30, 3550),  # 电压在范围内
        (35, 4100),  # 电压超出范围
    ]
    
    print("\n=== 测试用例 ===")
    for i, (temp, voltage) in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: temp={temp}°C, voltage={voltage}mV")
        try:
            current, voltage_used = worker.calculate_current(temp, voltage)
            print(f"结果: current={current:.6f}, voltage_used={voltage_used}")
        except Exception as e:
            print(f"错误: {e}")

def test_four_combinations():
    """测试四种温度电压组合"""
    print("\n=== 测试四种温度电压组合 ===")
    
    charging_data = create_test_charging_data()
    input_data = create_test_input_data()
    
    class TestWorker:
        def __init__(self, charging_data):
            self.charging_data = charging_data
        
        def calculate_current(self, temp, voltage):
            # 简化版本，直接查找最接近的值
            temp_diff = abs(self.charging_data['temp'] - temp)
            voltage_diff = abs(self.charging_data['voltage'] - voltage)
            combined_diff = temp_diff + voltage_diff * 0.01  # 给电压差一个较小的权重
            
            closest_idx = combined_diff.idxmin()
            closest_row = self.charging_data.loc[closest_idx]
            
            return closest_row['current'], closest_row['voltage']
    
    worker = TestWorker(charging_data)
    
    print("输入数据:")
    print(input_data)
    
    for idx, row in input_data.iterrows():
        min_temp = row['MinT']
        max_temp = row['MaxT']
        min_voltage = row['MinV']
        max_voltage = row['MaxV']
        
        print(f"\n行 {idx}: 温度范围=[{min_temp}, {max_temp}], 电压范围=[{min_voltage}, {max_voltage}]")
        
        # 四种组合计算
        curr_minTminV, v_used_minTminV = worker.calculate_current(min_temp, min_voltage)
        curr_maxTmaxV, v_used_maxTmaxV = worker.calculate_current(max_temp, max_voltage)
        curr_minTmaxV, v_used_minTmaxV = worker.calculate_current(min_temp, max_voltage)
        curr_maxTminV, v_used_maxTminV = worker.calculate_current(max_temp, min_voltage)
        
        print(f"  curr_minTminV={curr_minTminV:.6f} (T={min_temp}, V={min_voltage}) -> 使用电压={v_used_minTminV}")
        print(f"  curr_maxTmaxV={curr_maxTmaxV:.6f} (T={max_temp}, V={max_voltage}) -> 使用电压={v_used_maxTmaxV}")
        print(f"  curr_minTmaxV={curr_minTmaxV:.6f} (T={min_temp}, V={max_voltage}) -> 使用电压={v_used_minTmaxV}")
        print(f"  curr_maxTminV={curr_maxTminV:.6f} (T={max_temp}, V={min_voltage}) -> 使用电压={v_used_maxTminV}")
        
        # 找出最小电流
        current_options = [
            (curr_minTminV, v_used_minTminV),
            (curr_maxTmaxV, v_used_maxTmaxV),
            (curr_minTmaxV, v_used_minTmaxV),
            (curr_maxTminV, v_used_maxTminV)
        ]
        final_current, final_voltage_used = min(current_options, key=lambda item: item[0])
        print(f"  最小电流: {final_current:.6f}, 对应使用电压: {final_voltage_used}")

if __name__ == "__main__":
    print("充电分析修复效果测试")
    print("=" * 50)
    
    # 设置随机种子以获得可重复的结果
    np.random.seed(42)
    
    test_calculate_current()
    test_four_combinations()
    
    print("\n测试完成!")
