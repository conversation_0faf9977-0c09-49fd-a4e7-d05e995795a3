#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
充电分析模块 - PyQt6版本
实现优化的充电分析算法和扩展的数据导出功能
"""

import numpy as np
import pandas as pd
from datetime import datetime
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
        QLabel, QProgressBar, QTextEdit, QGroupBox, QFileDialog,
        QMessageBox, QTabWidget, QWidget, QTableWidget, QTableWidgetItem,
        QComboBox, QSpinBox, QDoubleSpinBox, QCheckBox, QScrollArea,
        QFrame, QSplitter
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
    from PyQt6.QtGui import QFont, QPixmap

    # 导入matplotlib用于图表
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure

except ImportError as e:
    print(f"错误: 缺少必要的库 - {e}")
    import sys
    sys.exit(1)


class ChargingAnalysisWorker(QThread):
    """
    充电分析工作线程 - 融合了 chargevoltage.py 的核心算法
    - 支持四种温度/电压组合查询
    - 采用 chargevoltage.py 的 prev_voltage 更新策略
    - 采用 chargevoltage.py 的电流计算和插值算法
    """

    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    analysis_completed = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)

    def __init__(self, data, analysis_params, charging_data=None, app_data=None):
        super().__init__()
        self.data = data
        self.analysis_params = analysis_params
        self.charging_data = charging_data
        self.app_data = app_data
        self.is_cancelled = False

    def run(self):
        """执行充电分析 - 采用基于 chargevoltage.py 的新组合算法"""
        try:
            # === 数据加载与准备 ===
            self.status_updated.emit("正在加载并预处理基准数据...")
            self.progress_updated.emit(10)

            if self.charging_data is None:
                self.error_occurred.emit("缺少充电基准数据，请先导入充电基准文件")
                return

            # 数据清洗 (来自 chargevoltage.py)
            self.charging_data = self.charging_data[
                ~self.charging_data['temp'].astype(str).str.contains('<-30|>65', na=False) &
                self.charging_data['temp'].notna()
            ]
            if self.charging_data['temp'].dtype == 'object':
                self.charging_data['temp'] = pd.to_numeric(
                    self.charging_data['temp'].astype(str).str.replace('[^0-9.-]', '', regex=True),
                    errors='coerce'
                )
            self.charging_data = self.charging_data.dropna(subset=['temp'])
            if self.charging_data.empty:
                self.error_occurred.emit("充电基准数据为空或在清理后变为空。")
                return
            
            # === 输入数据处理与列映射 ===
            self.status_updated.emit("正在处理输入数据...")
            self.progress_updated.emit(30)
            
            mapping_config = {}
            if hasattr(self.app_data, 'data_mapping_config') and self.app_data.data_mapping_config:
                mappings = self.app_data.data_mapping_config.get('mappings', {})
                mapping_config = {logic_name: csv_col for csv_col, logic_name in mappings.items()}

            self.min_temp_col = mapping_config.get('MinT', 'MinT')
            self.max_voltage_col = mapping_config.get('MaxV', 'MaxV')
            self.min_voltage_col = mapping_config.get('MinV', 'MinV')
            self.max_temp_col = mapping_config.get('MaxT', 'MaxT')

            required_cols = [self.min_temp_col, self.max_voltage_col, self.min_voltage_col, self.max_temp_col]
            missing_cols = [col for col in required_cols if col not in self.data.columns]
            if missing_cols:
                self.error_occurred.emit(f"数据映射检查失败，缺少必要的列: {', '.join(missing_cols)}")
                return

            input_df = self.data.dropna(subset=required_cols).copy()

            # === 创建温度标记列 (来自 chargevoltage.py) ===
            self.status_updated.emit("创建温度标记...")
            self.progress_updated.emit(40)
            self.create_marker_column(input_df)

            # === 电流计算阶段 (基于chargevoltage.py的四组合扩展算法) ===
            curr_all_list = []
            curr_minTminV_list = []
            curr_maxTmaxV_list = []
            curr_minTmaxV_list = []
            curr_maxTminV_list = []
            voltage_used_list = []

            # 初始化 prev_voltage，这是 chargevoltage.py 算法的关键部分 (第179行)
            prev_voltage = 0

            total_rows = len(input_df)
            for idx, row in input_df.iterrows():
                if self.is_cancelled:
                    return

                progress = 40 + int(55 * (idx / total_rows))
                self.progress_updated.emit(progress)
                self.status_updated.emit(f"计算进度: {idx+1}/{total_rows}...")

                # 从当前行获取所有温度和电压值
                min_temp = row[self.min_temp_col]
                max_temp = row[self.max_temp_col]
                min_voltage_from_file = row[self.min_voltage_col]
                max_voltage_from_file = row[self.max_voltage_col]
                marker_a = row['a']

                # === chargevoltage.py的核心电压更新策略 (第188-194行) ===
                # 原始算法只处理一种组合 (MinT + MaxV)，现在扩展为四种组合

                # 1. 对MinV应用chargevoltage.py的电压更新策略
                if min_voltage_from_file >= prev_voltage or marker_a == 1:
                    v_for_min_calc = min_voltage_from_file
                else:
                    v_for_min_calc = prev_voltage - 0.001

                # 2. 对MaxV应用chargevoltage.py的电压更新策略
                if max_voltage_from_file >= prev_voltage or marker_a == 1:
                    v_for_max_calc = max_voltage_from_file
                else:
                    v_for_max_calc = prev_voltage - 0.001

                # === 四种组合计算 (扩展chargevoltage.py的单一计算) ===
                # 原始: current, voltage_used = self.calculate_current(temp, voltage)
                # 扩展为四种温度电压组合:
                curr_minTminV, v_used_minTminV = self.calculate_current(min_temp, v_for_min_calc)
                curr_maxTmaxV, v_used_maxTmaxV = self.calculate_current(max_temp, v_for_max_calc)
                curr_minTmaxV, v_used_minTmaxV = self.calculate_current(min_temp, v_for_max_calc)
                curr_maxTminV, v_used_maxTminV = self.calculate_current(max_temp, v_for_min_calc)

                # 调试输出（仅在前几行显示）
                if idx < 3:
                    print(f"行 {idx}: 输入温度范围=[{min_temp}, {max_temp}], 输入电压范围=[{min_voltage_from_file}, {max_voltage_from_file}]")
                    print(f"  prev_voltage={prev_voltage}, marker_a={marker_a}")
                    print(f"  电压更新策略结果: v_for_min_calc={v_for_min_calc}, v_for_max_calc={v_for_max_calc}")
                    print(f"  四种组合结果:")
                    print(f"    curr_minTminV={curr_minTminV:.6f} (T={min_temp}, V={v_for_min_calc}) -> 使用电压={v_used_minTminV}")
                    print(f"    curr_maxTmaxV={curr_maxTmaxV:.6f} (T={max_temp}, V={v_for_max_calc}) -> 使用电压={v_used_maxTmaxV}")
                    print(f"    curr_minTmaxV={curr_minTmaxV:.6f} (T={min_temp}, V={v_for_max_calc}) -> 使用电压={v_used_minTmaxV}")
                    print(f"    curr_maxTminV={curr_maxTminV:.6f} (T={max_temp}, V={v_for_min_calc}) -> 使用电压={v_used_maxTminV}")

                # 将所有中间结果添加到列表，用于最终输出
                curr_minTminV_list.append(curr_minTminV)
                curr_maxTmaxV_list.append(curr_maxTmaxV)
                curr_minTmaxV_list.append(curr_minTmaxV)
                curr_maxTminV_list.append(curr_maxTminV)

                # === 选择最优结果 (扩展chargevoltage.py的单一结果) ===
                # 原始: prev_voltage = voltage_used (第191行)
                # 扩展: 从四种组合中选择最小电流对应的电压
                current_options = [
                    (curr_minTminV, v_used_minTminV),
                    (curr_maxTmaxV, v_used_maxTmaxV),
                    (curr_minTmaxV, v_used_minTmaxV),
                    (curr_maxTminV, v_used_maxTminV)
                ]
                final_current, final_voltage_used = min(current_options, key=lambda item: item[0])

                if idx < 3:
                    print(f"  最小电流: {final_current:.6f}, 对应使用电压: {final_voltage_used}")
                    print(f"  prev_voltage更新: {prev_voltage} -> {final_voltage_used}")

                curr_all_list.append(final_current)
                voltage_used_list.append(final_voltage_used)

                # === chargevoltage.py的prev_voltage更新策略 (第191行) ===
                # 原始: prev_voltage = voltage_used
                # 扩展: prev_voltage = final_voltage_used (来自四种组合的最优选择)
                prev_voltage = final_voltage_used

            # === 结果整合与完成 ===
            input_df['curr_all'] = curr_all_list
            input_df['curr_minTminV'] = curr_minTminV_list
            input_df['curr_maxTmaxV'] = curr_maxTmaxV_list
            input_df['curr_minTmaxV'] = curr_minTmaxV_list
            input_df['curr_maxTminV'] = curr_maxTminV_list
            input_df['使用电压'] = voltage_used_list

            self.status_updated.emit("分析完成!")
            self.progress_updated.emit(100)

            final_results = {
                'analysis_results': input_df,
                'summary': {
                    'total_rows': len(input_df),
                    'analysis_method': 'chargevoltage.py 核心算法 (四组合优化)',
                    'timestamp': datetime.now().isoformat()
                }
            }
            self.analysis_completed.emit(final_results)

        except Exception as e:
            import traceback
            self.error_occurred.emit(f"分析过程中发生严重错误: {str(e)}\n{traceback.format_exc()}")

    def cancel(self):
        """取消分析"""
        self.is_cancelled = True

    def create_marker_column(self, input_df):
        """创建温度标记列 - 算法来自 chargevoltage.py"""
        a_list = []
        min_temp_col_name = self.min_temp_col 

        for i in range(len(input_df)):
            current_val = input_df[min_temp_col_name].iloc[i]
            
            if i == 0:
                prev_base = -np.inf
            else:
                prev_val = input_df[min_temp_col_name].iloc[i-1]
                prev_base = (prev_val // 5) * 5
                
            current_base = (current_val // 5) * 5
            
            # 标记条件：当温度跨越5的倍数边界，且新温度大于等于这个边界时
            condition = (current_val >= current_base) and (prev_base < current_base)
            a = 1 if condition else 0
            a_list.append(a)
        
        input_df['a'] = a_list

    def calculate_current(self, temp, voltage):
        """电流计算与插值核心算法 - 基于chargevoltage.py的精确实现"""
        try:
            temp = float(temp)
            voltage = float(voltage)

            # === 1. 温度层选择 (完全按照chargevoltage.py的逻辑) ===
            temp_filtered = self.charging_data[self.charging_data['temp'] <= temp]
            if len(temp_filtered) < 23:
                temp_filtered = self.charging_data.nsmallest(23, 'temp')

            if temp_filtered.empty:
                raise ValueError("在基准数据中找不到合适的温度层")

            temp_sorted = temp_filtered.iloc[(temp_filtered['temp'] - temp).abs().argsort()[:23]]
            common_temp = temp_sorted['temp'].mode()[0]
            temp_df = temp_sorted[temp_sorted['temp'] == common_temp].sort_values('voltage')

            # === 2. 电压筛选 (完全按照chargevoltage.py的逻辑) ===
            try:
                # 查找第一个电压大于输入电压的行 (chargevoltage.py第236行)
                voltage_used = temp_df[temp_df['voltage'] > voltage]['voltage'].iloc[0]
            except IndexError:
                # 如果没有，则使用该温度层的最大电压 (chargevoltage.py第238行)
                voltage_used = temp_df['voltage'].max()

            # === 3. 温度插值 (完全按照chargevoltage.py的逻辑) ===
            temp_below = self.charging_data[self.charging_data['temp'] <= temp].nlargest(23, 'temp')
            temp_above = self.charging_data[self.charging_data['temp'] > temp].nsmallest(23, 'temp')

            if temp_below.empty or temp_above.empty:
                # 如果无法进行插值，则返回最近点的值 (chargevoltage.py第245-246行)
                nearest = self.charging_data.iloc[(self.charging_data['temp'] - temp).abs().argsort()[0]]
                return nearest['current'], nearest['voltage']

            # 安全获取插值点 (chargevoltage.py第249-250行)
            df_below_options = temp_below[temp_below['voltage'] >= voltage_used]
            df_above_options = temp_above[temp_above['voltage'] >= voltage_used]

            if df_below_options.empty or df_above_options.empty:
                # 如果缺少插值点，直接使用当前温度层的结果
                current_at_voltage_used = temp_df[temp_df['voltage'] == voltage_used]['current'].iloc[0]
                return current_at_voltage_used, voltage_used

            df_below = df_below_options.iloc[0]
            df_above = df_above_options.iloc[0]

            # 执行线性插值 (chargevoltage.py第252-256行)
            current = np.interp(
                temp,
                [df_below['temp'], df_above['temp']],
                [df_below['current'], df_above['current']]
            )

            return current, voltage_used

        except Exception as e:
            # 在计算失败时抛出异常，由上层 run() 方法捕获
            raise RuntimeError(f"电流计算失败 (输入 temp={temp}, voltage={voltage}): {str(e)}")

    # 其他辅助方法 (preprocess_data, perform_multi_lookup_analysis 等) 可以保持不变或删除
    # 因为它们在新的 run() 逻辑中没有被调用


class ChargingAnalysisDialog(QDialog):
    """充电分析对话框"""

    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.app_data = app_data
        self.analysis_worker = None
        self.analysis_results = None

        self.init_ui()

        # 初始化映射状态显示
        self.update_mapping_status_display()

    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("充电分析 - 多种查表方式分析")
        self.setGeometry(150, 150, 1000, 700)
        self.setModal(True)

        # 创建主布局
        main_layout = QVBoxLayout(self)

        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)

        # 创建各个标签页
        self.create_analysis_tab()
        self.create_results_tab()
        self.create_export_tab()

        # 创建底部按钮
        self.create_bottom_buttons(main_layout)

        # self.setup_styles() # Styles are now applied globally

    def import_reference_data(self):
        """导入充电基准数据"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择充电基准数据文件",
            "",
            "CSV文件 (*.csv);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf_8_sig')

            # 验证必要列
            required_columns = ['temp', 'voltage', 'current']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                QMessageBox.warning(
                    self,
                    "列缺失",
                    f"充电基准数据文件缺少必要列: {', '.join(missing_columns)}\n"
                    f"需要包含: {', '.join(required_columns)}"
                )
                return

            # 更新应用数据
            self.app_data.charging_reference_data = df
            self.app_data.charging_reference_filepath = file_path

            # 更新显示
            ref_rows, ref_cols = df.shape
            ref_info = f"基准数据: {ref_rows} 行 × {ref_cols} 列 ({Path(file_path).name})"
            if hasattr(self, 'reference_label'):
                self.reference_label.setText(ref_info)

            # 显示成功消息
            QMessageBox.information(
                self,
                "导入成功",
                f"成功导入充电基准数据: {Path(file_path).name}\n"
                f"数据规模: {ref_rows}行 × {ref_cols}列"
            )

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"无法导入充电基准数据文件:\n{str(e)}")

    def update_mapping_status_display(self):
        """更新映射状态显示"""
        if not hasattr(self, 'mapping_status_label'):
            return

        # 修复：使用正确的映射配置
        mapping_config = {}
        if hasattr(self.app_data, 'data_mapping_config') and self.app_data.data_mapping_config:
            # 从data_mapping_config中提取映射关系
            mappings = self.app_data.data_mapping_config.get('mappings', {})
            # 反转映射关系：从 {csv_col: logic_name} 转为 {logic_name: csv_col}
            mapping_config = {logic_name: csv_col for csv_col, logic_name in mappings.items()}

        current_data = self.app_data.filtered_data if self.app_data.filter_applied else self.app_data.imported_data

        if current_data is None:
            self.mapping_status_label.setText("无数据，无法检查映射状态")
            return

        available_columns = list(current_data.columns)

        # 检查四个关键列的映射状态
        required_mappings = ['MinT', 'MaxV', 'MinV', 'MaxT']
        status_lines = []

        for mapping_key in required_mappings:
            mapped_column = mapping_config.get(mapping_key, mapping_key)
            if mapped_column in available_columns:
                status_lines.append(f"✅ {mapping_key} → {mapped_column}")
            else:
                status_lines.append(f"❌ {mapping_key} → {mapped_column} (列不存在)")

        status_text = "\n".join(status_lines)
        self.mapping_status_label.setText(status_text)

    def create_analysis_tab(self):
        """创建分析设置标签页"""
        analysis_widget = QWidget()
        layout = QVBoxLayout(analysis_widget)

        # 数据信息组
        data_group = QGroupBox("数据信息")
        data_layout = QVBoxLayout(data_group)

        current_data = self.app_data.filtered_data if self.app_data.filter_applied else self.app_data.imported_data
        if current_data is not None:
            rows, cols = current_data.shape
            data_info = f"当前数据: {rows} 行 × {cols} 列"
            if self.app_data.filter_applied:
                data_info += " (已筛选)"
        else:
            data_info = "无数据"

        data_label = QLabel(data_info)
        data_layout.addWidget(data_label)
        layout.addWidget(data_group)

        # 充电基准数据组
        reference_group = QGroupBox("充电基准数据")
        reference_layout = QVBoxLayout(reference_group)

        # 基准数据状态
        if self.app_data.charging_reference_data is not None:
            ref_rows, ref_cols = self.app_data.charging_reference_data.shape
            ref_info = f"基准数据: {ref_rows} 行 × {ref_cols} 列"
            if self.app_data.charging_reference_filepath:
                ref_info += f" ({Path(self.app_data.charging_reference_filepath).name})"
        else:
            ref_info = "未导入基准数据"

        self.reference_label = QLabel(ref_info)
        reference_layout.addWidget(self.reference_label)

        # 导入基准数据按钮
        import_ref_btn = QPushButton("导入充电基准数据")
        import_ref_btn.clicked.connect(self.import_reference_data)
        reference_layout.addWidget(import_ref_btn)

        layout.addWidget(reference_group)

        # 映射状态组
        mapping_group = QGroupBox("数据映射状态")
        mapping_layout = QVBoxLayout(mapping_group)

        self.mapping_status_label = QLabel()
        self.update_mapping_status_display()
        mapping_layout.addWidget(self.mapping_status_label)

        layout.addWidget(mapping_group)

        # 分析参数组
        params_group = QGroupBox("分析参数")
        params_layout = QGridLayout(params_group)

        # 查表方式说明
        method_label = QLabel("查表方式:")
        method_label.setFont(QFont("", 10, QFont.Weight.Bold))
        params_layout.addWidget(method_label, 0, 0, 1, 2)

        methods_text = """
        1. C(MaxT, MinV) - 最高温度与最低电压组合
        2. C(MaxT, MaxV) - 最高温度与最高电压组合
        3. C(MinT, MinV) - 最低温度与最低电压组合
        4. C(MinT, MaxV) - 最低温度与最高电压组合

        最终结果: min(方式1, 方式2, 方式3, 方式4)
        """
        methods_display = QLabel(methods_text)
        methods_display.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        params_layout.addWidget(methods_display, 1, 0, 1, 2)

        layout.addWidget(params_group)

        # 分析控制组
        control_group = QGroupBox("分析控制")
        control_layout = QVBoxLayout(control_group)

        # 开始分析按钮
        self.start_analysis_btn = QPushButton("开始充电分析")
        self.start_analysis_btn.clicked.connect(self.start_analysis)
        self.start_analysis_btn.setMinimumHeight(40)
        control_layout.addWidget(self.start_analysis_btn)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)

        # 状态标签
        self.status_label = QLabel("准备开始分析")
        control_layout.addWidget(self.status_label)

        layout.addWidget(control_group)

        layout.addStretch()
        self.tab_widget.addTab(analysis_widget, "分析设置")

    def create_results_tab(self):
        """创建结果显示标签页"""
        results_widget = QWidget()
        layout = QVBoxLayout(results_widget)

        # 结果摘要组
        summary_group = QGroupBox("分析摘要")
        summary_layout = QVBoxLayout(summary_group)

        self.summary_text = QTextEdit()
        self.summary_text.setReadOnly(True)
        self.summary_text.setMaximumHeight(150)
        summary_layout.addWidget(self.summary_text)

        layout.addWidget(summary_group)

        # 详细结果组
        details_group = QGroupBox("详细结果")
        details_layout = QVBoxLayout(details_group)

        self.results_table = QTableWidget()
        details_layout.addWidget(self.results_table)

        layout.addWidget(details_group)

        self.tab_widget.addTab(results_widget, "分析结果")

    def create_export_tab(self):
        """创建导出设置标签页"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)

        # 导出选项组
        options_group = QGroupBox("导出选项")
        options_layout = QGridLayout(options_group)

        # 导出格式选择
        format_label = QLabel("导出格式:")
        self.export_format = QComboBox()
        self.export_format.addItems(["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"])
        options_layout.addWidget(format_label, 0, 0)
        options_layout.addWidget(self.export_format, 0, 1)

        # 包含内容选择
        content_label = QLabel("包含内容:")
        options_layout.addWidget(content_label, 1, 0)

        self.include_summary = QCheckBox("分析摘要")
        self.include_summary.setChecked(True)
        options_layout.addWidget(self.include_summary, 1, 1)

        self.include_details = QCheckBox("详细结果")
        self.include_details.setChecked(True)
        options_layout.addWidget(self.include_details, 2, 1)

        self.include_raw_data = QCheckBox("原始数据")
        options_layout.addWidget(self.include_raw_data, 3, 1)

        self.include_recommendations = QCheckBox("分析建议")
        self.include_recommendations.setChecked(True)
        options_layout.addWidget(self.include_recommendations, 4, 1)

        layout.addWidget(options_group)

        # 导出按钮
        export_btn = QPushButton("导出分析结果")
        export_btn.clicked.connect(self.export_results)
        export_btn.setMinimumHeight(40)
        layout.addWidget(export_btn)

        layout.addStretch()
        self.tab_widget.addTab(export_widget, "导出设置")

    def create_bottom_buttons(self, parent_layout):
        """创建底部按钮"""
        button_layout = QHBoxLayout()

        # 返回按钮
        return_btn = QPushButton("返回主页")
        return_btn.clicked.connect(self.accept)
        button_layout.addWidget(return_btn)

        button_layout.addStretch()

        # 取消分析按钮
        self.cancel_btn = QPushButton("取消分析")
        self.cancel_btn.clicked.connect(self.cancel_analysis)
        self.cancel_btn.setVisible(False)
        button_layout.addWidget(self.cancel_btn)

        parent_layout.addLayout(button_layout)

    def start_analysis(self):
        """开始充电分析"""
        current_data = self.app_data.filtered_data if self.app_data.filter_applied else self.app_data.imported_data

        if current_data is None:
            QMessageBox.warning(self, "无数据", "没有可分析的数据。")
            return

        # 检查充电基准数据
        if self.app_data.charging_reference_data is None:
            QMessageBox.warning(self, "缺少基准数据",
                              "请先导入充电基准数据文件（包含temp, voltage, current列）。")
            return

        # 准备分析参数
        analysis_params = {
            'method': 'charging_analysis_algorithm',
            'timestamp': datetime.now().isoformat()
        }

        # 创建工作线程，传递充电基准数据和app_data
        self.analysis_worker = ChargingAnalysisWorker(
            current_data,
            analysis_params,
            self.app_data.charging_reference_data,
            self.app_data
        )

        # 连接信号
        self.analysis_worker.progress_updated.connect(self.update_progress)
        self.analysis_worker.status_updated.connect(self.update_status)
        self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
        self.analysis_worker.error_occurred.connect(self.on_analysis_error)

        # 更新UI状态
        self.start_analysis_btn.setEnabled(False)
        self.cancel_btn.setVisible(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动分析
        self.analysis_worker.start()

    def cancel_analysis(self):
        """取消分析"""
        if self.analysis_worker and self.analysis_worker.isRunning():
            self.analysis_worker.cancel()
            self.analysis_worker.wait()

        self.reset_ui_state()
        self.status_label.setText("分析已取消")

    def update_progress(self, value):
        """更新进度条"""
        self.progress_bar.setValue(value)

    def update_status(self, message):
        """更新状态信息"""
        self.status_label.setText(message)

    def on_analysis_completed(self, results):
        """分析完成处理"""
        self.analysis_results = results # This will be the dict from run()

        # 保存分析结果到共享数据，供其他模块使用
        self.app_data.charging_analysis_results = results
        self.app_data.charging_analysis_timestamp = datetime.now().isoformat()

        self.display_results()
        self.reset_ui_state()

        # 切换到结果标签页
        self.tab_widget.setCurrentIndex(1)

        # 通知用户分析结果已可用于其他功能
        QMessageBox.information(
            self,
            "分析完成",
            "充电分析已完成！\n\n分析结果已保存，可在数据分析和导出功能中使用。"
        )

    def on_analysis_error(self, error_message):
        """分析错误处理"""
        QMessageBox.critical(self, "分析错误", error_message)
        self.reset_ui_state()

    def reset_ui_state(self):
        """重置UI状态"""
        self.start_analysis_btn.setEnabled(True)
        self.cancel_btn.setVisible(False)
        self.progress_bar.setVisible(False)

    def display_results(self):
        """显示分析结果"""
        if not self.analysis_results or 'summary' not in self.analysis_results or 'analysis_results' not in self.analysis_results:
            self.summary_text.setPlainText("分析结果的格式不正确或不完整。")
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            return

        # 显示摘要
        summary_data = self.analysis_results['summary']
        summary_text = self.format_summary(summary_data)
        self.summary_text.setPlainText(summary_text)

        # 显示详细结果表格
        # The 'analysis_results' from run() is a DataFrame
        results_df = self.analysis_results['analysis_results']
        self.populate_results_table(results_df)

    def format_summary(self, summary_data):
        """格式化摘要信息"""
        text = []
        text.append("=== 充电分析摘要 ===")
        text.append(f"分析方法: {summary_data.get('analysis_method', 'N/A')}")
        text.append(f"总处理行数: {summary_data.get('total_rows', 'N/A')}")
        text.append(f"分析时间: {summary_data.get('timestamp', 'N/A')}")

        # If the other analysis type's data is present, show it (for future flexibility)
        if 'min_lookup_value' in summary_data:
            text.append(f"最小查表值: {summary_data['min_lookup_value']:.6f}")
        if 'lookup_methods_count' in summary_data:
            text.append(f"查表方式数量: {summary_data['lookup_methods_count']}")

        text.append("")

        # Example: Add recommendations if they exist (though current run() doesn't produce them)
        if 'recommendations' in self.analysis_results.get('report_data', {}):
            text.append("=== 分析建议 ===")
            for rec in self.analysis_results['report_data']['recommendations']:
                text.append(f"• {rec}")

        return "\n".join(text)

    def populate_results_table(self, results_df):
        """填充结果表格 - Adapted for DataFrame from run() method """
        if results_df is None or results_df.empty:
            self.results_table.setRowCount(0)
            self.results_table.setColumnCount(0)
            self.results_table.setHorizontalHeaderLabels(["无数据"])
            return

        self.results_table.setRowCount(len(results_df))
        self.results_table.setColumnCount(len(results_df.columns))
        self.results_table.setHorizontalHeaderLabels(results_df.columns)

        for i, (idx, row) in enumerate(results_df.iterrows()):
            for j, col_name in enumerate(results_df.columns):
                value = row[col_name]
                item_text = ""
                if pd.isna(value):
                    item_text = "N/A"
                elif isinstance(value, float):
                    item_text = f"{value:.3f}" # Format float
                else:
                    item_text = str(value)
                self.results_table.setItem(i, j, QTableWidgetItem(item_text))

        self.results_table.resizeColumnsToContents()
        # Remove highlighting logic for min_value as it's specific to the other analysis type
        # If needed later, it can be added based on '计算电流' or similar.

    def get_method_description(self, method):
        """获取查表方式说明 - This is for the multi-lookup method, may not be directly used by run() results"""
        descriptions = {
            'MaxT_MinV': '最高温度 + 最低电压',
            'MaxT_MaxV': '最高温度 + 最高电压',
            'MinT_MinV': '最低温度 + 最低电压',
            'MinT_MaxV': '最低温度 + 最高电压'
        }
        return descriptions.get(method, '未知方式')

    def export_results(self):
        """导出分析结果表格"""
        if not self.analysis_results or 'analysis_results' not in self.analysis_results:
            QMessageBox.warning(self, "无结果", "没有可导出的分析结果。")
            return

        # 获取分析结果DataFrame
        results_df = self.analysis_results['analysis_results']
        if results_df is None or results_df.empty:
            QMessageBox.warning(self, "无数据", "分析结果为空，无法导出。")
            return

        # 获取导出格式
        format_text = self.export_format.currentText()
        if "Excel" in format_text:
            file_filter = "Excel文件 (*.xlsx)"
            default_ext = ".xlsx"
        elif "CSV" in format_text:
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"
        else:  # JSON - 对于表格数据，转换为CSV
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"

        # 选择保存位置
        default_filename = f"charging_analysis_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}{default_ext}"
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出分析结果表格",
            default_filename,
            file_filter
        )

        if not file_path:
            return

        try:
            # 直接导出DataFrame
            if "Excel" in format_text:
                results_df.to_excel(file_path, index=False, engine='openpyxl')
            else:
                results_df.to_csv(file_path, index=False, encoding='utf-8-sig')

            QMessageBox.information(self, "导出成功", f"分析结果表格已导出到:\n{file_path}")

        except Exception as e:
            QMessageBox.critical(self, "导出失败", f"导出过程中发生错误: {str(e)}")

    def perform_export(self, file_path, format_text):
        """执行导出操作"""
        export_data = self.prepare_export_data()

        if "Excel" in format_text:
            self.export_to_excel(file_path, export_data)
        elif "CSV" in format_text:
            self.export_to_csv(file_path, export_data)
        else:  # JSON
            self.export_to_json(file_path, export_data)

    def prepare_export_data(self):
        """准备导出数据"""
        export_data = {}

        # 修复：正确访问分析结果的数据结构
        if self.include_summary.isChecked():
            # 从正确的位置获取摘要数据
            if 'summary' in self.analysis_results:
                export_data['summary'] = self.analysis_results['summary']
            else:
                export_data['summary'] = {'message': '无摘要数据'}

        if self.include_details.isChecked():
            # 导出分析结果DataFrame
            if 'analysis_results' in self.analysis_results:
                analysis_df = self.analysis_results['analysis_results']
                if hasattr(analysis_df, 'to_dict'):
                    export_data['detailed_results'] = analysis_df.to_dict('records')
                else:
                    export_data['detailed_results'] = {'message': '无详细结果数据'}
            else:
                export_data['detailed_results'] = {'message': '无详细结果数据'}

        if self.include_raw_data.isChecked():
            current_data = self.app_data.filtered_data if self.app_data.filter_applied else self.app_data.imported_data
            if current_data is not None:
                export_data['raw_data'] = current_data.to_dict('records')
            else:
                export_data['raw_data'] = {'message': '无原始数据'}

        if self.include_recommendations.isChecked():
            # 生成基本建议
            export_data['recommendations'] = [
                "充电分析已完成",
                "请检查计算电流和使用电压列",
                "建议根据实际需求调整充电参数"
            ]

        return export_data

    def export_to_excel(self, file_path, export_data):
        """导出到Excel"""
        import pandas as pd

        with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
            # 摘要工作表
            if 'summary' in export_data and export_data['summary']:
                if isinstance(export_data['summary'], dict):
                    summary_df = pd.DataFrame([export_data['summary']])
                else:
                    summary_df = pd.DataFrame({'摘要': [str(export_data['summary'])]})
                summary_df.to_excel(writer, sheet_name='分析摘要', index=False)

            # 详细结果工作表
            if 'detailed_results' in export_data and export_data['detailed_results']:
                try:
                    if isinstance(export_data['detailed_results'], list):
                        # 如果是记录列表，直接转换为DataFrame
                        details_df = pd.DataFrame(export_data['detailed_results'])
                    elif isinstance(export_data['detailed_results'], dict):
                        # 如果是字典，安全地检查是否是查表方式结果
                        if (export_data['detailed_results'] and
                            all(isinstance(v, (int, float)) for v in export_data['detailed_results'].values())):
                            details_df = pd.DataFrame([
                                {'查表方式': k, '计算值': v, '说明': self.get_method_description(k)}
                                for k, v in export_data['detailed_results'].items()
                            ])
                        else:
                            details_df = pd.DataFrame([export_data['detailed_results']])
                    else:
                        details_df = pd.DataFrame({'详细结果': [str(export_data['detailed_results'])]})
                    details_df.to_excel(writer, sheet_name='详细结果', index=False)
                except Exception as e:
                    # 如果处理详细结果时出错，创建错误信息DataFrame
                    error_df = pd.DataFrame([{
                        '错误': '详细结果处理错误',
                        '信息': str(e),
                        '说明': '处理详细结果时发生错误'
                    }])
                    error_df.to_excel(writer, sheet_name='详细结果', index=False)

            # 原始数据工作表
            if 'raw_data' in export_data and export_data['raw_data']:
                if isinstance(export_data['raw_data'], list):
                    raw_df = pd.DataFrame(export_data['raw_data'])
                else:
                    raw_df = pd.DataFrame({'原始数据': [str(export_data['raw_data'])]})
                raw_df.to_excel(writer, sheet_name='原始数据', index=False)

            # 建议工作表
            if 'recommendations' in export_data and export_data['recommendations']:
                if isinstance(export_data['recommendations'], list):
                    rec_df = pd.DataFrame({'建议': export_data['recommendations']})
                else:
                    rec_df = pd.DataFrame({'建议': [str(export_data['recommendations'])]})
                rec_df.to_excel(writer, sheet_name='分析建议', index=False)

    def export_to_csv(self, file_path, export_data):
        """导出到CSV"""
        import pandas as pd

        # 合并所有数据到一个DataFrame
        combined_data = []

        # 添加摘要信息
        if 'summary' in export_data and export_data['summary']:
            if isinstance(export_data['summary'], dict):
                for key, value in export_data['summary'].items():
                    combined_data.append({
                        '类型': '摘要',
                        '项目': key,
                        '值': str(value),
                        '说明': '分析摘要信息'
                    })

        # 添加详细结果
        if 'detailed_results' in export_data and export_data['detailed_results']:
            if isinstance(export_data['detailed_results'], list):
                # 如果是记录列表，添加每条记录
                for i, record in enumerate(export_data['detailed_results']):
                    if isinstance(record, dict):
                        for key, value in record.items():
                            combined_data.append({
                                '类型': '详细结果',
                                '项目': f"记录{i+1}_{key}",
                                '值': str(value),
                                '说明': '分析详细结果'
                            })
            elif isinstance(export_data['detailed_results'], dict):
                # 如果是字典，安全地处理
                try:
                    # 检查是否是查表方式结果（所有值都是数字）
                    if (export_data['detailed_results'] and
                        all(isinstance(v, (int, float)) for v in export_data['detailed_results'].values())):
                        for method, value in export_data['detailed_results'].items():
                            combined_data.append({
                                '类型': '查表结果',
                                '项目': method,
                                '值': str(value),
                                '说明': self.get_method_description(method)
                            })
                    else:
                        # 普通字典结果
                        for key, value in export_data['detailed_results'].items():
                            combined_data.append({
                                '类型': '详细结果',
                                '项目': key,
                                '值': str(value),
                                '说明': '分析详细结果'
                            })
                except Exception as e:
                    # 如果处理详细结果时出错，添加错误信息
                    combined_data.append({
                        '类型': '错误',
                        '项目': '详细结果处理错误',
                        '值': str(e),
                        '说明': '处理详细结果时发生错误'
                    })

        # 添加建议
        if 'recommendations' in export_data and export_data['recommendations']:
            if isinstance(export_data['recommendations'], list):
                for i, rec in enumerate(export_data['recommendations']):
                    combined_data.append({
                        '类型': '建议',
                        '项目': f"建议{i+1}",
                        '值': str(rec),
                        '说明': '分析建议'
                    })

        # 如果没有数据，添加一个默认行
        if not combined_data:
            combined_data.append({
                '类型': '信息',
                '项目': '导出状态',
                '值': '无可导出数据',
                '说明': '分析结果为空'
            })

        df = pd.DataFrame(combined_data)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')

    def export_to_json(self, file_path, export_data):
        """导出到JSON"""
        import json

        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=4, ensure_ascii=False)

    def setup_styles(self):
        """设置对话框样式"""
        style = """
        QDialog {
            background-color: #f5f5f5;
        }
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e0e0e0;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: white;
            border-bottom: 2px solid #2196F3;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QTableWidget {
            gridline-color: #d0d0d0;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }
        QTableWidget::item:selected {
            background-color: #3daee9;
            color: white;
        }
        """
        self.setStyleSheet(style)
