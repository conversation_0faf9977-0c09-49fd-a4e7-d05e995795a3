#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试充电分析导出功能
"""

import sys
import os
import pandas as pd
from PyQt6.QtWidgets import QApplication

# 添加当前目录到路径
sys.path.append('.')

from charging_analysis_pyqt import ChargingAnalysisDialog
from main_pyqt import AppData

def test_export():
    """测试导出功能"""
    print("开始测试导出功能...")
    
    # 创建QApplication（GUI测试需要）
    app = QApplication(sys.argv)
    
    try:
        # 创建测试数据
        app_data = AppData()
        app_data.imported_data = pd.DataFrame({
            'time': range(10),
            'temp': [20, 21, 22, 23, 24, 25, 26, 27, 28, 29],
            'voltage': [3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9],
            '计算电流': [1.0, 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 1.7, 1.8, 1.9],
            '使用电压': [3.0, 3.1, 3.2, 3.3, 3.4, 3.5, 3.6, 3.7, 3.8, 3.9]
        })
        
        # 创建对话框实例
        dialog = ChargingAnalysisDialog(None, app_data)
        
        # 模拟分析结果
        dialog.analysis_results = {
            'analysis_results': app_data.imported_data,
            'summary': {
                'total_rows': 10,
                'analysis_method': 'charging_analysis.py算法',
                'timestamp': '2024-01-01T00:00:00'
            }
        }
        
        # 设置导出选项
        dialog.include_summary.setChecked(True)
        dialog.include_details.setChecked(True)
        dialog.include_raw_data.setChecked(True)
        dialog.include_recommendations.setChecked(True)
        
        # 测试导出数据准备
        print("测试导出数据准备...")
        export_data = dialog.prepare_export_data()
        print(f"导出数据准备成功，包含键: {list(export_data.keys())}")
        
        # 测试CSV导出
        print("测试CSV导出...")
        csv_file = "test_export.csv"
        dialog.export_to_csv(csv_file, export_data)
        print(f"CSV导出成功: {csv_file}")
        
        # 测试Excel导出
        print("测试Excel导出...")
        xlsx_file = "test_export.xlsx"
        dialog.export_to_excel(xlsx_file, export_data)
        print(f"Excel导出成功: {xlsx_file}")
        
        # 测试JSON导出
        print("测试JSON导出...")
        json_file = "test_export.json"
        dialog.export_to_json(json_file, export_data)
        print(f"JSON导出成功: {json_file}")
        
        print("所有导出测试通过！")
        
        # 清理测试文件
        for file in [csv_file, xlsx_file, json_file]:
            if os.path.exists(file):
                os.remove(file)
                print(f"清理测试文件: {file}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        app.quit()

if __name__ == "__main__":
    test_export()
