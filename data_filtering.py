# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import json
import os

class FilteringWindow(tk.Toplevel):
    """数据筛选窗口 - 用于对导入的数据进行信号筛选"""

    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.title("数据筛选")
        self.geometry("800x600")
        self.transient(parent)
        self.grab_set()

        # 数据相关属性
        self.dfs = []  # 存储所有加载的DataFrame
        self.file_paths = []  # 存储对应的文件路径
        self.original_columns = []  # 所有列名的合并列表
        self.checkbox_vars = {}  # 复选框状态字典 {列名: IntVar}
        self.checkbox_widgets = []  # 复选框组件列表
        self.filter = lambda col: True  # 当前筛选条件

        self.setup_ui()
        self.load_data()

    def setup_ui(self):
        """设置用户界面"""
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        # 顶部控制区域
        control_frame = ttk.Frame(main_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 搜索框
        search_frame = ttk.Frame(control_frame)
        search_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(search_frame, text="搜索信号:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_entry = ttk.Entry(search_frame, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', lambda event: self.search_function())

        # 筛选按钮
        filter_buttons_frame = ttk.Frame(control_frame)
        filter_buttons_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(filter_buttons_frame, text="显示所有", command=self.show_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(filter_buttons_frame, text="只显示已选", command=self.show_selected_only).pack(side=tk.LEFT, padx=(0, 5))

        # 配置管理按钮
        config_frame = ttk.Frame(control_frame)
        config_frame.pack(fill=tk.X, pady=(5, 0))
        ttk.Button(config_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_frame, text="加载配置", command=self.load_config).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(config_frame, text="保存筛选后的数据", command=self.save_data).pack(side=tk.LEFT, padx=(0, 5))

        # 返回按钮
        ttk.Button(config_frame, text="返回主页", command=self.destroy).pack(side=tk.RIGHT)

        # 滚动区域用于显示复选框
        self.setup_scrollable_area(main_frame)

    def setup_scrollable_area(self, parent):
        """设置可滚动的复选框区域"""
        # 创建Canvas和滚动条
        canvas_frame = ttk.Frame(parent)
        canvas_frame.pack(expand=True, fill=tk.BOTH)

        self.canvas = tk.Canvas(canvas_frame)
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        self.scroll_area = ttk.Frame(self.canvas)

        # 配置滚动
        self.scroll_area.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )
        self.canvas.create_window((0, 0), window=self.scroll_area, anchor='nw')
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定鼠标滚轮事件
        self.canvas.bind("<MouseWheel>", self.on_mousewheel)

    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        self.canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")

    def load_data(self):
        """加载数据并创建复选框"""
        try:
            # 从app_data获取导入的数据
            if self.app_data.imported_data is not None:
                self.dfs = [self.app_data.imported_data]
                self.file_paths = [self.app_data.imported_filepath or "导入的数据"]

                # 获取所有列名
                self.original_columns = list(self.app_data.imported_data.columns)

                # 创建复选框
                self.create_checkboxes()

                messagebox.showinfo("数据加载", f"已加载数据，共 {len(self.original_columns)} 个信号列。", parent=self)
            else:
                messagebox.showwarning("无数据", "没有找到已导入的数据。请先在主界面导入数据。", parent=self)

        except Exception as e:
            messagebox.showerror("加载错误", f"加载数据时发生错误: {e}", parent=self)

    def create_checkboxes(self):
        """为所有列创建复选框"""
        # 清空现有的复选框
        for widget in self.checkbox_widgets:
            widget.destroy()
        self.checkbox_widgets.clear()
        self.checkbox_vars.clear()

        # 为每个列创建复选框
        for col in self.original_columns:
            var = tk.IntVar(value=0)  # 默认未选中
            self.checkbox_vars[col] = var

            cb = ttk.Checkbutton(self.scroll_area, text=col, variable=var)
            self.checkbox_widgets.append(cb)

        # 初始显示所有复选框
        self.filter_checkboxes()

    def search_function(self):
        """根据关键词筛选信号"""
        keyword = self.search_entry.get().lower()
        if keyword:
            self.filter = lambda col: keyword in col.lower()
        else:
            self.filter = lambda col: True
        self.filter_checkboxes()

    def show_selected_only(self):
        """只显示已勾选的信号"""
        self.filter = lambda col: self.checkbox_vars[col].get() == 1
        self.filter_checkboxes()

    def show_all(self):
        """显示所有信号"""
        self.filter = lambda col: True
        self.filter_checkboxes()

    def filter_checkboxes(self):
        """根据当前筛选条件更新复选框显示"""
        # 隐藏所有复选框
        for cb in self.checkbox_widgets:
            cb.grid_remove()

        # 应用筛选条件并显示符合条件的复选框
        filtered_columns = [col for col in self.original_columns if self.filter(col)]

        for idx, col in enumerate(filtered_columns):
            # 找到对应的复选框
            cb_index = self.original_columns.index(col)
            cb = self.checkbox_widgets[cb_index]
            cb.grid(row=idx, column=0, sticky='w', padx=5, pady=1)

        # 更新滚动区域
        self.canvas.configure(scrollregion=self.canvas.bbox("all"))

    def save_config(self):
        """保存当前选择的信号配置到JSON文件"""
        selected_columns = [col for col in self.original_columns if self.checkbox_vars[col].get()]

        if not selected_columns:
            messagebox.showwarning("无选择", "没有选择任何信号列。", parent=self)
            return

        filepath = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON 配置文件", "*.json"), ("所有文件", "*.*")],
            title="保存筛选配置",
            initialfile="filtering_config.json",
            parent=self
        )

        if not filepath:
            return

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(selected_columns, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("保存成功", f"筛选配置已保存到:\n{filepath}", parent=self)
        except Exception as e:
            messagebox.showerror("保存失败", f"无法保存配置文件: {e}", parent=self)

    def load_config(self):
        """从JSON文件加载筛选配置"""
        filepath = filedialog.askopenfilename(
            filetypes=[("JSON 配置文件", "*.json"), ("所有文件", "*.*")],
            title="加载筛选配置",
            parent=self
        )

        if not filepath:
            return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                selected_columns = json.load(f)

            # 重置所有复选框为未选中
            for col in self.original_columns:
                self.checkbox_vars[col].set(0)

            # 设置配置文件中的列为选中
            loaded_count = 0
            for col in selected_columns:
                if col in self.checkbox_vars:
                    self.checkbox_vars[col].set(1)
                    loaded_count += 1

            messagebox.showinfo("加载成功",
                              f"筛选配置已加载。\n成功加载 {loaded_count} 个信号列。",
                              parent=self)

        except Exception as e:
            messagebox.showerror("加载失败", f"无法加载配置文件: {e}", parent=self)

    def save_data(self):
        """保存筛选后的数据文件"""
        selected_columns = [col for col in self.original_columns if self.checkbox_vars[col].get()]

        if not selected_columns:
            messagebox.showwarning("无选择", "没有选择任何信号列。", parent=self)
            return

        if not self.dfs:
            messagebox.showwarning("无数据", "没有可保存的数据。", parent=self)
            return

        try:
            saved_files = []
            for i, (df, original_path) in enumerate(zip(self.dfs, self.file_paths)):
                # 生成默认文件名
                if original_path and original_path != "导入的数据":
                    base_name = os.path.splitext(os.path.basename(original_path))[0]
                    default_filename = f"{base_name}_filtered.csv"
                    initial_dir = os.path.dirname(original_path)
                else:
                    default_filename = f"filtered_data_{i+1}.csv"
                    initial_dir = os.getcwd()

                # 让用户选择保存位置
                new_path = filedialog.asksaveasfilename(
                    defaultextension=".csv",
                    filetypes=[("CSV 文件", "*.csv"), ("所有文件", "*.*")],
                    title=f"保存筛选后的数据 ({i+1}/{len(self.dfs)})",
                    initialfile=default_filename,
                    initialdir=initial_dir,
                    parent=self
                )

                if not new_path:
                    continue

                # 筛选并保存数据
                filtered_df = df[selected_columns]
                filtered_df.to_csv(new_path, index=False, encoding='utf_8_sig')
                saved_files.append(new_path)

            if saved_files:
                files_list = '\n'.join([os.path.basename(f) for f in saved_files])
                messagebox.showinfo("保存成功",
                                  f"筛选后的数据已保存到:\n{files_list}",
                                  parent=self)
            else:
                messagebox.showinfo("取消保存", "用户取消了数据保存操作。", parent=self)

        except Exception as e:
            messagebox.showerror("保存失败", f"保存数据时发生错误: {e}", parent=self)


# 独立测试代码
if __name__ == '__main__':
    class MockAppData:
        def __init__(self):
            import pandas as pd
            # 创建模拟数据
            self.imported_data = pd.DataFrame({
                'Timestamp': ['2023-01-01 10:00:00', '2023-01-01 10:00:01', '2023-01-01 10:00:02'],
                'Sensor1_Temp': [25.1, 25.2, 25.3],
                'Sensor2_Temp': [26.1, 26.2, 26.3],
                'Sensor3_Temp': [24.1, 24.2, 24.3],
                'Voltage_Cell1': [3.2, 3.21, 3.22],
                'Voltage_Cell2': [3.19, 3.20, 3.21],
                'Current_Battery': [10.5, 10.6, 10.7],
                'SOC_Display': [80.0, 80.1, 80.2],
                'BMS_Status': [1, 1, 1],
                'Notes': ['A', 'B', 'C']
            })
            self.imported_filepath = "test_data.csv"
            self.data_mapping_config = {}
            self.filtered_data_config = {}

    class MockParent(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title("Mock Parent - 数据筛选测试")
            self.geometry("300x200")
            self.app_data = MockAppData()

            ttk.Label(self, text="数据筛选模块测试").pack(pady=20)
            ttk.Button(self, text="打开数据筛选", command=self.open_filtering).pack(pady=10)

        def open_filtering(self):
            FilteringWindow(self, self.app_data)

        def update_info_display(self, message):
            print(f"MockParent Info: {message}")

    app = MockParent()
    app.mainloop()