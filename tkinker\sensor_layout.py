# -*- coding: utf-8 -*-


import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pandas as pd
import numpy as np
import os
from PIL import Image, ImageTk
import json
import re

class SensorLayoutWindow(tk.Toplevel):
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.title("温感布置分析")
        self.geometry("1000x750")

        self.transient(parent)
        self.grab_set()

        # --- 初始化变量 ---
        self.original_pil_image = None
        self.display_tk_image = None
        self.image_path = None

        # Robustly initialize df_imported and data_mapping_config from app_data
        self.df_imported = getattr(self.app_data, 'imported_data', None)
        self.data_mapping_config = getattr(self.app_data, 'data_mapping_config', {})

        self.temp_points = {}
        self.zoom_factor = 1.0
        self.pan_offset_x, self.pan_offset_y = 0, 0
        self.last_pan_x, self.last_pan_y = 0, 0
        self.display_mode = "delta_t"  # 'delta_t', 'max_val', 'min_val', 'slider'
        self.current_time_index = 0
        self.selected_point_name_in_tree = None

        # --- 构建UI ---
        self._create_widgets()

        # --- 初始化状态 ---
        self.update_time_slider_config() # ensure this is called after df_imported is set
        self.redraw_canvas()
        if self.df_imported is None:
            if self.winfo_exists(): messagebox.showwarning("无数据", "主数据未导入。部分功能可能受限。", parent=self)

        # 绑定窗口关闭事件
        self.protocol("WM_DELETE_WINDOW", self.custom_destroy_sensor_layout)

    def _create_widgets(self):
        # --- 工具栏 ---
        toolbar_frame = ttk.Frame(self, padding=5)
        toolbar_frame.pack(fill=tk.X)

        ttk.Button(toolbar_frame, text="导入图片", command=self.import_image_ui).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="删除选中点", command=self.delete_selected_point_ui).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(toolbar_frame, text="显示温差", command=lambda: self.set_summary_display_mode_ui("delta_t")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="最大值", command=lambda: self.set_summary_display_mode_ui("max_val")).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="最小值", command=lambda: self.set_summary_display_mode_ui("min_val")).pack(side=tk.LEFT, padx=2)
        ttk.Separator(toolbar_frame, orient=tk.VERTICAL).pack(side=tk.LEFT, padx=5, fill=tk.Y)

        ttk.Button(toolbar_frame, text="保存项目", command=self.save_project_ui).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="加载项目", command=self.load_project_ui).pack(side=tk.LEFT, padx=2)
        ttk.Button(toolbar_frame, text="返回主页", command=self.custom_destroy_sensor_layout).pack(side=tk.RIGHT, padx=2)

        main_content_frame = ttk.Frame(self, padding=5)
        main_content_frame.pack(expand=True, fill=tk.BOTH)

        self.image_canvas = tk.Canvas(main_content_frame, bg="lightgrey", cursor="crosshair")
        self.image_canvas.pack(side=tk.LEFT, expand=True, fill=tk.BOTH, padx=(0,5))
        self.image_canvas.bind("<ButtonPress-1>", self.on_canvas_click_add_point)
        self.image_canvas.bind("<ButtonPress-2>", self.start_pan)
        self.image_canvas.bind("<B2-Motion>", self.pan_image)
        self.image_canvas.bind("<MouseWheel>", self.zoom_image_event)
        self.image_canvas.bind("<Button-4>", self.zoom_image_event_linux)
        self.image_canvas.bind("<Button-5>", self.zoom_image_event_linux)

        tree_frame = ttk.Frame(main_content_frame, width=300)
        tree_frame.pack(side=tk.RIGHT, fill=tk.Y)
        tree_frame.pack_propagate(False)

        self.tree = ttk.Treeview(tree_frame, columns=("name", "value", "mapped_col"), show="headings", height=15)
        self.tree.heading("name", text="温度点")
        self.tree.column("name", width=80, anchor=tk.W, stretch=tk.NO)
        self.tree.heading("value", text="值")
        self.tree.column("value", width=60, anchor=tk.CENTER, stretch=tk.NO)
        self.tree.heading("mapped_col", text="映射CSV列")
        self.tree.column("mapped_col", width=140, anchor=tk.W, stretch=tk.YES)
        self.tree.pack(expand=True, fill=tk.BOTH)
        self.tree.bind("<<TreeviewSelect>>", self.on_tree_select)

        slider_frame = ttk.Frame(self, padding=5)
        slider_frame.pack(fill=tk.X)
        ttk.Label(slider_frame, text="时间点:").pack(side=tk.LEFT, padx=5)
        self.time_slider_var = tk.DoubleVar()
        self.time_slider = ttk.Scale(slider_frame, from_=0, to=100, orient=tk.HORIZONTAL, variable=self.time_slider_var, command=self.on_time_slider_change_ui)
        self.time_slider.pack(side=tk.LEFT, expand=True, fill=tk.X, padx=5)
        self.time_label_var = tk.StringVar(value="时间: N/A")
        ttk.Label(slider_frame, textvariable=self.time_label_var).pack(side=tk.LEFT, padx=5)

    def update_time_slider_config(self):
        max_val = 0 # Default to 0 if no data
        if self.df_imported is not None and not self.df_imported.empty:
            max_val = len(self.df_imported) - 1

        self.time_slider.config(to=max_val if max_val > 0 else 0) # Ensure 'to' is not negative
        self.current_time_index = min(self.current_time_index, max_val if max_val >=0 else 0)
        self.time_slider_var.set(self.current_time_index)
        # Call on_time_slider_change_ui without forcing mode update initially if not needed
        self.on_time_slider_change_ui(str(self.current_time_index), force_mode_update=False)


    def import_image_ui(self):
        file_types = [("图片文件", "*.jpg *.jpeg *.png *.bmp *.gif"), ("所有文件", "*.*")]
        fp = filedialog.askopenfilename(title="选择背景图片", filetypes=file_types, parent=self)
        if fp:
            try:
                self.original_pil_image = Image.open(fp)
                self.image_path = fp
                self.zoom_factor = 1.0
                self.pan_offset_x = 0
                self.pan_offset_y = 0
                self.redraw_canvas()
            except Exception as e:
                if self.winfo_exists(): messagebox.showerror("图片加载失败", f"无法加载图片: {e}", parent=self)
                self.original_pil_image = None
                self.image_path = None

    def on_canvas_click_add_point(self, event):
        if not self.original_pil_image:
            if self.winfo_exists(): messagebox.showwarning("无图片", "请先导入一张背景图片。", parent=self)
            return

        img_x = (event.x - self.pan_offset_x) / self.zoom_factor
        img_y = (event.y - self.pan_offset_y) / self.zoom_factor

        if not (0 <= img_x <= self.original_pil_image.width and 0 <= img_y <= self.original_pil_image.height):
            return

        p_name = simpledialog.askstring("设置测温点名称", "请输入测温点名称 (对应映射配置中的逻辑名):", initialvalue=f"P{len(self.temp_points)+1}", parent=self)
        if p_name and p_name not in self.temp_points:
            mapped_col = None
            series = None

            # Try to find CSV column from global mapping using p_name as logical_name
            if self.data_mapping_config and isinstance(self.data_mapping_config.get('mappings'), dict):
                # Need to invert the mapping: {csv_col: logical_name} to {logical_name: csv_col}
                reverse_mappings = {v: k for k, v in self.data_mapping_config['mappings'].items()}
                mapped_col = reverse_mappings.get(p_name)

            if mapped_col and self.df_imported is not None and mapped_col in self.df_imported.columns:
                series = self.df_imported[mapped_col]
                if self.winfo_exists(): messagebox.showinfo("映射成功", f"测温点 '{p_name}' 已自动映射到CSV列 '{mapped_col}' (来自全局配置)。", parent=self)
            elif self.df_imported is not None:
                if self.winfo_exists():
                    user_choice = messagebox.askyesno("映射提示", f"在全局配置中未找到 '{p_name}' 的映射。\n是否手动为此点选择CSV列？", parent=self)
                    if user_choice:
                        cols = [""] + self.df_imported.columns.tolist()
                        manual_mapped_col = self.ask_csv_column_mapping(cols, p_name)
                        if manual_mapped_col and manual_mapped_col in self.df_imported:
                            mapped_col = manual_mapped_col # Override with manual choice
                            series = self.df_imported[mapped_col]
            else: # No df_imported
                 if self.winfo_exists(): messagebox.showwarning("无数据", "无法映射测温点，主数据未导入。", parent=self)


            self.temp_points[p_name] = {
                "coords": (img_x, img_y),
                "mapped_csv_col": mapped_col, # This is the actual CSV column name
                "logical_name": p_name,      # Store the logical name used for mapping
                "display_value": np.nan,
                "raw_series": series,
                "canvas_item_id": None,
                "text_item_id": None
            }
            self.update_treeview()
            self.calculate_and_redraw_based_on_mode()
        elif p_name:
            if self.winfo_exists(): messagebox.showwarning("名称已存在", f"测温点 '{p_name}' 已存在。", parent=self)


    def ask_csv_column_mapping(self, available_cols, point_name_for_title):
        dialog = tk.Toplevel(self)
        dialog.title(f"手动映射点 '{point_name_for_title}'")
        dialog.geometry("400x150")
        dialog.resizable(False, False)
        dialog.transient(self)
        dialog.grab_set()

        ttk.Label(dialog, text=f"为 '{point_name_for_title}' 选择一个CSV列:").pack(pady=10, padx=10)

        var = tk.StringVar()
        combo = ttk.Combobox(dialog, textvariable=var, values=available_cols, state="readonly", width=38)
        combo.pack(pady=5, padx=10)
        if available_cols: combo.set(available_cols[0] if available_cols[0] else (available_cols[1] if len(available_cols)>1 else ""))

        result = {"col": None}
        def on_ok():
            result["col"] = var.get() if var.get() else None
            dialog.destroy()
        def on_cancel():
            dialog.destroy()

        btn_frame = ttk.Frame(dialog)
        btn_frame.pack(pady=10)
        ttk.Button(btn_frame, text="确定", command=on_ok).pack(side=tk.LEFT, padx=10)
        ttk.Button(btn_frame, text="取消", command=on_cancel).pack(side=tk.LEFT, padx=10)

        self.wait_window(dialog)
        return result["col"]

    def redraw_canvas(self):
        if not self.winfo_exists() or not hasattr(self, 'image_canvas'): return
        self.image_canvas.delete("all")
        if not self.original_pil_image:
            canvas_w = self.image_canvas.winfo_width()
            canvas_h = self.image_canvas.winfo_height()
            if canvas_w > 0 and canvas_h > 0:
                 self.image_canvas.create_text(canvas_w/2, canvas_h/2, text="请通过“导入图片”按钮加载一张背景图", fill="grey")
            return

        w = int(self.original_pil_image.width * self.zoom_factor)
        h = int(self.original_pil_image.height * self.zoom_factor)
        if w <= 0 or h <= 0: return

        resample_method = Image.Resampling.NEAREST if self.zoom_factor < 0.5 or self.zoom_factor > 2.0 else Image.Resampling.BILINEAR
        img_resized = self.original_pil_image.resize((w, h), resample_method)
        self.display_tk_image = ImageTk.PhotoImage(img_resized)

        self.image_canvas.create_image(self.pan_offset_x, self.pan_offset_y, anchor=tk.NW, image=self.display_tk_image)
        min_v, max_v = self.get_overall_min_max_display_values()
        for name, data in self.temp_points.items():
            self.draw_single_point_on_canvas(name, data, min_v, max_v)

    def draw_single_point_on_canvas(self, name, data, overall_min, overall_max):
        if not self.winfo_exists() or not hasattr(self, 'image_canvas'): return
        img_x, img_y = data["coords"]
        canvas_x = self.pan_offset_x + (img_x * self.zoom_factor)
        canvas_y = self.pan_offset_y + (img_y * self.zoom_factor)

        radius = max(3, 5 * self.zoom_factor**0.5)
        text_offset = radius + 2
        val = data.get("display_value", np.nan)
        color = self.get_color_for_value(val, overall_min, overall_max)

        if data.get("canvas_item_id"): self.image_canvas.delete(data["canvas_item_id"])
        if data.get("text_item_id"): self.image_canvas.delete(data["text_item_id"])

        item_id = self.image_canvas.create_rectangle(
            canvas_x - radius, canvas_y - radius, canvas_x + radius, canvas_y + radius,
            fill=color, outline="black", width=max(1, 1.5 * self.zoom_factor**0.2), tags=("tp", name)
        )
        text_content = f"{data.get('logical_name', name)}\n{val:.1f}°C" if pd.notnull(val) else data.get('logical_name', name)
        font_size = max(6, int(7 * self.zoom_factor**0.4))
        text_id = self.image_canvas.create_text(
            canvas_x, canvas_y + text_offset, text=text_content, fill="black",
            anchor=tk.N, justify=tk.CENTER, font=("Arial", font_size), tags=("tp_txt", name)
        )
        data["canvas_item_id"] = item_id
        data["text_item_id"] = text_id

    def get_overall_min_max_display_values(self):
        valid_values = [p["display_value"] for p in self.temp_points.values() if pd.notnull(p.get("display_value"))]
        return (min(valid_values), max(valid_values)) if valid_values else (np.nan, np.nan)

    def get_color_for_value(self, val, min_v, max_v):
        if pd.isna(val) or pd.isna(min_v) or pd.isna(max_v) : return "#AAAAAA"
        if min_v == max_v: return "#00FF00"
        norm = (val - min_v) / (max_v - min_v) if (max_v - min_v) != 0 else 0.5
        norm = max(0, min(1, norm))
        r, g, b = (0, int(2 * norm * 255), int((1 - 2 * norm) * 255)) if norm < 0.5 else \
                  (int(2 * (norm - 0.5) * 255), int(2 * (1 - norm) * 255), 0)
        return f'#{r:02x}{g:02x}{b:02x}'

    def update_treeview(self):
        if not self.winfo_exists() or not hasattr(self, 'tree'): return
        selected_iid = self.tree.focus()
        self.tree.delete(*self.tree.get_children())
        for name, data in self.temp_points.items(): # name is logical_name here
            val_str = f"{data.get('display_value', np.nan):.2f}" if pd.notnull(data.get('display_value')) else "N/A"
            col_str = data.get('mapped_csv_col', "未映射")
            self.tree.insert("", "end", values=(data.get('logical_name', name), val_str, col_str), iid=name) # iid is logical_name

        if selected_iid and self.tree.exists(selected_iid):
            self.tree.focus(selected_iid)
            self.tree.selection_set(selected_iid)

    def on_tree_select(self, event):
        if not self.winfo_exists() or not hasattr(self, 'tree'): return
        selected_items = self.tree.selection()
        self.selected_point_name_in_tree = selected_items[0] if selected_items else None # This is logical_name

    def delete_selected_point_ui(self):
        if not self.selected_point_name_in_tree or self.selected_point_name_in_tree not in self.temp_points:
            if self.winfo_exists(): messagebox.showwarning("无选中项", "请先从右侧列表中选择一个测温点进行删除。", parent=self)
            return

        name_to_delete = self.selected_point_name_in_tree # This is the logical_name
        if self.winfo_exists() and messagebox.askyesno("确认删除", f"您确定要删除测温点 '{name_to_delete}' 吗?", parent=self):
            point_data = self.temp_points[name_to_delete]
            if point_data.get("canvas_item_id"): self.image_canvas.delete(point_data["canvas_item_id"])
            if point_data.get("text_item_id"): self.image_canvas.delete(point_data["text_item_id"])
            del self.temp_points[name_to_delete]
            self.selected_point_name_in_tree = None
            self.update_treeview()

    def calculate_and_redraw_based_on_mode(self):
        if self.display_mode == "slider":
            self.on_time_slider_change_ui(str(self.time_slider_var.get()), force_mode_update=False)
            return

        for name, data in self.temp_points.items():
            series = data.get("raw_series")
            value = np.nan
            if series is not None and not series.empty:
                numeric_series = pd.to_numeric(series, errors='coerce').dropna()
                if not numeric_series.empty:
                    if self.display_mode == "delta_t":
                        value = numeric_series.iloc[-1] - numeric_series.iloc[0] if len(numeric_series) >= 2 else np.nan
                    elif self.display_mode == "max_val": value = numeric_series.max()
                    elif self.display_mode == "min_val": value = numeric_series.min()
            data["display_value"] = value
        self.update_treeview()
        self.redraw_canvas()

    def set_summary_display_mode_ui(self, mode):
        self.display_mode = mode
        self.calculate_and_redraw_based_on_mode()

    def on_time_slider_change_ui(self, value_str, force_mode_update=True):
        if not self.winfo_exists(): return
        try:
            self.current_time_index = int(float(value_str))
        except ValueError:
            self.current_time_index = 0 # Default to 0 if conversion fails

        if force_mode_update: self.display_mode = "slider"

        time_label_text = f"索引: {self.current_time_index}"
        # Refresh df_imported and data_mapping_config before using them
        self.df_imported = getattr(self.app_data, 'imported_data', None)
        self.data_mapping_config = getattr(self.app_data, 'data_mapping_config', {})

        if self.df_imported is not None and not self.df_imported.empty:
            try:
                time_col_logical_name = self.data_mapping_config.get("time_column") # This is CSV column name
                if time_col_logical_name and time_col_logical_name in self.df_imported.columns:
                    if self.current_time_index < len(self.df_imported):
                        time_val = self.df_imported[time_col_logical_name].iloc[self.current_time_index]
                        time_label_text = f"{time_val} (索引: {self.current_time_index})"
            except Exception as e:
                 print(f"Error accessing time column for slider: {e}") # Log error
                 pass
        self.time_label_var.set(f"时间点: {time_label_text}")

        for name, data in self.temp_points.items():
            series = data.get("raw_series") # This series is directly from df_imported[mapped_csv_col]
            value = np.nan
            if series is not None and not series.empty and self.current_time_index < len(series):
                value = pd.to_numeric(series.iloc[self.current_time_index], errors='coerce')
            data["display_value"] = value

        self.update_treeview()
        self.redraw_canvas()

    def start_pan(self, event): self.last_pan_x, self.last_pan_y = event.x, event.y
    def pan_image(self, event):
        if self.original_pil_image:
            dx, dy = event.x - self.last_pan_x, event.y - self.last_pan_y
            self.pan_offset_x += dx; self.pan_offset_y += dy
            self.last_pan_x, self.last_pan_y = event.x, event.y
            self.redraw_canvas()
    def zoom_image_event_linux(self, event): self.zoom_image_logic(event.x, event.y, 1.1 if event.num == 4 else 1/1.1)
    def zoom_image_event(self, event): self.zoom_image_logic(event.x, event.y, 1.1 if event.delta > 0 else 1/1.1)
    def zoom_image_logic(self, mouse_x, mouse_y, factor):
        if not self.original_pil_image: return
        old_zoom = self.zoom_factor
        self.zoom_factor = max(0.1, min(self.zoom_factor * factor, 5.0))
        self.pan_offset_x = mouse_x - (mouse_x - self.pan_offset_x) * (self.zoom_factor / old_zoom)
        self.pan_offset_y = mouse_y - (mouse_y - self.pan_offset_y) * (self.zoom_factor / old_zoom)
        self.redraw_canvas()

    def save_project_ui(self):
        fp = filedialog.asksaveasfilename(defaultextension=".slt_proj.json", filetypes=[("温感布置项目", "*.slt_proj.json")], title="保存项目", parent=self)
        if not fp: return
        project_data = {
            "image_path": self.image_path,
            "csv_filepath": getattr(self.app_data, 'imported_filepath', None),
            "temp_points": {data.get("logical_name", name): {"coords": data["coords"],
                                                             "mapped_csv_col": data["mapped_csv_col"],
                                                             "logical_name": data.get("logical_name", name)}
                            for name, data in self.temp_points.items()}, # Save with logical_name as key
            "view_state": {"zoom_factor": self.zoom_factor, "pan_offset_x": self.pan_offset_x, "pan_offset_y": self.pan_offset_y,
                           "display_mode": self.display_mode, "current_time_index": self.current_time_index },
            "main_data_mapping_config_snapshot": getattr(self.app_data, 'data_mapping_config', {})
        }
        try:
            with open(fp, 'w', encoding='utf-8') as f: json.dump(project_data, f, indent=4)
            if self.winfo_exists(): messagebox.showinfo("成功", "项目已保存。", parent=self)
        except Exception as e:
            if self.winfo_exists(): messagebox.showerror("保存失败", f"无法保存项目: {e}", parent=self)

    def load_project_ui(self):
        fp = filedialog.askopenfilename(filetypes=[("温感布置项目", "*.slt_proj.json")], title="加载项目", parent=self)
        if not fp: return
        try:
            with open(fp, 'r', encoding='utf-8') as f: proj_data = json.load(f)

            self.image_path = proj_data.get("image_path")
            self.original_pil_image = None
            if self.image_path and os.path.exists(self.image_path): self.original_pil_image = Image.open(self.image_path)
            elif self.image_path and self.winfo_exists(): messagebox.showwarning("图片缺失", f"图片 '{self.image_path}' 未找到。", parent=self)

            proj_csv = proj_data.get("csv_filepath")
            # Always try to refresh df_imported and data_mapping_config from app_data first
            self.df_imported = getattr(self.app_data, 'imported_data', None)
            self.data_mapping_config = getattr(self.app_data, 'data_mapping_config', {})

            if proj_csv and os.path.exists(proj_csv):
                current_app_csv = getattr(self.app_data, 'imported_filepath', None)
                if current_app_csv != proj_csv:
                    if self.winfo_exists() and messagebox.askyesno("更新主数据?", f"项目关联数据 '{os.path.basename(proj_csv)}' 与当前主程序数据不同。\n是否加载项目数据到主程序？", parent=self):
                        try:
                            df_p = pd.read_csv(proj_csv)
                            df_p.columns = [re.sub(r'\[.*?\]', '', c).strip() for c in df_p.columns]
                            df_p.dropna(how='all', inplace=True); df_p.reset_index(drop=True, inplace=True)
                            self.app_data.imported_data = df_p
                            self.app_data.imported_filepath = proj_csv
                            # Load mapping snapshot from project if user agrees to load project's CSV
                            self.app_data.data_mapping_config = proj_data.get("main_data_mapping_config_snapshot", {})
                            if hasattr(self.parent, 'update_info_display'): self.parent.update_info_display(f"数据及映射已从项目 '{os.path.basename(fp)}' 加载: {os.path.basename(proj_csv)}")
                            # Update local references
                            self.df_imported = self.app_data.imported_data
                            self.data_mapping_config = self.app_data.data_mapping_config
                        except Exception as e:
                             if self.winfo_exists(): messagebox.showerror("主数据更新失败", f"加载项目CSV '{proj_csv}' 失败: {e}", parent=self)
            elif proj_csv and self.winfo_exists(): messagebox.showwarning("项目CSV缺失", f"项目数据文件 '{proj_csv}' 未找到。", parent=self)

            # If not loaded from project, ensure local self.df_imported and self.data_mapping_config are up-to-date with app_data
            if self.df_imported is None: self.df_imported = getattr(self.app_data, 'imported_data', None)
            if not self.data_mapping_config : self.data_mapping_config = getattr(self.app_data, 'data_mapping_config', {})


            self.temp_points.clear()
            for logical_name_key, data in proj_data.get("temp_points", {}).items():
                csv_col_for_point = data.get("mapped_csv_col") # CSV col name from project file
                point_logical_name = data.get("logical_name", logical_name_key) # Ensure logical name consistency

                series = None
                final_csv_col = csv_col_for_point # Default to project's mapped column

                # If global mapping exists, try to find the CSV column for this logical_name
                if self.data_mapping_config and isinstance(self.data_mapping_config.get('mappings'), dict):
                    reverse_mappings = {v: k for k, v in self.data_mapping_config['mappings'].items()}
                    globally_mapped_csv_col = reverse_mappings.get(point_logical_name)
                    if globally_mapped_csv_col:
                        final_csv_col = globally_mapped_csv_col # Prefer global mapping's CSV column

                if self.df_imported is not None and final_csv_col and final_csv_col in self.df_imported.columns:
                    series = self.df_imported[final_csv_col]
                elif self.df_imported is not None and csv_col_for_point and csv_col_for_point in self.df_imported.columns:
                    # Fallback to project's original mapped_csv_col if global one not found in current CSV
                    final_csv_col = csv_col_for_point
                    series = self.df_imported[final_csv_col]
                    if self.winfo_exists(): print(f"SensorLayout: Point '{point_logical_name}' using project's CSV column '{final_csv_col}' as global mapping was not found in current CSV.")

                self.temp_points[point_logical_name] = { # Use logical_name as key
                    "coords": tuple(data.get("coords", (0,0))),
                    "mapped_csv_col": final_csv_col,
                    "logical_name": point_logical_name,
                    "display_value": np.nan, "raw_series": series,
                    "canvas_item_id": None, "text_item_id": None
                }

            vs = proj_data.get("view_state", {})
            self.zoom_factor = vs.get("zoom_factor", 1.0)
            self.pan_offset_x = vs.get("pan_offset_x", 0); self.pan_offset_y = vs.get("pan_offset_y", 0)
            self.display_mode = vs.get("display_mode", "delta_t"); self.current_time_index = vs.get("current_time_index", 0)

            self.update_time_slider_config()
            self.calculate_and_redraw_based_on_mode() # This will call update_treeview and redraw_canvas
            if self.winfo_exists(): messagebox.showinfo("成功", "项目已加载。", parent=self)

        except Exception as e:
            if self.winfo_exists():
                # import traceback # Already imported
                # traceback.print_exc() # For debugging
                messagebox.showerror("加载失败", f"无法加载项目: {e}\n{traceback.format_exc()}", parent=self)


    def custom_destroy_sensor_layout(self):
        bindings = ["<ButtonPress-1>", "<ButtonPress-2>", "<B2-Motion>", "<MouseWheel>", "<Button-4>", "<Button-5>"]
        if hasattr(self, 'image_canvas') and self.image_canvas.winfo_exists():
            for b in bindings: self.image_canvas.unbind(b)
        super().destroy()
