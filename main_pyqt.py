#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化程序 - PyQt6版本主程序
基于PyQt6重新设计的现代化GUI界面
"""

import sys
import os
import pandas as pd
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
        QGridLayout, QPushButton, QLabel, QTableWidget, QTableWidgetItem,
        QFileDialog, QMessageBox, QProgressBar, QStatusBar, QMenuBar,
        QScrollArea, QFrame, QSplitter, QTabWidget, QTextEdit, QGroupBox
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QIcon, QPixmap, QAction
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6")
    sys.exit(1)


class AppData:
    """应用程序共享数据类"""
    def __init__(self):
        self.imported_data = None
        self.filtered_data = None  # 新增：筛选后的数据
        self.data_mapping_config = {} # Stores the entire mapping configuration, including time column
        self.filtered_data_config = {}
        self.imported_filepath = None # Singular file path for the main imported CSV
        self.current_filename = ""  # Derived from imported_filepath
        # self.file_paths = [] # Removed, as imported_filepath is used and file_paths was not used elsewhere.
        self.filter_applied = False  # 新增：是否应用了筛选
        # 添加充电基准数据存储
        self.charging_reference_data = None
        self.charging_reference_filepath = None
        # 新增：充电分析结果存储
        self.charging_analysis_results = None
        self.charging_analysis_timestamp = None
        # Redundant mapping attributes removed, data_mapping_config is comprehensive.
        # self.mapping_config = {}
        # self.time_column = ""


class DataVisualizationApp(QMainWindow):
    """主应用程序窗口类"""
    
    def __init__(self):
        super().__init__()
        self.app_data = AppData()
        self.init_ui()
        self.setup_menu()
        self.setup_status_bar()
        # Styles are now set globally in main()
        
    def init_ui(self):
        """初始化用户界面 - 参考main_opt.py的布局方式"""
        self.setWindowTitle("数据处理显示程序 (PyQt6版)")
        self.setGeometry(100, 100, 800, 600)

        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局 - 水平布局，参考main_opt.py
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 左侧按钮区域 - 参考main_opt.py的button_frame
        self.create_button_panel(main_layout)

        # 右侧数据显示区域 - 参考main_opt.py的right_frame
        self.create_data_display_area(main_layout)

        # self.setup_styles() # Styles are now set globally in main()
        
    def create_button_panel(self, parent_layout):
        """创建左侧按钮面板 - 参考main_opt.py的button_frame"""
        button_frame = QFrame()
        button_frame.setFixedWidth(200)
        button_layout = QVBoxLayout(button_frame)
        button_layout.setAlignment(Qt.AlignmentFlag.AlignTop)

        # 功能按钮配置 - 参考main_opt.py的buttons_config
        buttons_config = [
            ("导入数据", self.open_import_data),
            ("数据映射", self.open_data_mapping),
            ("数据筛选", self.open_data_filtering),
            ("充电分析", self.open_charging_analysis),
            ("温感布置", self.open_sensor_layout),
            ("导出充电数据报告", self.open_export_report),
            ("数据分析", self.open_data_analysis)
        ]

        self.function_buttons = {}
        for text, command in buttons_config:
            btn = QPushButton(text)
            btn.clicked.connect(command)
            btn.setMinimumHeight(40)
            btn.setFixedWidth(180)
            if text != "导入数据":
                btn.setEnabled(False)  # 除导入数据外，其他按钮初始禁用
            button_layout.addWidget(btn)
            self.function_buttons[text] = btn

        button_layout.addStretch()
        parent_layout.addWidget(button_frame)
        
    def create_data_display_area(self, parent_layout):
        """创建右侧数据显示区域 - 参考main_opt.py的right_frame"""
        right_frame = QFrame()
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(10, 10, 10, 10)

        # 文件信息显示 - 参考main_opt.py的file_info_frame
        file_info_group = QGroupBox("当前数据文件信息")
        file_info_layout = QVBoxLayout(file_info_group)

        # 数据信息标签
        self.data_info_label = QLabel("请先导入CSV数据文件")
        self.data_info_label.setStyleSheet("color: gray; font-size: 12px;")
        file_info_layout.addWidget(self.data_info_label)

        # 筛选状态标签
        self.filter_status_label = QLabel("未应用筛选")
        self.filter_status_label.setStyleSheet("color: #999; font-size: 12px;")
        file_info_layout.addWidget(self.filter_status_label)

        right_layout.addWidget(file_info_group)

        # 数据预览区域 - 使用标签页显示不同数据源
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout(preview_group)

        # 创建标签页控件
        self.data_tabs = QTabWidget()

        # 原始数据标签页
        self.original_data_widget = QWidget()
        original_layout = QVBoxLayout(self.original_data_widget)

        # 原始数据表格
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.data_table.cellClicked.connect(self.on_table_cell_clicked)
        self.data_table.cellDoubleClicked.connect(self.on_table_cell_double_clicked)

        # 设置表格样式
        table_style = """
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
        """
        self.data_table.setStyleSheet(table_style)

        original_layout.addWidget(self.data_table)
        self.data_tabs.addTab(self.original_data_widget, "原始数据 (前5行 + 后5行)")

        # 充电分析结果标签页
        self.charging_analysis_widget = QWidget()
        charging_layout = QVBoxLayout(self.charging_analysis_widget)

        # 充电分析结果表格
        self.charging_analysis_table = QTableWidget()
        self.charging_analysis_table.setAlternatingRowColors(True)
        self.charging_analysis_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.charging_analysis_table.setStyleSheet(table_style)

        # 充电分析状态标签
        self.charging_analysis_status = QLabel("暂无充电分析结果")
        self.charging_analysis_status.setStyleSheet("color: gray; font-size: 12px; padding: 10px;")

        charging_layout.addWidget(self.charging_analysis_status)
        charging_layout.addWidget(self.charging_analysis_table)
        self.data_tabs.addTab(self.charging_analysis_widget, "充电分析结果")

        preview_layout.addWidget(self.data_tabs)
        right_layout.addWidget(preview_group)

        parent_layout.addWidget(right_frame)
        
    def open_import_data(self):
        """打开文件对话框以导入CSV数据 - 参考main_opt.py的open_import_data"""
        QMessageBox.information(self, "CSV格式要求", "请选择一个CSV格式的数据文件...")

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择CSV数据文件",
            "",
            "CSV 文件 (*.csv);;所有文件 (*.*)"
        )

        if not file_path:
            self.update_info_display("用户取消了文件选择。")
            return

        try:
            # 尝试用不同的编码格式读取文件，增加兼容性
            try:
                df = pd.read_csv(file_path, skip_blank_lines=False)
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(file_path, encoding='gbk', skip_blank_lines=False)
                except UnicodeDecodeError:
                    df = pd.read_csv(file_path, encoding='latin1', skip_blank_lines=False)

            # 清理列名中的单位等无关字符
            import re
            df.columns = [re.sub(r'\[.*?\]', '', col).strip() for col in df.columns]
            # 删除全为空值的行
            df.dropna(how='all', inplace=True)
            df.reset_index(drop=True, inplace=True)

            # 数据清理：删除除第一列外全为0的列
            original_cols = df.shape[1]
            cleaned_df = self.clean_data(df)
            removed_cols = original_cols - cleaned_df.shape[1]

            # 添加时间列：从0开始到数据最后
            if 'time' not in cleaned_df.columns and 'Time' not in cleaned_df.columns:
                # 生成从0开始的时间序列
                time_values = list(range(len(cleaned_df)))
                # 插入到第一列
                cleaned_df.insert(0, 'time', time_values)
                self.update_info_display(f"已自动添加时间列 'time'，数据范围: 0 到 {len(cleaned_df)-1}")

            # 更新共享数据
            self.app_data.imported_data = cleaned_df
            self.app_data.imported_filepath = file_path

            # 应用数据映射配置（包括自定义变量）
            self.apply_data_mapping()

            # 获取最终数据的形状（可能包含新添加的自定义变量）
            final_data = self.app_data.imported_data
            nr, nc = final_data.shape
            col_preview = ', '.join(final_data.columns[:5])
            if nc > 5:
                col_preview += '...'

            # 更新显示
            self.update_info_display("数据导入成功")

            # 更新数据预览表格
            self.show_data_preview()

            # 启用功能按钮
            for btn_name, btn in self.function_buttons.items():
                if btn_name != "导入数据":
                    btn.setEnabled(True)

            info_msg = (f"成功导入: {os.path.basename(file_path)}\n"
                       f"原始数据: {df.shape[0]} 行, {original_cols} 列\n"
                       f"清理后数据: {nr} 行, {nc} 列\n"
                       f"删除了 {removed_cols} 个全零列\n"
                       f"前5个列名: {col_preview}")
            QMessageBox.information(self, "成功", info_msg)

        except Exception as e:
            error_msg = f"导入文件时发生错误: {e}"
            QMessageBox.critical(self, "导入错误", error_msg)
            self.update_info_display(error_msg)

    def clean_data(self, df):
        """清理数据：删除除第一列外全为0的列 - 参考main_opt.py的clean_data"""
        try:
            cleaned_df = df.copy()

            # 获取除第一列外的所有列
            if len(cleaned_df.columns) > 1:
                cols_to_check = cleaned_df.columns[1:]  # 跳过第一列

                # 找出全为0的列
                zero_cols = []
                for col in cols_to_check:
                    try:
                        # 尝试转换为数值类型并检查是否全为0
                        col_data = pd.to_numeric(cleaned_df[col], errors='coerce')
                        if col_data.notna().any() and (col_data == 0).all():
                            zero_cols.append(col)
                    except:
                        # 如果转换失败，跳过该列
                        continue

                # 删除全为0的列
                if zero_cols:
                    cleaned_df = cleaned_df.drop(columns=zero_cols)
                    print(f"删除了全零列: {zero_cols}")

            return cleaned_df

        except Exception as e:
            print(f"数据清理时出错: {e}")
            return df  # 如果清理失败，返回原始数据

    def update_info_display(self, message):
        """更新主界面的信息显示区域 - 参考main_opt.py的update_info_display"""
        # 更新文件信息标签
        if hasattr(self, 'file_info_label'):
            if self.app_data.imported_filepath:
                filename = os.path.basename(self.app_data.imported_filepath)
                self.file_info_label.setText(f"当前文件: {filename}")
                self.file_info_label.setStyleSheet("color: black;")
            else:
                self.file_info_label.setText("未导入数据文件")
                self.file_info_label.setStyleSheet("color: gray;")

        # 如果有数据，显示数据预览
        if self.app_data.imported_data is not None:
            self.show_data_preview()
        else:
            self.show_welcome_message(message)

    def show_welcome_message(self, message):
        """显示欢迎信息 - 参考main_opt.py的show_welcome_message"""
        # 清空表格内容
        self.data_table.clear()
        self.data_table.setRowCount(1)
        self.data_table.setColumnCount(1)

        # 设置简单的列结构显示欢迎信息
        self.data_table.setHorizontalHeaderLabels(['系统信息'])

        # 插入欢迎信息
        item = QTableWidgetItem(message)
        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
        self.data_table.setItem(0, 0, item)

        # 调整列宽
        self.data_table.resizeColumnsToContents()

    def show_data_preview(self):
        """显示数据预览表格（前5行+后5行）- 参考main_opt.py的show_data_preview"""
        # 显示原始数据
        self.show_original_data_preview()
        # 显示充电分析结果
        self.show_charging_analysis_preview()

    def show_original_data_preview(self):
        """显示原始数据预览"""
        try:
            # 清空现有表格内容
            self.data_table.clear()

            # 使用筛选后的数据（如果有）或原始数据
            df = self.get_current_data()
            if df is None or len(df) == 0:
                return

            # 获取前5行和后5行数据
            if len(df) <= 10:
                preview_data = df.copy()
                row_labels = list(range(len(df)))
            else:
                head_data = df.head(5)
                tail_data = df.tail(5)
                preview_data = pd.concat([head_data, tail_data])
                # 创建行标签，显示原始行号
                head_labels = list(range(5))
                tail_labels = list(range(len(df)-5, len(df)))
                row_labels = head_labels + tail_labels

            # 设置表格大小
            columns = ['行号'] + list(preview_data.columns)
            self.data_table.setRowCount(len(preview_data) + (1 if len(df) > 10 else 0))  # +1 for separator
            self.data_table.setColumnCount(len(columns))
            self.data_table.setHorizontalHeaderLabels(columns)

            # 插入数据行
            table_row = 0
            for i, (_, row) in enumerate(preview_data.iterrows()):
                # 准备行数据：行号 + 数据值
                row_data = [str(row_labels[i])]
                for value in row:
                    if pd.isna(value):
                        row_data.append("NaN")
                    elif isinstance(value, float):
                        row_data.append(f"{value:.3f}")
                    else:
                        row_data.append(str(value))

                # 插入到表格
                for col, data in enumerate(row_data):
                    item = QTableWidgetItem(data)
                    if col == 0:  # 行号列居中
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.data_table.setItem(table_row, col, item)

                table_row += 1

                # 如果是前5行和后5行的分界，添加视觉分隔
                if len(df) > 10 and i == 4:
                    # 插入分隔行
                    for col in range(len(columns)):
                        separator_item = QTableWidgetItem('---')
                        separator_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        separator_item.setBackground(Qt.GlobalColor.lightGray)
                        self.data_table.setItem(table_row, col, separator_item)
                    table_row += 1

            # 调整列宽 - 根据列名长度动态调整列宽，最小150像素
            for col in range(len(columns)):
                if col == 0:  # 行号列
                    self.data_table.setColumnWidth(col, 60)
                else:
                    col_name = columns[col]
                    col_width = max(150, len(str(col_name)) * 12 + 20)
                    self.data_table.setColumnWidth(col, col_width)

        except Exception as e:
            # 如果表格显示失败，显示错误信息
            self.data_table.clear()
            self.data_table.setRowCount(1)
            self.data_table.setColumnCount(2)
            self.data_table.setHorizontalHeaderLabels(['错误', '详情'])

            error_item = QTableWidgetItem("错误")
            detail_item = QTableWidgetItem(f"数据预览显示错误: {e}")
            self.data_table.setItem(0, 0, error_item)
            self.data_table.setItem(0, 1, detail_item)

    def show_charging_analysis_preview(self):
        """显示充电分析结果预览"""
        try:
            # 清空充电分析表格
            self.charging_analysis_table.clear()

            # 获取充电分析数据
            charging_df = self.get_charging_analysis_data()

            if charging_df is None or len(charging_df) == 0:
                # 没有充电分析数据
                self.charging_analysis_status.setText("暂无充电分析结果")
                self.charging_analysis_status.setVisible(True)
                self.charging_analysis_table.setVisible(False)
                return

            # 有充电分析数据，显示预览
            analysis_time = getattr(self.app_data, 'charging_analysis_timestamp', '未知')
            self.charging_analysis_status.setText(f"充电分析完成时间: {analysis_time}")
            self.charging_analysis_status.setVisible(True)
            self.charging_analysis_table.setVisible(True)

            # 获取前5行和后5行数据
            if len(charging_df) <= 10:
                preview_data = charging_df.copy()
                row_labels = list(range(len(charging_df)))
            else:
                head_data = charging_df.head(5)
                tail_data = charging_df.tail(5)
                preview_data = pd.concat([head_data, tail_data])
                # 创建行标签，显示原始行号
                head_labels = list(range(5))
                tail_labels = list(range(len(charging_df)-5, len(charging_df)))
                row_labels = head_labels + tail_labels

            # 设置表格大小
            columns = ['行号'] + list(preview_data.columns)
            self.charging_analysis_table.setRowCount(len(preview_data) + (1 if len(charging_df) > 10 else 0))
            self.charging_analysis_table.setColumnCount(len(columns))
            self.charging_analysis_table.setHorizontalHeaderLabels(columns)

            # 插入数据行
            table_row = 0
            for i, (_, row) in enumerate(preview_data.iterrows()):
                # 准备行数据：行号 + 数据值
                row_data = [str(row_labels[i])]
                for value in row:
                    if pd.isna(value):
                        row_data.append("NaN")
                    elif isinstance(value, float):
                        row_data.append(f"{value:.3f}")
                    else:
                        row_data.append(str(value))

                # 插入到表格
                for col, data in enumerate(row_data):
                    item = QTableWidgetItem(data)
                    if col == 0:  # 行号列居中
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    self.charging_analysis_table.setItem(table_row, col, item)

                table_row += 1

                # 如果是前5行和后5行的分界，添加视觉分隔
                if len(charging_df) > 10 and i == 4:
                    # 插入分隔行
                    for col in range(len(columns)):
                        separator_item = QTableWidgetItem('---')
                        separator_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                        separator_item.setBackground(Qt.GlobalColor.lightGray)
                        self.charging_analysis_table.setItem(table_row, col, separator_item)
                    table_row += 1

            # 调整列宽
            for col in range(len(columns)):
                if col == 0:  # 行号列
                    self.charging_analysis_table.setColumnWidth(col, 60)
                else:
                    col_name = columns[col]
                    col_width = max(150, len(str(col_name)) * 12 + 20)
                    self.charging_analysis_table.setColumnWidth(col, col_width)

        except Exception as e:
            # 如果表格显示失败，显示错误信息
            self.charging_analysis_table.clear()
            self.charging_analysis_table.setRowCount(1)
            self.charging_analysis_table.setColumnCount(2)
            self.charging_analysis_table.setHorizontalHeaderLabels(['错误', '详情'])

            error_item = QTableWidgetItem("错误")
            detail_item = QTableWidgetItem(f"充电分析预览显示错误: {e}")
            self.charging_analysis_table.setItem(0, 0, error_item)
            self.charging_analysis_table.setItem(0, 1, detail_item)

    def apply_data_mapping(self):
        """应用数据映射配置，包括自定义变量计算"""
        if not hasattr(self.app_data, 'data_mapping_config') or not self.app_data.data_mapping_config:
            return

        config = self.app_data.data_mapping_config

        # 应用自定义变量计算
        if 'custom_variables' in config and config['custom_variables']:
            try:
                import pandas as pd
                import numpy as np

                data = self.app_data.imported_data.copy()
                custom_vars_count = 0

                for var_config in config['custom_variables']:
                    var_name = var_config.get('name', '').strip()
                    formula = var_config.get('formula', '').strip()

                    if not var_name or not formula:
                        continue

                    try:
                        # 创建安全的计算环境
                        safe_dict = {
                            'np': np,
                            'abs': abs,
                            'max': max,
                            'min': min,
                            'sum': sum,
                            'len': len
                        }

                        # 添加数据列到计算环境
                        for col in data.columns:
                            safe_dict[col] = data[col]

                        # 计算自定义变量
                        result = eval(formula, {"__builtins__": {}}, safe_dict)
                        data[var_name] = result
                        custom_vars_count += 1

                    except Exception as e:
                        print(f"计算自定义变量 {var_name} 时出错: {str(e)}")
                        data[var_name] = np.nan

                # 更新数据
                self.app_data.imported_data = data

                if custom_vars_count > 0:
                    self.update_info_display(f"已计算 {custom_vars_count} 个自定义变量")

            except Exception as e:
                print(f"应用自定义变量时出错: {str(e)}")

    def open_data_analysis(self):
        """打开数据分析视图 - 参考data_analysis_view.py"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "警告", "请先导入数据文件")
            return

        try:
            from data_analysis_view_pyqt import DataAnalysisDialog
            self.data_analysis_window = DataAnalysisDialog(self, self.app_data)
            self.data_analysis_window.show()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开数据分析视图时发生错误: {str(e)}")


    def _setup_menu_actions(self):
        """Helper method to create menu actions. Called by setup_menu."""
        # File Menu Actions
        import_action = QAction('导入数据', self)
        import_action.setShortcut('Ctrl+O')
        # Connect to the unified open_import_data method
        import_action.triggered.connect(self.open_import_data)
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)

        # Tools Menu Actions
        clear_filter_action = QAction('清除筛选', self)
        clear_filter_action.triggered.connect(self.clear_filter)

        # Help Menu Actions
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)

        return {
            "file": [import_action, None, exit_action], # None for separator
            "tools": [clear_filter_action],
            "help": [about_action]
        }

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        actions = self._setup_menu_actions()

        file_menu = menubar.addMenu('文件')
        for action in actions["file"]:
            if action is None:
                file_menu.addSeparator()
            else:
                file_menu.addAction(action)

        tools_menu = menubar.addMenu('工具')
        for action in actions["tools"]:
            tools_menu.addAction(action)

        help_menu = menubar.addMenu('帮助')
        for action in actions["help"]:
            help_menu.addAction(action)
        
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪") # Initial message
        
    def setup_styles(self):
        """设置应用程序样式"""
        # This is the more comprehensive style definition
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #4CAF50;
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QTableWidget {
            gridline-color: #d0d0d0;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }
        QTableWidget::item:selected {
            background-color: #3daee9;
            color: white;
        }
        """
        self.setStyleSheet(style)
        
    # Removed duplicated import_data method. open_import_data is now the standard.
    # The menu action for import now calls open_import_data.

    def import_charging_reference_data(self):
        """导入充电基准数据（用于充电分析）"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择充电基准数据文件",
            "",
            "CSV文件 (*.csv);;所有文件 (*)"
        )

        if not file_path:
            return

        try:
            # 读取CSV文件
            df = pd.read_csv(file_path, encoding='utf_8_sig')

            # 验证必要列
            required_columns = ['temp', 'voltage', 'current']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                QMessageBox.warning(
                    self,
                    "列缺失",
                    f"充电基准数据文件缺少必要列: {', '.join(missing_columns)}\n"
                    f"需要包含: {', '.join(required_columns)}"
                )
                return

            # 更新应用数据
            self.app_data.charging_reference_data = df
            self.app_data.charging_reference_filepath = file_path

            # 显示成功消息
            QMessageBox.information(
                self,
                "导入成功",
                f"成功导入充电基准数据: {Path(file_path).name}\n"
                f"数据规模: {len(df)}行 × {len(df.columns)}列"
            )

            # 更新状态
            if hasattr(self, 'status_bar'):
                 self.status_bar.showMessage(f"充电基准数据已导入: {Path(file_path).name}")

        except Exception as e:
            QMessageBox.critical(self, "导入错误", f"无法导入充电基准数据文件:\n{str(e)}")
        
    def get_current_data(self):
        """获取当前应该使用的数据（筛选后的或原始的）"""
        if self.app_data.filter_applied and self.app_data.filtered_data is not None:
            return self.app_data.filtered_data
        return self.app_data.imported_data

    def get_charging_analysis_data(self):
        """获取充电分析结果数据"""
        if (hasattr(self.app_data, 'charging_analysis_results') and
            self.app_data.charging_analysis_results and
            'analysis_results' in self.app_data.charging_analysis_results):
            return self.app_data.charging_analysis_results['analysis_results']
        return None
        
    def update_data_info(self):
        """更新数据信息显示"""
        # Ensure app_data.current_filename is set correctly after import
        # open_import_data sets app_data.imported_filepath
        if self.app_data.imported_filepath:
            self.app_data.current_filename = Path(self.app_data.imported_filepath).name
        else:
            self.app_data.current_filename = ""

        current_data = self.get_current_data()
        if current_data is not None:
            rows, cols = current_data.shape
            info_text = f"文件: {self.app_data.current_filename} | 数据: {rows}行 × {cols}列"
            self.data_info_label.setText(info_text)
            
            # 更新筛选状态
            if self.app_data.filter_applied:
                # Ensure imported_data is not None before trying to get its length
                if self.app_data.imported_data is not None:
                    original_rows = len(self.app_data.imported_data)
                    self.filter_status_label.setText(f"已应用筛选 ({original_rows}→{rows}行)")
                else: # Should not happen if filter_applied is True, but as a safeguard
                    self.filter_status_label.setText(f"已应用筛选 (?→{rows}行)")
                self.filter_status_label.setStyleSheet("color: #4CAF50; font-size: 12px; font-weight: bold;")
            else:
                self.filter_status_label.setText("未应用筛选")
                self.filter_status_label.setStyleSheet("color: #999; font-size: 12px;")
        else:
            self.data_info_label.setText("请先导入CSV数据文件")
            self.filter_status_label.setText("未应用筛选")

        # Update status bar message after data import or filter change
        if hasattr(self, 'status_bar'):
            if self.app_data.current_filename:
                self.status_bar.showMessage(f"当前文件: {self.app_data.current_filename}")
            else:
                self.status_bar.showMessage("就绪")

            
    def on_table_cell_clicked(self, row, column):
        """处理表格单元格点击事件 - 简化版本"""
        # 简单的点击处理，不执行删除操作
        pass

    def on_table_cell_double_clicked(self, row, column):
        """处理表格单元格双击事件 - 删除行功能"""
        if self.app_data.imported_data is None:
            return

        # 获取当前使用的数据
        current_data_df = self.get_current_data() # Renamed to avoid conflict
        if current_data_df is None or len(current_data_df) == 0:
            return

        # 由于表格显示的是前5行+后5行的预览，需要计算实际行号
        actual_row = self.get_actual_row_index(row, len(current_data_df))
        if actual_row is None: # Could be the separator row
            # QMessageBox.warning(self, "无法删除", "无法确定要删除的行，请在数据筛选界面进行删除操作。")
            return

        # 弹框确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"是否要删除第 {actual_row + 1} 行数据？\n\n注意：此操作将直接修改原始数据，无法撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            try:
                # Determine which dataframe to modify
                if self.app_data.filter_applied and self.app_data.filtered_data is not None:
                    # If filter is applied, modify filtered_data
                    # Also, find the corresponding row in imported_data to remove it as well,
                    # to maintain consistency if the filter is cleared later.
                    # This requires careful index management.
                    # For now, let's simplify: if filtered, only modify filtered.
                    # User should be aware that clearing filter might bring back "deleted" rows if not handled in original.
                    # A better approach would be to get the original index from filtered_data and remove from imported_data.

                    # Get the index from the original DataFrame if possible
                    original_index_to_drop = None
                    if self.app_data.filtered_data.index[actual_row] in self.app_data.imported_data.index:
                         original_index_to_drop = self.app_data.filtered_data.index[actual_row]

                    self.app_data.filtered_data = self.app_data.filtered_data.drop(
                        self.app_data.filtered_data.index[actual_row]
                    ).reset_index(drop=True)

                    # If the index was found in imported_data, remove it from there too
                    if original_index_to_drop is not None and self.app_data.imported_data is not None:
                        try:
                            self.app_data.imported_data = self.app_data.imported_data.drop(
                                original_index_to_drop
                            ).reset_index(drop=True)
                        except KeyError: # Index might not exist if rows were already dropped
                            pass

                elif self.app_data.imported_data is not None: # No filter, or filtered_data is None
                    # Delete from original data
                    self.app_data.imported_data = self.app_data.imported_data.drop(
                        self.app_data.imported_data.index[actual_row]
                    ).reset_index(drop=True)

                # Refresh display
                self.show_data_preview()
                self.update_data_info()

                QMessageBox.information(self, "删除成功", f"已成功删除第 {actual_row + 1} 行数据。")

            except Exception as e:
                QMessageBox.critical(self, "删除失败", f"删除数据时发生错误：{str(e)}")

    def get_actual_row_index(self, display_row, total_rows):
        """根据显示行号获取实际数据行号"""
        if total_rows <= 10:
            return display_row
        else:
            if display_row < 5: # First 5 rows
                return display_row
            elif display_row == 5: # Separator row
                return None
            else: # Last 5 rows (display_row index 6 to 10)
                  # display_row 6 maps to total_rows - 5
                  # display_row 7 maps to total_rows - 4
                  # ...
                  # display_row 10 maps to total_rows - 1
                return total_rows - 5 + (display_row - 6)
            
    def clear_filter(self):
        """清除当前的筛选状态"""
        self.app_data.filter_applied = False
        self.app_data.filtered_data = None
        self.show_data_preview() # Refresh preview with original data
        self.update_data_info()  # Update info labels
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("筛选已清除。显示原始数据。")
        QMessageBox.information(self, "筛选已清除", "已清除所有筛选，当前显示原始数据。")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
                          "数据处理显示程序 (PyQt6版)\n\n"
                          "一个用于CSV数据可视化和分析的工具。\n"
                          "版本: 2.0 (Jules Refactored)")
        
    # 功能模块启动方法
    def open_data_filtering(self):
        """打开数据筛选窗口"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "请先导入CSV数据文件。")
            return

        try:
            from data_filtering_pyqt import DataFilteringDialog

            dialog = DataFilteringDialog(self, self.app_data)
            # 连接筛选应用信号
            dialog.filter_applied.connect(self.on_filter_applied)
            dialog.exec()

        except ImportError:
            QMessageBox.critical(self, "模块错误", "无法加载数据筛选模块。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开数据筛选窗口时发生错误: {str(e)}")

    def on_filter_applied(self, filtered_data):
        """处理筛选应用事件"""
        # 更新应用数据状态
        self.app_data.filtered_data = filtered_data
        self.app_data.filter_applied = True
        # 更新数据显示
        self.show_data_preview()
        self.update_data_info() # This will also update filter_status_label
        if hasattr(self, 'status_bar'):
            self.status_bar.showMessage("筛选已应用。")
        # QMessageBox.information(self, "筛选应用", "新的数据筛选已应用。") # This is now handled in DataFilteringDialog

    # Removed duplicated get_current_data, setup_styles, setup_menu, setup_status_bar
        
    def open_charging_analysis(self):
        """打开充电分析窗口"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "请先导入CSV数据文件。")
            return

        try:
            from charging_analysis_pyqt import ChargingAnalysisDialog

            dialog = ChargingAnalysisDialog(self, self.app_data)
            result = dialog.exec()

            # 对话框关闭后，刷新数据显示以显示可能的充电分析结果
            self.show_data_preview()

            # 如果有充电分析结果，切换到充电分析结果标签页
            if (hasattr(self.app_data, 'charging_analysis_results') and
                self.app_data.charging_analysis_results):
                self.data_tabs.setCurrentIndex(1)  # 切换到充电分析结果标签页

        except ImportError:
            QMessageBox.critical(self, "模块错误", "无法加载充电分析模块。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开充电分析窗口时发生错误: {str(e)}")
        
    def open_data_mapping(self):
        """打开数据映射窗口"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "请先导入CSV数据文件。")
            return

        try:
            from data_mapping_pyqt import MappingWindow

            dialog = MappingWindow(self, self.app_data)
            dialog.exec()

        except ImportError:
            QMessageBox.critical(self, "模块错误", "无法加载数据映射模块。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开数据映射窗口时发生错误: {str(e)}")

    def open_sensor_layout(self):
        """打开温感布置窗口"""
        try:
            from sensor_layout_pyqt import SensorLayoutDialog

            dialog = SensorLayoutDialog(self, self.app_data)
            dialog.exec()

        except ImportError:
            QMessageBox.critical(self, "模块错误", "无法加载温感布置模块。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开温感布置窗口时发生错误: {str(e)}")

    def open_data_analysis_view(self):
        """打开数据分析视图窗口"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "请先导入CSV数据文件。")
            return

        try:
            from data_analysis_view_pyqt import DataAnalysisDialog

            dialog = DataAnalysisDialog(self, self.app_data)
            dialog.exec()

        except ImportError:
            QMessageBox.critical(self, "模块错误", "无法加载数据分析模块。")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开数据分析视图窗口时发生错误: {str(e)}")
        
    def open_export_report(self):
        """打开报告导出窗口"""
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "警告", "请先导入数据文件")
            return

        try:
            from export_report_pyqt import ExportReportWindow
            self.export_window = ExportReportWindow(self, self.app_data)
            self.export_window.show()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开报告导出时发生错误: {str(e)}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("数据可视化分析程序")
    app.setApplicationVersion("2.0")
    app.setOrganizationName("DataViz")
    
    # 创建主窗口
    window = DataVisualizationApp()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == '__main__':
    main()
