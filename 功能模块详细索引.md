# 功能模块详细索引

## 📋 模块功能概览

### 🏠 main_opt.py - 主程序模块
**主要功能:**
- 应用程序启动和主界面管理
- CSV数据文件导入和预览
- 数据表格显示（前5行+后5行预览）
- 行删除功能（点击行弹出确认对话框）
- 各功能模块的导航入口

**核心类:**
- `DataVisualizationApp` - 主应用程序类
- `AppData` - 共享数据管理类

**主要方法:**
- `import_data()` - 数据导入功能
- `show_data_preview()` - 数据预览显示
- `on_table_click()` - 表格点击事件处理
- `delete_row()` - 行删除功能

---

### 🔍 data_filtering.py - 数据筛选模块
**主要功能:**
- 信号列的选择和筛选
- 筛选配置的保存和加载
- 筛选后数据的导出
- 自定义保存位置选择

**核心类:**
- `DataFilteringWindow` - 数据筛选窗口类

**主要方法:**
- `create_checkboxes()` - 创建信号选择复选框
- `save_config()` - 保存筛选配置
- `load_config()` - 加载筛选配置
- `save_data()` - 保存筛选后的数据

**特色功能:**
- 默认无信号选中，避免误操作
- 支持批量信号选择/取消
- 用户可指定保存位置

---

### ⚡ charging_analysis.py - 充电分析模块
**主要功能:**
- 充电数据的深度分析
- 充电曲线可视化
- 分析报告生成和导出
- 滚动界面支持长内容显示

**核心类:**
- `ChargingAnalysisWindow` - 充电分析窗口类

**主要方法:**
- `start_processing()` - 开始数据分析
- `show_analysis_plot()` - 显示分析图表
- `generate_report()` - 生成分析报告

**界面特色:**
- 固定返回按钮，始终可见
- 滚动内容区域，支持查看所有信息
- 进度条显示分析进度

---

### 🗺️ data_mapping.py - 数据映射模块
**主要功能:**
- 逻辑列名与物理列名的映射管理
- 映射关系的配置和保存
- 数据列对应关系处理

**核心类:**
- `DataMappingWindow` - 数据映射窗口类

**主要方法:**
- `create_mapping_interface()` - 创建映射界面
- `save_mapping()` - 保存映射配置
- `load_mapping()` - 加载映射配置

---

### 🌡️ sensor_layout.py - 温感布置模块
**主要功能:**
- 传感器布局的可视化显示
- 温度数据的空间分布分析
- 图像处理和显示功能

**核心类:**
- `SensorLayoutWindow` - 温感布置窗口类

**依赖要求:**
- PIL/Pillow库（图像处理）
- 自动检测依赖并提示安装

---

### 📊 data_analysis_view.py - 数据分析视图模块
**主要功能:**
- 数据分析结果的可视化展示
- 多维度数据图表生成
- 分析结果的交互式查看

**核心类:**
- `DataAnalysisViewWindow` - 数据分析视图窗口类

---

### 📄 export_report.py - 报告导出模块
**主要功能:**
- 分析结果报告的生成
- 多种格式的导出支持
- 报告模板的管理

**核心类:**
- `ExportReportWindow` - 报告导出窗口类

---

## 🔧 工具和测试模块

### 🧪 test_modules.py
- 各功能模块的单元测试
- 模块功能验证

### 🔍 test_syntax.py
- 代码语法检查
- 静态代码分析

### 🔋 beautifulbattery.py
- 电池状态美化显示
- 电池图形界面工具

### ⚡ chargevoltage.py
- 充电电压专项分析
- 电压曲线处理工具

---

## 📁 配置文件说明

### 📋 requirements.txt
项目依赖包列表：
```
tkinter
pandas
matplotlib
numpy
Pillow
```

### 🗂️ E08-data_mapping_config.json
数据映射配置文件，存储：
- 逻辑列名与物理列名的对应关系
- 用户自定义的映射规则
- 映射配置的历史记录

---

## 🚀 模块启动顺序

1. **main_opt.py** - 程序入口，初始化主界面
2. **数据导入** - 用户选择CSV文件导入
3. **功能模块** - 根据需要启动相应分析模块
4. **结果输出** - 生成报告或导出处理后的数据

---

## 🔄 数据流转

```
CSV文件 → main_opt.py → AppData共享对象 → 各功能模块 → 分析结果 → 报告导出
```

---

## 🎯 核心特性

- **模块化设计** - 各功能独立，便于维护
- **数据共享** - AppData类统一管理数据状态
- **用户友好** - 直观的GUI界面和操作流程
- **功能完整** - 从数据导入到结果导出的完整流程
- **错误处理** - 完善的异常处理和用户提示
- **界面优化** - 滚动条、列宽调整等用户体验改进

---
*最后更新时间: 2025-06-26*
