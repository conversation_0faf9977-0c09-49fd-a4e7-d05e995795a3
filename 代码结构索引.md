# 代码结构索引

## 📝 代码组织结构

### 🏗️ 架构模式
- **MVC模式** - 模型(AppData)、视图(各Window类)、控制器(事件处理方法)
- **模块化设计** - 每个功能独立成模块，便于维护和扩展
- **事件驱动** - 基于Tkinter的事件驱动GUI架构

---

## 🔍 核心类和方法索引

### 📁 main_opt.py

#### 🏠 DataVisualizationApp类
```python
class DataVisualizationApp(tk.Tk):
    def __init__(self):                    # 主程序初始化
    def setup_ui(self):                    # 界面设置
    def import_data(self):                 # 数据导入
    def show_data_preview(self):           # 数据预览显示
    def on_table_click(self, event):       # 表格点击事件
    def delete_row(self, row_index):       # 行删除功能
    def update_info_display(self, message): # 信息显示更新
    
    # 功能模块启动方法
    def open_data_filtering(self):         # 打开数据筛选
    def open_charging_analysis(self):      # 打开充电分析
    def open_data_mapping(self):           # 打开数据映射
    def open_sensor_layout(self):          # 打开温感布置
    def open_data_analysis_view(self):     # 打开数据分析视图
    def open_export_report(self):          # 打开报告导出
```

#### 📊 AppData类
```python
class AppData:
    def __init__(self):
        self.imported_data = None          # 导入的数据
        self.file_paths = []               # 文件路径列表
        self.current_filename = ""         # 当前文件名
```

---

### 📁 data_filtering.py

#### 🔍 DataFilteringWindow类
```python
class DataFilteringWindow(tk.Toplevel):
    def __init__(self, parent, app_data):  # 窗口初始化
    def setup_ui(self):                    # 界面设置
    def create_checkboxes(self):           # 创建复选框
    def select_all(self):                  # 全选功能
    def deselect_all(self):                # 全不选功能
    def show_selected_only(self):          # 只显示已选
    def save_config(self):                 # 保存配置
    def load_config(self):                 # 加载配置
    def save_data(self):                   # 保存筛选数据
```

---

### 📁 charging_analysis.py

#### ⚡ ChargingAnalysisWindow类
```python
class ChargingAnalysisWindow(tk.Toplevel):
    def __init__(self, parent, app_data):  # 窗口初始化
    def setup_ui(self):                    # 界面设置（含滚动）
    def start_processing(self):            # 开始分析处理
    def show_analysis_plot(self):          # 显示分析图表
    def generate_report(self):             # 生成分析报告
    def update_progress(self, value):      # 更新进度条
```

**界面结构:**
```
固定区域: 返回按钮
滚动区域: 
  ├── 控制面板
  ├── 分析参数设置
  ├── 进度显示
  └── 结果展示
```

---

### 📁 data_mapping.py

#### 🗺️ DataMappingWindow类
```python
class DataMappingWindow(tk.Toplevel):
    def __init__(self, parent, app_data):  # 窗口初始化
    def setup_ui(self):                    # 界面设置
    def create_mapping_interface(self):    # 创建映射界面
    def save_mapping(self):                # 保存映射配置
    def load_mapping(self):                # 加载映射配置
    def apply_mapping(self):               # 应用映射
```

---

### 📁 sensor_layout.py

#### 🌡️ SensorLayoutWindow类
```python
class SensorLayoutWindow(tk.Toplevel):
    def __init__(self, parent, app_data):  # 窗口初始化
    def setup_ui(self):                    # 界面设置
    def load_image(self):                  # 加载图像
    def process_sensor_data(self):         # 处理传感器数据
    def display_layout(self):              # 显示布局
```

---

## 🔧 关键技术实现

### 📊 数据处理
```python
# pandas DataFrame操作
df = pd.read_csv(file_path, encoding='utf_8_sig')
df = df.drop(index=row_index).reset_index(drop=True)
filtered_df = df[selected_columns]
```

### 🖼️ GUI界面
```python
# Tkinter滚动界面实现
canvas = tk.Canvas(container)
scrollbar = ttk.Scrollbar(container, orient="vertical", command=canvas.yview)
scrollable_frame = ttk.Frame(canvas)
canvas.configure(yscrollcommand=scrollbar.set)
```

### 📁 文件操作
```python
# 文件对话框
filepath = filedialog.askopenfilename(
    filetypes=[("CSV 文件", "*.csv"), ("所有文件", "*.*")],
    title="选择CSV文件"
)
```

### 🎨 图表绘制
```python
# matplotlib图表
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
```

---

## 🔄 事件处理机制

### 🖱️ 鼠标事件
```python
# 表格点击事件
self.data_table.bind('<Button-1>', self.on_table_click)

# 鼠标滚轮事件
def _on_mousewheel(event):
    self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
self.canvas.bind("<MouseWheel>", _on_mousewheel)
```

### ⌨️ 按钮事件
```python
# 按钮命令绑定
ttk.Button(frame, text="开始分析", command=self.start_processing)
ttk.Button(frame, text="返回主页", command=self.destroy)
```

---

## 📦 依赖管理

### 🔗 导入结构
```python
# 标准库
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import json

# 第三方库
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from PIL import Image, ImageTk  # 可选依赖
```

### 🛡️ 错误处理
```python
try:
    # 核心功能代码
    pass
except ImportError:
    messagebox.showerror("依赖缺失", "请安装必要的依赖包")
except Exception as e:
    messagebox.showerror("错误", f"操作失败: {e}")
```

---

## 🎯 代码规范

### 📝 命名约定
- **类名**: PascalCase (如: `DataFilteringWindow`)
- **方法名**: snake_case (如: `setup_ui()`)
- **变量名**: snake_case (如: `selected_columns`)
- **常量**: UPPER_CASE (如: `DEFAULT_WIDTH`)

### 📋 文档字符串
```python
def method_name(self, param):
    """方法功能描述
    
    Args:
        param: 参数描述
        
    Returns:
        返回值描述
    """
```

### 🔧 代码组织
- 每个模块独立文件
- 类和方法按功能分组
- 导入语句按标准库、第三方库、本地模块顺序
- 适当的注释和文档字符串

## 🚀 快速查找索引

### 🔍 常用功能定位

| 功能 | 文件 | 类/方法 | 行号范围 |
|------|------|---------|----------|
| 数据导入 | main_opt.py | `import_data()` | ~140-170 |
| 行删除 | main_opt.py | `delete_row()` | ~148-166 |
| 表格显示 | main_opt.py | `show_data_preview()` | ~198-250 |
| 数据筛选 | data_filtering.py | `DataFilteringWindow` | 全文件 |
| 充电分析 | charging_analysis.py | `ChargingAnalysisWindow` | 全文件 |
| 滚动界面 | charging_analysis.py | `setup_ui()` | ~47-87 |
| 配置保存 | data_filtering.py | `save_config()` | ~174-198 |
| 报告生成 | charging_analysis.py | `generate_report()` | ~800+ |

### 🐛 常见问题定位

| 问题类型 | 查找位置 | 关键词 |
|----------|----------|--------|
| 界面布局问题 | `setup_ui()` 方法 | `pack()`, `grid()`, `Canvas` |
| 数据处理错误 | 各模块的数据处理方法 | `pandas`, `DataFrame` |
| 文件操作问题 | `filedialog` 相关方法 | `askopenfilename`, `asksaveasfilename` |
| 事件处理问题 | `bind()` 相关代码 | `<Button-1>`, `<MouseWheel>` |

### 📋 配置文件位置

| 配置类型 | 文件名 | 用途 |
|----------|--------|------|
| 数据映射 | `E08-data_mapping_config.json` | 列名映射关系 |
| 筛选配置 | 用户指定 | 信号筛选设置 |
| 依赖包 | `requirements.txt` | Python包依赖 |

---
*最后更新时间: 2025-06-26*
