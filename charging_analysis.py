# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import threading
import os

# 配置 Matplotlib 中文字体
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
except Exception as e:
    print(f"注意: 无法设置 Matplotlib 中文字体: {e}")

class ChargingAnalysisWindow(tk.Toplevel):
    """充电分析窗口 - 用于电池充电数据分析"""

    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.title("充电分析")
        self.geometry("900x700")
        self.transient(parent)
        self.grab_set()

        # 数据相关属性
        self.charging_path = None  # 充电基准文件路径
        self.df = None  # 充电基准数据DataFrame
        self.input_df = None  # 输入数据DataFrame
        self.result_df = None  # 分析结果DataFrame

        # UI组件
        self.progress = tk.DoubleVar()
        self.status_label = None
        self.plot_frame = None
        self.canvas = None

        self.setup_ui()
        self.prepare_input_data()
        self.load_existing_charging_data()

    def setup_ui(self):
        """设置用户界面"""
        # 创建顶级容器
        top_container = ttk.Frame(self, padding="10")
        top_container.pack(expand=True, fill=tk.BOTH)

        # 创建固定的返回按钮区域（不滚动）
        fixed_button_frame = ttk.Frame(top_container)
        fixed_button_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(fixed_button_frame, text="返回主页", command=self.destroy).pack(side=tk.RIGHT)

        # 创建滚动容器框架
        scroll_container = ttk.Frame(top_container)
        scroll_container.pack(expand=True, fill=tk.BOTH)

        # 创建Canvas和滚动条
        self.canvas = tk.Canvas(scroll_container)
        scrollbar = ttk.Scrollbar(scroll_container, orient="vertical", command=self.canvas.yview)
        self.scrollable_frame = ttk.Frame(self.canvas)

        # 配置滚动
        self.scrollable_frame.bind(
            "<Configure>",
            lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all"))
        )

        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")
        self.canvas.configure(yscrollcommand=scrollbar.set)

        # 布局Canvas和滚动条
        self.canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 绑定鼠标滚轮事件
        def _on_mousewheel(event):
            self.canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        self.canvas.bind("<MouseWheel>", _on_mousewheel)

        # 现在使用scrollable_frame作为主框架
        main_frame = self.scrollable_frame

        # 顶部控制区域
        control_frame = ttk.LabelFrame(main_frame, text="文件选择与控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))

        # 数据映射状态显示
        mapping_frame = ttk.LabelFrame(control_frame, text="数据映射状态", padding="5")
        mapping_frame.pack(fill=tk.X, pady=(0, 10))

        self.mapping_status_text = tk.Text(mapping_frame, height=3, width=80, state=tk.DISABLED, wrap=tk.WORD)
        self.mapping_status_text.pack(fill=tk.X)

        # 文件选择区域
        file_frame = ttk.Frame(control_frame)
        file_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(file_frame, text="充电基准文件:").pack(side=tk.LEFT, padx=(0, 5))
        self.charging_file_label = ttk.Label(file_frame, text="未选择", foreground="red")
        self.charging_file_label.pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(file_frame, text="选择充电基准文件", command=self.select_charging_file).pack(side=tk.LEFT)

        # 操作按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        self.start_button = ttk.Button(button_frame, text="开始分析", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))

        self.plot_button = ttk.Button(button_frame, text="显示图表", command=self.show_analysis_plot, state=tk.DISABLED)
        self.plot_button.pack(side=tk.LEFT, padx=(0, 5))

        self.report_button = ttk.Button(button_frame, text="生成报告", command=self.generate_report, state=tk.DISABLED)
        self.report_button.pack(side=tk.LEFT, padx=(0, 5))

        # 进度条和状态
        progress_frame = ttk.Frame(control_frame)
        progress_frame.pack(fill=tk.X, pady=(10, 0))

        ttk.Label(progress_frame, text="进度:").pack(side=tk.LEFT, padx=(0, 5))
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress, maximum=100)
        self.progress_bar.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        self.status_label = ttk.Label(progress_frame, text="就绪")
        self.status_label.pack(side=tk.RIGHT)

        # 数据预览区域
        preview_frame = ttk.LabelFrame(main_frame, text="数据预览", padding="5")
        preview_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建两列预览
        preview_columns = ttk.Frame(preview_frame)
        preview_columns.pack(fill=tk.X)

        # 输入数据预览
        input_preview_frame = ttk.LabelFrame(preview_columns, text="输入数据 (前5行)", padding="5")
        input_preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        self.input_preview_text = tk.Text(input_preview_frame, height=6, state=tk.DISABLED, wrap=tk.NONE)
        input_scrollbar = ttk.Scrollbar(input_preview_frame, orient=tk.HORIZONTAL, command=self.input_preview_text.xview)
        self.input_preview_text.config(xscrollcommand=input_scrollbar.set)
        self.input_preview_text.pack(fill=tk.BOTH, expand=True)
        input_scrollbar.pack(fill=tk.X)

        # 基准数据预览
        charging_preview_frame = ttk.LabelFrame(preview_columns, text="充电基准数据 (前5行)", padding="5")
        charging_preview_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))

        self.charging_preview_text = tk.Text(charging_preview_frame, height=6, state=tk.DISABLED, wrap=tk.NONE)
        charging_scrollbar = ttk.Scrollbar(charging_preview_frame, orient=tk.HORIZONTAL, command=self.charging_preview_text.xview)
        self.charging_preview_text.config(xscrollcommand=charging_scrollbar.set)
        self.charging_preview_text.pack(fill=tk.BOTH, expand=True)
        charging_scrollbar.pack(fill=tk.X)

        # 报告预览区域
        report_preview_frame = ttk.LabelFrame(main_frame, text="分析报告预览 (前5列)", padding="5")
        report_preview_frame.pack(fill=tk.X, pady=(0, 10))

        self.report_preview_text = tk.Text(report_preview_frame, height=6, state=tk.DISABLED, wrap=tk.NONE)
        report_scrollbar = ttk.Scrollbar(report_preview_frame, orient=tk.HORIZONTAL, command=self.report_preview_text.xview)
        self.report_preview_text.config(xscrollcommand=report_scrollbar.set)
        self.report_preview_text.pack(fill=tk.BOTH, expand=True)
        report_scrollbar.pack(fill=tk.X)

        # 图表显示区域
        self.plot_frame = ttk.LabelFrame(main_frame, text="分析图表", padding="10")
        self.plot_frame.pack(expand=True, fill=tk.BOTH)

        # 初始提示
        ttk.Label(self.plot_frame, text="请先选择充电基准文件并开始分析").pack(expand=True)

    def prepare_input_data(self):
        """准备输入数据"""
        try:
            if self.app_data.imported_data is not None:
                # 使用数据映射配置转换列名
                self.input_df = self.app_data.imported_data.copy()

                # 应用数据映射
                if self.app_data.data_mapping_config and self.app_data.data_mapping_config.get("mappings"):
                    mappings = self.app_data.data_mapping_config["mappings"]
                    # 创建反向映射字典
                    reverse_mappings = {v: k for k, v in mappings.items()}

                    # 重命名列
                    rename_dict = {}
                    for logical_name, csv_col in reverse_mappings.items():
                        if csv_col in self.input_df.columns:
                            rename_dict[csv_col] = logical_name

                    if rename_dict:
                        self.input_df.rename(columns=rename_dict, inplace=True)

                self.update_status("输入数据已准备就绪")

                # 更新映射状态显示
                self.update_mapping_status()

                # 更新输入数据预览
                self.update_input_data_preview()

            else:
                messagebox.showwarning("无数据", "没有找到已导入的数据。请先在主界面导入数据。", parent=self)

        except Exception as e:
            messagebox.showerror("数据准备错误", f"准备输入数据时发生错误: {e}", parent=self)

    def load_existing_charging_data(self):
        """加载已存在的充电基准数据"""
        try:
            if (self.app_data.charging_reference_data is not None and
                self.app_data.charging_reference_filepath is not None):

                # 从共享数据加载充电基准数据
                self.df = self.app_data.charging_reference_data.copy()
                self.charging_path = self.app_data.charging_reference_filepath

                # 更新UI显示
                filename = os.path.basename(self.charging_path)
                self.charging_file_label.config(text=filename, foreground="green")
                self.update_status(f"已加载充电基准文件: {filename}")

                # 更新充电基准数据预览
                self.update_charging_data_preview()

        except Exception as e:
            self.update_status(f"加载已有充电数据时出错: {e}")

    def update_mapping_status(self):
        """更新数据映射状态显示"""
        try:
            self.mapping_status_text.config(state=tk.NORMAL)
            self.mapping_status_text.delete(1.0, tk.END)

            required_columns = ['MinT', 'MaxV']
            status_lines = []

            # 检查是否有导入数据
            if self.app_data.imported_data is None:
                status_lines.append("⚠ 未导入数据，请先在主界面导入数据")
                self.mapping_status_text.insert(tk.END, "\n".join(status_lines))
                self.mapping_status_text.config(state=tk.DISABLED)
                return

            # 检查数据映射配置
            if self.app_data.data_mapping_config and self.app_data.data_mapping_config.get('mappings'):
                mappings = self.app_data.data_mapping_config['mappings']  # csv_col: logical_name
                status_lines.append("✓ 数据映射配置已加载")

                # 创建反向映射：logical_name -> csv_col
                reverse_mappings = {logical_name: csv_col for csv_col, logical_name in mappings.items()}

                # 检查每个必需列的映射状态
                for col in required_columns:
                    # 检查是否有映射到该逻辑列名
                    if col in reverse_mappings:
                        csv_col = reverse_mappings[col]
                        # 检查映射的物理列是否存在于原始数据中
                        if csv_col in self.app_data.imported_data.columns:
                            status_lines.append(f"✓ {col} <- {csv_col} (已映射)")
                        else:
                            status_lines.append(f"✗ {col} <- {csv_col} (源列不存在)")
                    else:
                        # 检查是否有直接匹配的列（在处理后的数据中）
                        if hasattr(self, 'input_df') and self.input_df is not None and col in self.input_df.columns:
                            status_lines.append(f"✓ {col} (直接匹配)")
                        elif col in self.app_data.imported_data.columns:
                            status_lines.append(f"✓ {col} (原始数据中存在)")
                        else:
                            status_lines.append(f"✗ {col} (缺少映射)")
            else:
                # 没有映射配置，检查直接匹配
                status_lines.append("⚠ 未配置数据映射")
                for col in required_columns:
                    if col in self.app_data.imported_data.columns:
                        status_lines.append(f"✓ {col} (直接匹配)")
                    else:
                        status_lines.append(f"✗ {col} (需要映射)")

            # 显示当前可用列
            available_cols = list(self.app_data.imported_data.columns)
            status_lines.append(f"\n可用列: {', '.join(available_cols[:5])}" + ("..." if len(available_cols) > 5 else ""))

            self.mapping_status_text.insert(tk.END, "\n".join(status_lines))
            self.mapping_status_text.config(state=tk.DISABLED)

        except Exception as e:
            self.mapping_status_text.config(state=tk.NORMAL)
            self.mapping_status_text.delete(1.0, tk.END)
            self.mapping_status_text.insert(tk.END, f"映射状态更新错误: {e}")
            self.mapping_status_text.config(state=tk.DISABLED)

    def update_input_data_preview(self):
        """更新输入数据预览"""
        try:
            self.input_preview_text.config(state=tk.NORMAL)
            self.input_preview_text.delete(1.0, tk.END)

            if self.input_df is not None and len(self.input_df) > 0:
                # 显示前5行数据
                preview_df = self.input_df.head(5)
                preview_text = preview_df.to_string(max_cols=10, max_colwidth=15)
                self.input_preview_text.insert(tk.END, preview_text)
            else:
                self.input_preview_text.insert(tk.END, "无输入数据")

            self.input_preview_text.config(state=tk.DISABLED)

        except Exception as e:
            self.input_preview_text.config(state=tk.NORMAL)
            self.input_preview_text.delete(1.0, tk.END)
            self.input_preview_text.insert(tk.END, f"数据预览错误: {e}")
            self.input_preview_text.config(state=tk.DISABLED)

    def update_charging_data_preview(self):
        """更新充电基准数据预览"""
        try:
            self.charging_preview_text.config(state=tk.NORMAL)
            self.charging_preview_text.delete(1.0, tk.END)

            if self.df is not None and len(self.df) > 0:
                # 显示前5行数据
                preview_df = self.df.head(5)
                preview_text = preview_df.to_string(max_cols=10, max_colwidth=15)
                self.charging_preview_text.insert(tk.END, preview_text)
            else:
                self.charging_preview_text.insert(tk.END, "未加载充电基准数据")

            self.charging_preview_text.config(state=tk.DISABLED)

        except Exception as e:
            self.charging_preview_text.config(state=tk.NORMAL)
            self.charging_preview_text.delete(1.0, tk.END)
            self.charging_preview_text.insert(tk.END, f"基准数据预览错误: {e}")
            self.charging_preview_text.config(state=tk.DISABLED)

    def update_report_preview(self):
        """更新分析报告预览（前5列）"""
        try:
            self.report_preview_text.config(state=tk.NORMAL)
            self.report_preview_text.delete(1.0, tk.END)

            if self.result_df is None or len(self.result_df) == 0:
                self.report_preview_text.insert(tk.END, "暂无分析结果，请先完成数据分析")
                self.report_preview_text.config(state=tk.DISABLED)
                return

            # 获取前5列数据
            preview_df = self.result_df.iloc[:, :5]  # 前5列
            preview_rows = min(10, len(preview_df))  # 最多显示10行

            # 格式化显示
            preview_text = f"分析报告预览 (共 {len(self.result_df)} 行, {len(self.result_df.columns)} 列，显示前5列)\n"
            preview_text += "=" * 80 + "\n"

            # 显示列名
            col_names = " | ".join([f"{col:>12}" for col in preview_df.columns])
            preview_text += col_names + "\n"
            preview_text += "-" * 80 + "\n"

            # 显示数据行
            for i in range(preview_rows):
                row_data = []
                for col in preview_df.columns:
                    value = preview_df.iloc[i][col]
                    if pd.isna(value):
                        row_data.append("NaN".rjust(12))
                    elif isinstance(value, (int, float)):
                        row_data.append(f"{value:>12.3f}")
                    else:
                        row_data.append(f"{str(value):>12}")

                preview_text += " | ".join(row_data) + "\n"

            if len(self.result_df) > preview_rows:
                preview_text += f"... (还有 {len(self.result_df) - preview_rows} 行)\n"

            self.report_preview_text.insert(tk.END, preview_text)
            self.report_preview_text.config(state=tk.DISABLED)

        except Exception as e:
            self.report_preview_text.config(state=tk.NORMAL)
            self.report_preview_text.delete(1.0, tk.END)
            self.report_preview_text.insert(tk.END, f"报告预览错误: {e}")
            self.report_preview_text.config(state=tk.DISABLED)

    def select_charging_file(self):
        """选择充电基准文件"""
        filepath = filedialog.askopenfilename(
            title="选择充电基准文件",
            filetypes=[("CSV 文件", "*.csv"), ("Excel 文件", "*.xlsx"), ("所有文件", "*.*")],
            parent=self
        )

        if not filepath:
            return

        try:
            # 根据文件扩展名选择读取方法
            if filepath.lower().endswith('.xlsx'):
                self.df = pd.read_excel(filepath)
            else:
                # 尝试不同编码读取CSV
                try:
                    self.df = pd.read_csv(filepath, encoding='utf-8')
                except UnicodeDecodeError:
                    try:
                        self.df = pd.read_csv(filepath, encoding='gbk')
                    except UnicodeDecodeError:
                        self.df = pd.read_csv(filepath, encoding='latin-1')

            # 验证必要的列
            required_columns = ['temp', 'voltage', 'current']
            missing_columns = [col for col in required_columns if col not in self.df.columns]

            if missing_columns:
                messagebox.showerror("文件格式错误",
                                   f"充电基准文件缺少必要的列: {', '.join(missing_columns)}\n"
                                   f"需要包含: temp, voltage, current",
                                   parent=self)
                return

            self.charging_path = filepath

            # 保存到共享数据中，实现持久化
            self.app_data.charging_reference_data = self.df.copy()
            self.app_data.charging_reference_filepath = filepath

            # 更新UI显示
            self.charging_file_label.config(text=os.path.basename(filepath), foreground="green")
            self.update_status(f"已加载充电基准文件: {os.path.basename(filepath)}")

            # 更新充电基准数据预览
            self.update_charging_data_preview()

            messagebox.showinfo("文件加载成功",
                              f"充电基准文件加载成功!\n"
                              f"数据维度: {self.df.shape[0]} 行, {self.df.shape[1]} 列\n"
                              f"数据已保存，返回主界面后无需重新导入",
                              parent=self)

        except Exception as e:
            messagebox.showerror("文件加载错误", f"无法加载充电基准文件: {e}", parent=self)

    def start_processing(self):
        """启动处理线程"""
        if not self.validate_inputs():
            return

        self.toggle_buttons(False)
        self.update_progress(0, "开始处理...")

        # 启动后台处理线程
        threading.Thread(target=self.process_data, daemon=True).start()

    def validate_inputs(self):
        """验证输入数据"""
        if self.df is None:
            messagebox.showerror("缺少文件", "请先选择充电基准文件。", parent=self)
            return False

        if self.input_df is None:
            messagebox.showerror("缺少数据", "没有找到输入数据。请先在主界面导入数据。", parent=self)
            return False

        # 检查输入数据中的必要列
        required_input_columns = ['MinT', 'MaxV']  # 根据需求文档
        missing_input_columns = [col for col in required_input_columns if col not in self.input_df.columns]

        if missing_input_columns:
            messagebox.showerror("输入数据格式错误",
                               f"输入数据缺少必要的列: {', '.join(missing_input_columns)}\n"
                               f"请检查数据映射配置。",
                               parent=self)
            return False

        return True

    def toggle_buttons(self, state):
        """切换按钮状态"""
        button_state = tk.NORMAL if state else tk.DISABLED
        self.start_button.config(state=button_state)

    def update_progress(self, value, message=None):
        """更新进度条和状态信息"""
        self.progress.set(value)
        if message:
            self.status_label.config(text=message)
        self.update_idletasks()

    def update_status(self, message):
        """更新状态栏文本"""
        if self.status_label:
            self.status_label.config(text=message)

    def process_data(self):
        """核心数据处理流程（基于chargevoltage.py的正确算法）"""
        try:
            # === 数据加载阶段 ===
            self.update_progress(10, "正在加载基准数据...")

            # 数据清洗（基于chargevoltage.py的方法）
            self.df = self.df[
                ~self.df['temp'].astype(str).str.contains('<-30|>65', na=False) &
                self.df['temp'].notna()
            ]

            # 确保temp列为数值类型
            if self.df['temp'].dtype == 'object':
                self.df['temp'] = pd.to_numeric(
                    self.df['temp'].astype(str).str.replace('[^0-9.-]', '', regex=True),
                    errors='coerce'
                )
            self.df = self.df.dropna(subset=['temp'])

            # === 输入数据处理 ===
            self.update_progress(30, "正在处理输入数据...")

            # 列存在性检查
            required_cols = ['MinT', 'MaxV']
            missing_cols = [col for col in required_cols if col not in self.input_df.columns]
            if missing_cols:
                raise ValueError(f"缺少必要列: {', '.join(missing_cols)}")

            # 清理输入数据
            self.input_df = self.input_df.dropna(subset=required_cols).copy()

            # 创建温度标记列（改进后的方法）
            self.update_progress(40, "创建温度标记...")
            self.create_marker_column()

            # === 电流计算阶段（基于chargevoltage.py的算法）===
            current_list = []
            voltage_used_list = []
            prev_voltage = 0

            total_rows = len(self.input_df)
            for idx, row in self.input_df.iterrows():
                progress = 40 + int(50 * (idx / total_rows))
                self.update_progress(progress, f"计算进度: {idx+1}/{total_rows}...")

                temp = row['MinT']
                voltage = row['MaxV']

                # 电压更新策略（chargevoltage.py的核心逻辑）
                if voltage >= prev_voltage or row['a'] == 1:
                    current, voltage_used = self.calculate_current(temp, voltage)
                    prev_voltage = voltage_used
                else:
                    current, voltage_used = self.calculate_current(temp, prev_voltage-0.001)

                current_list.append(current)
                voltage_used_list.append(voltage_used)

            # === 结果保存 ===
            self.input_df['计算电流'] = current_list
            self.input_df['使用电压'] = voltage_used_list

            self.update_progress(95, "正在保存结果...")
            self.save_results(self.input_df)

            self.result_df = self.input_df
            self.update_progress(100, "处理完成!")

            # 更新报告预览
            self.update_report_preview()

            # 启用按钮
            self.plot_button.config(state=tk.NORMAL)
            self.report_button.config(state=tk.NORMAL)
            self.toggle_buttons(True)

            messagebox.showinfo("分析完成", "充电分析处理完成！可以查看图表和生成报告。", parent=self)

        except Exception as e:
            self.update_progress(0, "处理失败")
            self.toggle_buttons(True)
            messagebox.showerror("处理错误", f"数据处理时发生错误: {e}", parent=self)

    def create_marker_column(self):
        """创建温度标记列（基于chargevoltage.py的5℃区间算法）"""
        try:
            # 创建温度标记列（a）- 基于chargevoltage.py的逻辑
            a_values = []

            for i in range(len(self.input_df)):
                current_val = self.input_df['MinT'].iloc[i]
                current_base = (current_val // 5) * 5

                if i == 0:
                    prev_base = -float('inf')  # 第一行没有前一行
                else:
                    prev_val = self.input_df['MinT'].iloc[i-1]
                    prev_base = (prev_val // 5) * 5

                # chargevoltage.py的核心条件：
                # 当前值 >= 当前基准值 AND 前一个基准值 < 当前基准值
                condition = (current_val >= current_base) and (prev_base < current_base)
                a = 1 if condition else 0
                a_values.append(a)

            self.input_df['a'] = a_values

            # 创建温度区间标签（用于显示）
            self.input_df['温度区间'] = self.input_df['MinT'].apply(
                lambda x: f"{int((x//5)*5)}-{int((x//5)*5+5)}℃"
            )

            # 计算温度基准值（用于后续计算）
            self.input_df['温度基准值'] = self.input_df['MinT'].apply(
                lambda x: int((x//5)*5)
            )

        except Exception as e:
            raise Exception(f"创建温度标记列时出错: {e}")

    def calculate_current(self, temp, voltage):
        """基于chargevoltage.py的电流计算算法"""
        try:
            # === 第一步：温度层筛选 ===
            # 筛选温度小于等于目标温度的数据
            temp_filtered = self.df[self.df['temp'] <= temp]

            if len(temp_filtered) == 0:
                # 如果没有符合条件的温度，使用最小温度的数据
                temp_filtered = self.df[self.df['temp'] == self.df['temp'].min()]

            # === 第二步：温度层选择（双层逻辑）===
            # 获取最高温度值
            max_temp = temp_filtered['temp'].max()
            max_temp_data = temp_filtered[temp_filtered['temp'] == max_temp]

            # 如果最高温度数据点少于23个，选择次高温度补充
            if len(max_temp_data) < 23:
                # 获取次高温度
                remaining_temps = temp_filtered[temp_filtered['temp'] < max_temp]['temp'].unique()
                if len(remaining_temps) > 0:
                    second_max_temp = max(remaining_temps)
                    second_temp_data = temp_filtered[temp_filtered['temp'] == second_max_temp]

                    # 合并两个温度层的数据
                    combined_data = pd.concat([max_temp_data, second_temp_data])
                    temp_layer = combined_data.head(23)  # 最多取23个点
                else:
                    temp_layer = max_temp_data
            else:
                temp_layer = max_temp_data.head(23)

            # === 第三步：电压筛选 ===
            # 筛选电压大于目标电压的数据
            voltage_candidates = temp_layer[temp_layer['voltage'] > voltage]

            if not voltage_candidates.empty:
                # 选择电压最小的那个（刚好大于目标电压的第一个）
                min_voltage_idx = voltage_candidates['voltage'].idxmin()
                selected_row = voltage_candidates.loc[min_voltage_idx]

                voltage_used = selected_row['voltage']
                current_result = selected_row['current']
            else:
                # 如果没有大于目标电压的数据，使用温度层中电压最大的数据
                max_voltage_idx = temp_layer['voltage'].idxmax()
                max_voltage_row = temp_layer.loc[max_voltage_idx]
                voltage_used = max_voltage_row['voltage']
                current_result = max_voltage_row['current']

            return current_result, voltage_used

        except Exception as e:
            # 异常处理：返回默认值
            return 0, voltage

    def save_results(self, result_df):
        """保存分析结果，支持用户选择保存位置"""
        try:
            # 生成默认文件名
            if self.app_data.imported_filepath:
                base_name = os.path.splitext(os.path.basename(self.app_data.imported_filepath))[0]
                default_filename = f"{base_name}_charging_analysis_result.csv"
                initial_dir = os.path.dirname(self.app_data.imported_filepath)
            else:
                default_filename = "charging_analysis_result.csv"
                initial_dir = os.getcwd()

            # 让用户选择保存位置
            from tkinter import filedialog
            result_path = filedialog.asksaveasfilename(
                title="保存充电分析报告",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
                initialfile=default_filename,
                initialdir=initial_dir,
                parent=self
            )

            if not result_path:  # 用户取消了保存
                return

            # 保存结果
            result_df.to_csv(result_path, index=False, encoding='utf_8_sig')
            self.update_status(f"结果已保存到: {result_path}")

            # 显示成功消息
            messagebox.showinfo("保存成功",
                              f"充电分析报告已保存到:\n{result_path}",
                              parent=self)

        except Exception as e:
            error_msg = f"保存结果时出错: {e}"
            self.update_status(error_msg)
            messagebox.showerror("保存失败", error_msg, parent=self)

    def show_analysis_plot(self):
        """显示分析图表（带温度基准线）"""
        if self.result_df is None:
            messagebox.showwarning("无结果", "请先完成数据分析。", parent=self)
            return

        try:
            # 清空现有图表
            for widget in self.plot_frame.winfo_children():
                widget.destroy()

            # 创建Matplotlib图表
            fig, ax = plt.subplots(figsize=(12, 8))

            # 绘制计算电流曲线
            ax.plot(self.result_df.index, self.result_df['计算电流'],
                   'b-', label='计算电流', linewidth=2)

            # 如果存在实际电流，也绘制出来
            if '实际电流' in self.result_df.columns:
                ax.plot(self.result_df.index, self.result_df['实际电流'],
                       'r--', label='实际电流', linewidth=2)

            # 添加温度基准线（灰色虚线）
            temp_values = self.result_df['MinT']
            min_temp = int((temp_values.min() // 5) * 5)
            max_temp = int((temp_values.max() // 5 + 1) * 5)

            for temp_line in range(min_temp, max_temp + 1, 5):
                ax.axhline(y=temp_line, color='gray', linestyle='--', alpha=0.5, linewidth=0.8)

            # 标记温度阈值点（绿色圆点）
            threshold_points = self.result_df[self.result_df['a'] == 1]
            if not threshold_points.empty:
                ax.scatter(threshold_points.index, threshold_points['计算电流'],
                          color='green', s=50, label='温度阈值点', zorder=5)

            # 设置图表属性
            ax.set_xlabel('数据点索引')
            ax.set_ylabel('电流 (A)')
            ax.set_title('充电分析结果 - 电流曲线与温度基准线')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 添加统计信息框
            stats_text = self.generate_statistics()
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

            # 嵌入Tkinter界面
            canvas = FigureCanvasTkAgg(fig, self.plot_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(expand=True, fill=tk.BOTH)

            # 添加工具栏
            from matplotlib.backends.backend_tkagg import NavigationToolbar2Tk
            toolbar = NavigationToolbar2Tk(canvas, self.plot_frame)
            toolbar.update()

        except Exception as e:
            messagebox.showerror("图表错误", f"显示图表时发生错误: {e}", parent=self)

    def generate_statistics(self):
        """生成温度区间统计信息"""
        try:
            if self.result_df is None:
                return "无统计数据"

            # 按温度区间分组
            grouped = self.result_df.groupby('温度区间')['计算电流']

            stats_lines = ["温度区间统计:"]
            for interval, group in grouped:
                avg_current = group.mean()
                std_current = group.std()
                count = len(group)
                stats_lines.append(f"{interval}: 平均={avg_current:.2f}A, 标准差={std_current:.2f}A, 点数={count}")

            return '\n'.join(stats_lines)

        except Exception as e:
            return f"统计生成错误: {e}"

    def generate_report(self):
        """生成分析报告（预留接口）"""
        if self.result_df is None:
            messagebox.showwarning("无结果", "请先完成数据分析。", parent=self)
            return

        try:
            # 生成简单的文本报告
            report_lines = [
                "充电分析报告",
                "=" * 50,
                f"分析时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"数据点数: {len(self.result_df)}",
                "",
                "统计信息:",
                self.generate_statistics(),
                "",
                "温度范围:",
                f"最低温度: {self.result_df['MinT'].min():.2f}℃",
                f"最高温度: {self.result_df['MinT'].max():.2f}℃",
                "",
                "电流范围:",
                f"最小计算电流: {self.result_df['计算电流'].min():.2f}A",
                f"最大计算电流: {self.result_df['计算电流'].max():.2f}A",
                f"平均计算电流: {self.result_df['计算电流'].mean():.2f}A"
            ]

            # 保存报告
            if self.app_data.imported_filepath:
                base_path = os.path.splitext(self.app_data.imported_filepath)[0]
                report_path = f"{base_path}_charging_analysis_report.txt"
            else:
                report_path = "charging_analysis_report.txt"

            with open(report_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            messagebox.showinfo("报告生成", f"分析报告已保存到:\n{os.path.basename(report_path)}", parent=self)

        except Exception as e:
            messagebox.showerror("报告错误", f"生成报告时发生错误: {e}", parent=self)


# 独立测试代码
if __name__ == '__main__':
    class MockAppData:
        def __init__(self):
            # 创建模拟输入数据
            self.imported_data = pd.DataFrame({
                'Timestamp': pd.date_range('2023-01-01', periods=100, freq='1min'),
                'MinT': np.random.normal(25, 5, 100),  # 温度数据
                'MaxV': np.random.normal(4.0, 0.1, 100),  # 电压数据
                'Current': np.random.normal(10, 2, 100),  # 电流数据
                'SOC': np.linspace(20, 90, 100)  # SOC数据
            })
            self.imported_filepath = "test_input_data.csv"
            self.data_mapping_config = {
                "time_column": "Timestamp",
                "mappings": {
                    "MinT": "MinT",
                    "MaxV": "MaxV"
                }
            }

    class MockParent(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title("Mock Parent - 充电分析测试")
            self.geometry("300x200")
            self.app_data = MockAppData()

            ttk.Label(self, text="充电分析模块测试").pack(pady=20)
            ttk.Button(self, text="打开充电分析", command=self.open_charging_analysis).pack(pady=10)

            # 创建模拟充电基准数据文件
            self.create_mock_charging_data()

        def create_mock_charging_data(self):
            """创建模拟充电基准数据文件"""
            try:
                # 生成模拟充电基准数据
                temps = np.arange(-10, 60, 2.5)  # 温度范围
                voltages = np.arange(3.0, 4.5, 0.1)  # 电压范围

                data = []
                for temp in temps:
                    for voltage in voltages:
                        # 模拟电流计算（简单的线性关系）
                        current = max(0, (voltage - 3.0) * 10 + temp * 0.1 + np.random.normal(0, 0.5))
                        data.append({'temp': temp, 'voltage': voltage, 'current': current})

                df = pd.DataFrame(data)
                df.to_csv('mock_charging_data.csv', index=False)
                print("已创建模拟充电基准数据文件: mock_charging_data.csv")

            except Exception as e:
                print(f"创建模拟数据时出错: {e}")

        def open_charging_analysis(self):
            ChargingAnalysisWindow(self, self.app_data)

        def update_info_display(self, message):
            print(f"MockParent Info: {message}")

    app = MockParent()
    app.mainloop()