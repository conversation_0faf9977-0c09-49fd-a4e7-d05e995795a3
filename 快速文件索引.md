# 快速文件索引

## 📋 核心程序文件 (PyQt6版本)

| 文件名 | 功能 | 状态 |
|--------|------|------|
| `main_pyqt.py` | 主程序入口 | ✅ 活跃 |
| `data_mapping_pyqt.py` | 数据映射 | ⭐ 最新优化 |
| `data_filtering_pyqt.py` | 数据筛选 | ✅ 稳定 |
| `charging_analysis_pyqt.py` | 充电分析 | ✅ 稳定 |
| `sensor_layout_pyqt.py` | 温感布置 | ✅ 稳定 |
| `data_analysis_view_pyqt.py` | 数据分析视图 | ✅ 稳定 |
| `export_report_pyqt.py` | 报告导出 | ✅ 稳定 |
| `columnnamechange.py` | 列名处理工具 | ✅ 辅助 |

## 🧪 测试文件 (新增)

| 文件名 | 用途 |
|--------|------|
| `test_one_to_many_mapping.py` | 一对多映射测试 |
| `verify_mapping_logic.py` | 映射逻辑验证 |
| `debug_mapping_test.py` | 映射调试 |
| `diagnose_mapping_issue.py` | 问题诊断工具 |

## 📋 配置文件

| 文件名 | 用途 |
|--------|------|
| `requirements_pyqt.txt` | 依赖包列表 |
| `E08-data_mapping_config.json` | E08项目配置 |
| `E0X-data_mapping_config.json` | E0X项目配置 |
| `example_one_to_many_mapping.json` | 一对多映射示例 |
| `test_one_to_many_config.json` | 测试配置 |

## 📖 文档文件

| 文件名 | 内容 |
|--------|------|
| `最新文件索引_2025.md` | 最新完整索引 |
| `一对多数据映射使用说明.md` | 新功能使用说明 |
| `快速参考指南.md` | 快速使用指南 |
| `功能模块详细索引.md` | 模块详细说明 |

## 📁 目录结构

```
根目录/
├── 📄 核心程序文件 (8个)
├── 📄 测试文件 (4个)
├── 📄 配置文件 (5个)
├── 📄 文档文件 (10个)
├── 📁 tkinker/ (历史版本)
├── 📁 images/ (图像资源)
├── 📁 使用的数据/ (测试数据)
└── 📁 __pycache__/ (缓存文件)
```

## ⭐ 最新更新

### 一对多数据映射功能
- **文件**: `data_mapping_pyqt.py`
- **功能**: 一个CSV列映射为多个逻辑名称
- **语法**: `CSV列 → 名称1, 名称2, 名称3`
- **示例**: `BMS_BattTempMin → MinT, TempMin, 最小温度`

### 相关测试文件
- `test_one_to_many_mapping.py` - GUI测试程序
- `verify_mapping_logic.py` - 逻辑验证脚本
- `diagnose_mapping_issue.py` - 问题诊断工具

## 🚀 快速启动

1. **安装依赖**: `pip install -r requirements_pyqt.txt`
2. **运行程序**: `python main_pyqt.py`
3. **测试映射**: `python test_one_to_many_mapping.py`
4. **验证功能**: `python verify_mapping_logic.py`

## 📞 技术支持

- **主要开发语言**: Python 3.11+
- **GUI框架**: PyQt6
- **数据处理**: pandas, numpy
- **可视化**: matplotlib

---
*更新时间: 2025-06-30*
