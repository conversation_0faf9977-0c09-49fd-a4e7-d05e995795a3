#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据可视化程序启动器
自动检测环境并启动合适的版本
"""

import sys
import os
import subprocess
from pathlib import Path


def check_pyqt6():
    """检查PyQt6是否可用"""
    try:
        import PyQt6
        return True
    except ImportError:
        return False


def check_tkinter():
    """检查Tkinter是否可用"""
    try:
        import tkinter
        return True
    except ImportError:
        return False


def install_pyqt6():
    """安装PyQt6"""
    print("正在安装PyQt6...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt6>=6.4.0"])
        print("PyQt6安装成功！")
        return True
    except subprocess.CalledProcessError:
        print("PyQt6安装失败。")
        return False


def show_version_menu():
    """显示版本选择菜单"""
    print("\n" + "="*50)
    print("    数据可视化程序启动器")
    print("="*50)
    
    pyqt6_available = check_pyqt6()
    tkinter_available = check_tkinter()
    
    print(f"\n环境检测:")
    print(f"  PyQt6:   {'✓ 可用' if pyqt6_available else '✗ 不可用'}")
    print(f"  Tkinter: {'✓ 可用' if tkinter_available else '✗ 不可用'}")
    
    print(f"\n可用选项:")
    options = []
    
    if pyqt6_available:
        options.append(("1", "启动 PyQt6 版本 (推荐)", "main_pyqt.py"))
        print(f"  1. 启动 PyQt6 版本 (推荐)")
    
    if tkinter_available:
        options.append(("2", "启动 Tkinter 版本 (兼容)", "main_opt.py"))
        print(f"  2. 启动 Tkinter 版本 (兼容)")
    
    if not pyqt6_available:
        options.append(("3", "安装 PyQt6", None))
        print(f"  3. 安装 PyQt6")
    
    options.append(("q", "退出", None))
    print(f"  q. 退出")
    
    if not options:
        print("\n错误: 没有可用的GUI库！")
        return None
    
    while True:
        choice = input(f"\n请选择 (1-{len(options)}): ").strip().lower()
        
        for opt_key, opt_desc, opt_file in options:
            if choice == opt_key:
                if opt_key == "q":
                    return None
                elif opt_key == "3" and not pyqt6_available:
                    if install_pyqt6():
                        return show_version_menu()  # 重新显示菜单
                    else:
                        continue
                else:
                    return opt_file
        
        print("无效选择，请重新输入。")


def launch_program(script_name):
    """启动指定的程序"""
    script_path = Path(script_name)
    
    if not script_path.exists():
        print(f"错误: 找不到文件 {script_name}")
        return False
    
    print(f"\n正在启动 {script_name}...")
    
    try:
        # 使用subprocess启动程序
        subprocess.Popen([sys.executable, str(script_path)])
        print("程序启动成功！")
        return True
    except Exception as e:
        print(f"启动失败: {e}")
        return False


def main():
    """主函数"""
    print("数据可视化程序启动器")
    print("版本: 2.0")
    print("支持: PyQt6 和 Tkinter")
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 显示版本选择菜单
    selected_script = show_version_menu()
    
    if selected_script:
        success = launch_program(selected_script)
        if success:
            print("\n程序已在后台运行。")
            print("您可以关闭此窗口。")
        else:
            print("\n程序启动失败，请检查错误信息。")
            input("按回车键退出...")
    else:
        print("\n已取消启动。")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n用户中断，程序退出。")
        sys.exit(1)
    except Exception as e:
        print(f"\n启动器发生错误: {e}")
        input("按回车键退出...")
        sys.exit(1)
