# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import re
import os
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import numpy as np

# 导入其他功能模块窗口
# 注意：您需要确保这些 .py 文件与本文件在同一目录下
try:
    from sensor_layout import SensorLayoutWindow
    from export_report import ExportReportWindow
    from data_mapping import MappingWindow
    from data_filtering import FilteringWindow
    from charging_analysis import ChargingAnalysisWindow
    from data_analysis_view import DataAnalysisView # <--- 新增导入
except ImportError as e:
    # 更新错误消息以包含 data_analysis_view.py
    messagebox.showerror("模块导入错误", f"无法导入功能模块: {e}\n\n请确保 sensor_layout.py, export_report.py, data_mapping.py, data_filtering.py, charging_analysis.py 和 data_analysis_view.py 文件与主程序在同一个文件夹中。")
    exit()

# 配置 Matplotlib 以支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"注意: 无法设置 Matplotlib 默认中文字体: {e}")

class AppData:
    """应用的中央数据存储类"""
    def __init__(self):
        self.imported_data = None
        self.data_mapping_config = {}
        self.filtered_data_config = {}
        self.imported_filepath = None
        # 添加充电基准数据存储
        self.charging_reference_data = None
        self.charging_reference_filepath = None

class MainApplication(tk.Tk):
    """主应用程序窗口"""
    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data
        self.title("数据处理显示程序 (优化版)")
        self.geometry("800x600")

        # --- UI 布局 ---
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        # 左侧按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.LEFT, anchor=tk.NW, padx=10, pady=10)

        # 功能按钮配置
        buttons_config = [
            ("导入数据", self.open_import_data),
            ("数据映射", self.open_data_mapping),
            ("数据筛选", self.open_data_filtering),
            ("充电分析", self.open_charging_analysis),
            ("温感布置", self.open_sensor_layout),
            ("导出充电数据报告", self.open_export_report),
            ("数据分析", self.open_data_analysis) # <--- 修改按钮文本
        ]
        for text, command in buttons_config:
            ttk.Button(button_frame, text=text, command=command, width=20).pack(pady=5, fill=tk.X)

        # 右侧数据显示区域
        right_frame = ttk.Frame(main_frame)
        right_frame.pack(side=tk.LEFT, expand=True, fill=tk.BOTH, padx=10, pady=10)

        # 文件信息显示
        file_info_frame = ttk.LabelFrame(right_frame, text="当前数据文件信息", padding="5")
        file_info_frame.pack(fill=tk.X, pady=(0, 10))

        self.file_info_label = ttk.Label(file_info_frame, text="未导入数据文件", foreground="gray")
        self.file_info_label.pack(anchor=tk.W)

        # 数据预览区域
        preview_frame = ttk.LabelFrame(right_frame, text="数据预览 (前5行 + 后5行)", padding="5")
        preview_frame.pack(expand=True, fill=tk.BOTH)

        # 创建表格显示区域
        table_frame = ttk.Frame(preview_frame)
        table_frame.pack(expand=True, fill=tk.BOTH)

        # 创建Treeview表格
        self.data_table = ttk.Treeview(table_frame, show='headings')

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.data_table.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.data_table.xview)
        self.data_table.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 正确布局表格和滚动条
        self.data_table.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # 配置grid权重
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)

        # 绑定表格点击事件
        self.data_table.bind('<Button-1>', self.on_table_click)

        self.update_info_display("应用程序已启动。请先导入CSV数据文件。")

    def on_table_click(self, event):
        """处理表格点击事件"""
        # 获取点击的项目
        item = self.data_table.selection()[0] if self.data_table.selection() else None
        if not item:
            return

        # 获取点击的行数据
        values = self.data_table.item(item, 'values')
        if not values or len(values) == 0:
            return

        # 检查是否是分隔行
        if values[0] == '---':
            return

        # 检查是否有导入的数据
        if self.app_data.imported_data is None:
            return

        try:
            row_index = int(values[0])  # 第一列是行号
        except (ValueError, IndexError):
            return

        # 弹出确认对话框
        result = messagebox.askyesno(
            "删除行确认",
            f"确定要删除第 {row_index} 行数据吗？\n\n注意：此操作将永久删除该行数据。",
            parent=self
        )

        if result:
            self.delete_row(row_index)

    def delete_row(self, row_index):
        """删除指定行的数据"""
        try:
            # 从DataFrame中删除指定行
            if row_index < len(self.app_data.imported_data):
                self.app_data.imported_data = self.app_data.imported_data.drop(index=row_index).reset_index(drop=True)

                # 重新显示数据
                self.show_data_preview()

                # 更新信息显示
                self.update_info_display(f"已删除第 {row_index} 行数据。当前数据共 {len(self.app_data.imported_data)} 行。")

                messagebox.showinfo("删除成功", f"第 {row_index} 行数据已删除。", parent=self)
            else:
                messagebox.showwarning("删除失败", "指定的行不存在。", parent=self)

        except Exception as e:
            messagebox.showerror("删除失败", f"删除行时发生错误: {e}", parent=self)

    def update_info_display(self, message):
        """更新主界面的信息显示区域"""
        # 更新文件信息标签
        if hasattr(self, 'file_info_label'):
            if self.app_data.imported_filepath:
                filename = os.path.basename(self.app_data.imported_filepath)
                self.file_info_label.config(text=f"当前文件: {filename}", foreground="black")
            else:
                self.file_info_label.config(text="未导入数据文件", foreground="gray")

        # 如果有数据，显示数据预览图表
        if self.app_data.imported_data is not None:
            self.show_data_preview()
        else:
            self.show_welcome_message(message)

    def show_welcome_message(self, message):
        """显示欢迎信息"""
        # 清空表格内容
        for item in self.data_table.get_children():
            self.data_table.delete(item)

        # 设置简单的列结构显示欢迎信息
        self.data_table['columns'] = ['信息']
        self.data_table.heading('信息', text='系统信息')
        self.data_table.column('信息', width=400, anchor='center')

        # 插入欢迎信息
        self.data_table.insert('', 'end', values=[message])

    def show_data_preview(self):
        """显示数据预览表格（前5行+后5行）"""
        try:
            # 清空现有表格内容
            for item in self.data_table.get_children():
                self.data_table.delete(item)

            df = self.app_data.imported_data
            if df is None or len(df) == 0:
                return

            # 获取前5行和后5行数据
            if len(df) <= 10:
                preview_data = df.copy()
                row_labels = list(range(len(df)))
            else:
                head_data = df.head(5)
                tail_data = df.tail(5)
                preview_data = pd.concat([head_data, tail_data])
                # 创建行标签，显示原始行号
                head_labels = list(range(5))
                tail_labels = list(range(len(df)-5, len(df)))
                row_labels = head_labels + tail_labels

            # 设置表格列
            columns = ['行号'] + list(preview_data.columns)
            self.data_table['columns'] = columns

            # 配置列标题和宽度
            for col in columns:
                self.data_table.heading(col, text=col)
                if col == '行号':
                    self.data_table.column(col, width=60, anchor='center')
                else:
                    # 根据列名长度动态调整列宽，最小150像素
                    col_width = max(150, len(str(col)) * 12 + 20)
                    self.data_table.column(col, width=col_width, anchor='center')

            # 插入数据行
            for i, (_, row) in enumerate(preview_data.iterrows()):
                # 准备行数据：行号 + 数据值
                row_data = [str(row_labels[i])]
                for value in row:
                    if pd.isna(value):
                        row_data.append("NaN")
                    elif isinstance(value, float):
                        row_data.append(f"{value:.3f}")
                    else:
                        row_data.append(str(value))

                # 插入到表格
                item_id = self.data_table.insert('', 'end', values=row_data)

                # 如果是前5行和后5行的分界，添加视觉分隔
                if len(df) > 10 and i == 4:
                    # 插入分隔行
                    separator_data = ['---'] * len(columns)
                    self.data_table.insert('', 'end', values=separator_data, tags=('separator',))

            # 配置分隔行样式
            self.data_table.tag_configure('separator', background='lightgray')

        except Exception as e:
            # 如果表格显示失败，显示错误信息
            error_data = ['错误', f"数据预览显示错误: {e}"]
            self.data_table.insert('', 'end', values=error_data)

    def open_import_data(self):
        """打开文件对话框以导入CSV数据"""
        messagebox.showinfo("CSV格式要求", "请选择一个CSV格式的数据文件...", parent=self)
        filepath = filedialog.askopenfilename(
            title="选择CSV数据文件",
            filetypes=(("CSV 文件", "*.csv"), ("所有文件", "*.*")),
            parent=self)

        if not filepath:
            self.update_info_display("用户取消了文件选择。")
            return

        try:
            # 尝试用不同的编码格式读取文件，增加兼容性
            try:
                df = pd.read_csv(filepath, skip_blank_lines=False)
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(filepath, encoding='gbk', skip_blank_lines=False)
                except UnicodeDecodeError:
                    df = pd.read_csv(filepath, encoding='latin1', skip_blank_lines=False)

            # 清理列名中的单位等无关字符
            df.columns = [re.sub(r'\[.*?\]', '', col).strip() for col in df.columns]
            # 删除全为空值的行
            df.dropna(how='all', inplace=True)
            df.reset_index(drop=True, inplace=True)

            # 数据清理：删除除第一列外全为0的列
            original_cols = df.shape[1]
            cleaned_df = self.clean_data(df)
            removed_cols = original_cols - cleaned_df.shape[1]

            # 更新共享数据
            self.app_data.imported_data = cleaned_df
            self.app_data.imported_filepath = filepath

            nr, nc = cleaned_df.shape
            col_preview = ', '.join(cleaned_df.columns[:5])
            if nc > 5:
                col_preview += '...'

            # 更新显示（会自动显示数据预览图表）
            self.update_info_display(f"数据导入成功")

            info_msg = (f"成功导入: {os.path.basename(filepath)}\n"
                        f"原始数据: {df.shape[0]} 行, {original_cols} 列\n"
                        f"清理后数据: {nr} 行, {nc} 列\n"
                        f"删除了 {removed_cols} 个全零列\n"
                        f"前5个列名: {col_preview}")
            messagebox.showinfo("成功", info_msg, parent=self)

        except Exception as e:
            error_msg = f"导入文件时发生错误: {e}"
            messagebox.showerror("导入错误", error_msg, parent=self)
            self.update_info_display(error_msg)

    def clean_data(self, df):
        """清理数据：删除除第一列外全为0的列"""
        try:
            cleaned_df = df.copy()

            # 获取除第一列外的所有列
            if len(cleaned_df.columns) > 1:
                cols_to_check = cleaned_df.columns[1:]  # 跳过第一列

                # 找出全为0的列
                zero_cols = []
                for col in cols_to_check:
                    try:
                        # 尝试转换为数值类型并检查是否全为0
                        col_data = pd.to_numeric(cleaned_df[col], errors='coerce')
                        if col_data.notna().any() and (col_data == 0).all():
                            zero_cols.append(col)
                    except:
                        # 如果转换失败，跳过该列
                        continue

                # 删除全为0的列
                if zero_cols:
                    cleaned_df = cleaned_df.drop(columns=zero_cols)
                    print(f"删除了全零列: {zero_cols}")

            return cleaned_df

        except Exception as e:
            print(f"数据清理时出错: {e}")
            return df  # 如果清理失败，返回原始数据

    def check_data_before_opening_window(self, window_name):
        """在打开需要数据的窗口前检查数据是否已导入"""
        if self.app_data.imported_data is None:
            messagebox.showerror("数据缺失", f"请先导入数据文件，再进行“{window_name}”操作。", parent=self)
            return False
        return True

    def open_data_mapping(self):
        if self.check_data_before_opening_window("数据映射"):
            MappingWindow(self, self.app_data)

    def open_sensor_layout(self):
        if self.check_data_before_opening_window("温感布置"):
            # 检查Pillow库是否存在，因为此模块强依赖它
            try:
                from PIL import Image, ImageTk
            except ImportError:
                messagebox.showerror("依赖缺失", "Pillow (PIL) 库未安装或找不到。\n温感布置功能无法使用。\n请通过 'pip install Pillow' 命令安装。", parent=self)
                return
            SensorLayoutWindow(self, self.app_data)

    def open_data_filtering(self):
        if self.check_data_before_opening_window("数据筛选"):
            FilteringWindow(self, self.app_data)

    def open_charging_analysis(self):
        if self.check_data_before_opening_window("充电分析"):
            ChargingAnalysisWindow(self, self.app_data)

    def open_export_report(self):
        if self.check_data_before_opening_window("导出充电数据报告"):
            ExportReportWindow(self, self.app_data)

    def open_data_analysis(self): # <--- 修改此方法
        """打开数据分析与可视化窗口"""
        if self.check_data_before_opening_window("数据分析"):
            # 检查Matplotlib是否可用，因为DataAnalysisView强依赖它
            try:
                import matplotlib.pyplot as plt
                from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
            except ImportError:
                messagebox.showerror("依赖缺失", "Matplotlib 库未安装或找不到。\n数据分析功能无法使用。\n请通过 'pip install matplotlib' 命令安装。", parent=self)
                return
            DataAnalysisView(self, self.app_data)

if __name__ == "__main__":
    # 实例化共享数据对象
    app_data_instance = AppData()
    # 启动主程序
    app = MainApplication(app_data_instance)
    app.mainloop()
