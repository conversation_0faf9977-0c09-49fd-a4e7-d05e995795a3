#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据映射功能 - 验证原始数据是否被保留
"""

import pandas as pd
import json

def test_mapping_logic():
    """测试映射逻辑"""
    print("=== 数据映射逻辑测试 ===\n")
    
    # 创建测试数据
    original_data = pd.DataFrame({
        'Time': ['2024-01-01 10:00:00', '2024-01-01 10:01:00', '2024-01-01 10:02:00'],
        'BMS_BattTempMin': [25.5, 26.0, 25.8],
        'BMS_BattTempMax': [28.2, 28.5, 28.1],
        'BMS_PackSOC': [85.5, 85.2, 84.9]
    })
    
    print("原始数据:")
    print(original_data)
    print(f"原始列名: {list(original_data.columns)}")
    print()
    
    # 模拟映射配置
    config = {
        "time_column": "Time",
        "mappings": {
            "BMS_BattTempMin": ["MinT", "TempMin", "最小温度"],
            "BMS_BattTempMax": ["MaxT", "TempMax"],
            "BMS_PackSOC": "SOC"
        },
        "custom_variables": [
            {
                "name": "TempDiff",
                "formula": "MaxT - MinT"
            }
        ]
    }
    
    print("映射配置:")
    print(json.dumps(config, ensure_ascii=False, indent=2))
    print()
    
    # 应用映射逻辑（复制main_pyqt.py中的逻辑）
    data = original_data.copy()
    mapping_count = 0
    custom_vars_count = 0
    
    if 'mappings' in config and config['mappings']:
        for csv_col, logic_names in config['mappings'].items():
            if csv_col in data.columns:
                # 支持一对多映射：logic_names可以是字符串或列表
                if isinstance(logic_names, list):
                    # 多个逻辑名称
                    for logic_name in logic_names:
                        if logic_name.strip():
                            data[logic_name.strip()] = data[csv_col].copy()
                            mapping_count += 1
                            print(f"创建映射: {csv_col} → {logic_name.strip()}")
                else:
                    # 单个逻辑名称（向后兼容）
                    if logic_names.strip():
                        data[logic_names.strip()] = data[csv_col].copy()
                        mapping_count += 1
                        print(f"创建映射: {csv_col} → {logic_names.strip()}")
    
    print()
    
    # 处理自定义变量
    if 'custom_variables' in config and config['custom_variables']:
        import numpy as np
        for var_config in config['custom_variables']:
            var_name = var_config.get('name', '').strip()
            formula = var_config.get('formula', '').strip()
            
            if not var_name or not formula:
                continue
                
            try:
                safe_dict = {
                    'np': np, 'abs': abs, 'max': max,
                    'min': min, 'sum': sum, 'len': len
                }
                for col in data.columns:
                    safe_dict[col] = data[col]
                    
                result = eval(formula, {"__builtins__": {}}, safe_dict)
                data[var_name] = result
                custom_vars_count += 1
                print(f"创建自定义变量: {var_name} = {formula}")
                
            except Exception as e:
                print(f"计算自定义变量 {var_name} 时出错: {str(e)}")
                data[var_name] = np.nan
    
    print()
    print("映射后的数据:")
    print(data)
    print(f"映射后列名: {list(data.columns)}")
    print()
    
    print("=== 验证结果 ===")
    print(f"原始列数: {len(original_data.columns)}")
    print(f"映射后列数: {len(data.columns)}")
    print(f"新增列数: {len(data.columns) - len(original_data.columns)}")
    print(f"应用映射数: {mapping_count}")
    print(f"自定义变量数: {custom_vars_count}")
    print()
    
    # 检查原始列是否保留
    print("原始列保留检查:")
    for col in original_data.columns:
        if col in data.columns:
            # 检查数据是否相同
            if data[col].equals(original_data[col]):
                print(f"✓ {col}: 原始列保留且数据一致")
            else:
                print(f"✗ {col}: 原始列存在但数据不一致")
        else:
            print(f"✗ {col}: 原始列丢失")
    
    print()
    
    # 检查新创建的列
    print("新创建的列:")
    for col in data.columns:
        if col not in original_data.columns:
            print(f"+ {col}: 新创建的列")
    
    return data

if __name__ == "__main__":
    result_data = test_mapping_logic()
