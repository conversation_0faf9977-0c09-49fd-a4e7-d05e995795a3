# 一对多数据映射功能使用说明

## 🎯 功能概述

优化后的数据映射界面支持**一个CSV列同时映射为多个逻辑名称**，大大提高了数据处理的灵活性。

## ✨ 新功能特性

### 1. 一对多映射
- 一个CSV列可以映射为多个不同的逻辑名称
- 使用逗号分隔多个名称：`name1, name2, name3`
- 支持中英文混合命名
- 向后兼容原有的单一映射方式

### 2. 智能验证
- 自动检测重复的逻辑名称
- 验证自定义变量与映射名称的冲突
- 提供详细的错误提示

### 3. 映射预览
- 实时预览当前映射配置
- 显示映射统计信息
- 区分一对一和一对多映射

### 4. 增强的用户界面
- 清晰的操作提示
- 更宽的输入框适应多个名称
- 直观的配置预览功能

## 📝 使用方法

### 基本操作流程

1. **打开数据映射配置**
   - 在主程序中点击"数据映射"按钮
   - 或通过菜单选择数据映射功能

2. **配置时间列**
   - 从下拉列表中选择时间列
   - 程序会自动识别常见的时间列名

3. **设置数据映射**
   - 点击"增加映射行"添加新的映射关系
   - 选择CSV列名
   - 输入逻辑名称（支持多个，用逗号分隔）

4. **添加自定义变量**（可选）
   - 点击"添加自定义变量"
   - 输入变量名和计算公式
   - 公式中可以使用已映射的逻辑名称

5. **预览和验证**
   - 点击"预览映射"查看当前配置
   - 系统会自动验证配置的有效性

6. **应用配置**
   - 点击"应用并返回"保存并应用配置
   - 系统会显示应用结果统计

### 一对多映射示例

#### 示例1：温度数据映射
```
CSV列: BMS_BattTempMin
逻辑名称: MinT, TempMin, 最小温度
```
结果：创建三个相同数据的列，分别命名为 `MinT`、`TempMin`、`最小温度`

#### 示例2：电流数据映射
```
CSV列: BMS_BattCurrent  
逻辑名称: Batt_Current, 电流, Current
```
结果：创建三个相同数据的列，分别命名为 `Batt_Current`、`电流`、`Current`

#### 示例3：混合映射配置
```json
{
  "time_column": "Time",
  "mappings": {
    "BMS_BattTempMin": ["MinT", "TempMin", "最小温度"],
    "BMS_BattTempMax": ["MaxT", "TempMax", "最大温度"], 
    "BMS_PackSOC": "SOC",
    "BMS_BattCurrent": ["Batt_Current", "电流"]
  },
  "custom_variables": [
    {
      "name": "TempDiff",
      "formula": "MaxT - MinT"
    }
  ]
}
```

## 🔧 高级功能

### 配置文件管理
- **导入配置**：加载已保存的映射配置文件
- **保存配置**：将当前配置保存为JSON文件
- **配置格式**：支持新的一对多格式和旧的单一格式

### 自定义变量计算
- 使用已映射的逻辑名称进行计算
- 支持基本算术运算：`+`, `-`, `*`, `/`
- 支持NumPy函数：`abs`, `max`, `min`, `sum`, `len`

### 验证机制
- **重复检测**：防止逻辑名称重复定义
- **冲突检测**：检查自定义变量与映射名称的冲突
- **空值检测**：确保所有名称都不为空

## 📊 配置文件格式

### 新格式（支持一对多）
```json
{
  "time_column": "Time",
  "mappings": {
    "CSV列名1": ["逻辑名1", "逻辑名2", "逻辑名3"],
    "CSV列名2": "单个逻辑名",
    "CSV列名3": ["逻辑名4", "逻辑名5"]
  },
  "custom_variables": [
    {
      "name": "变量名",
      "formula": "计算公式"
    }
  ]
}
```

### 向后兼容
程序完全兼容原有的单一映射格式：
```json
{
  "mappings": {
    "CSV列名": "逻辑名"
  }
}
```

## 🎯 使用场景

### 1. 多语言支持
为同一数据创建中英文两套名称：
```
BMS_BattTempMin → MinT, 最小温度
BMS_BattTempMax → MaxT, 最大温度
```

### 2. 兼容性处理
为了兼容不同版本的分析算法：
```
BMS_PackSOC → SOC, SOC_Display, 电量显示
```

### 3. 数据分组
将相关数据映射为不同的分组名称：
```
BMS_CellMaxVolt → MaxV, CellVolt_Max, 单体最大电压
```

## ⚠️ 注意事项

1. **名称唯一性**：所有逻辑名称必须唯一，不能重复
2. **格式要求**：多个名称用英文逗号分隔，支持空格
3. **内存使用**：一对多映射会创建多个数据副本，注意内存使用
4. **公式引用**：自定义变量公式中只能使用已映射的逻辑名称

## 🧪 测试方法

1. **运行测试程序**：
   ```bash
   python test_one_to_many_mapping.py
   ```

2. **加载示例配置**：
   - 使用 `example_one_to_many_mapping.json` 作为测试配置
   - 验证一对多映射的效果

3. **验证结果**：
   - 检查生成的列数是否正确
   - 确认数据内容的一致性
   - 验证自定义变量的计算结果

## 📈 性能优化建议

1. **合理使用一对多映射**：避免创建过多不必要的数据副本
2. **及时清理**：删除不需要的映射关系
3. **配置复用**：保存常用的映射配置文件
4. **分批处理**：对于大数据文件，考虑分批处理

---

## 🔄 更新记录

- **2025-06-30**: 实现一对多数据映射功能
- **功能**: 支持一个CSV列映射为多个逻辑名称
- **改进**: 增强验证机制和用户界面
- **兼容**: 完全向后兼容原有配置格式

*此功能大大提高了数据处理的灵活性，特别适用于需要多种命名方式的复杂分析场景。*
