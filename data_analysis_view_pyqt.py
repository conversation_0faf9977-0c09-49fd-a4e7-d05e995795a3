# -*- coding: utf-8 -*-

import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import numpy as np
import pandas as pd
from itertools import cycle

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QGroupBox, QMessageBox, QCompleter,
    QScrollArea, QWidget, QFrame, QCheckBox, QSpinBox, QListWidget,
    QTreeWidget, QTreeWidgetItem, QSplitter, QGridLayout
)
from PyQt6.QtCore import Qt, QStringListModel, pyqtSignal
from PyQt6.QtGui import QFont

# 尝试配置Matplotlib以支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"无法设置Matplotlib中文字体: {e}")

class AutoCompleteLineEdit(QLineEdit):
    """自动完成输入框 - 严格参考data_analysis_view.py的AutocompleteEntry"""
    
    def __init__(self, parent=None, app_data=None):
        super().__init__(parent)
        self.app_data = app_data
        self.headers = []
        
        # 创建下拉列表框
        self.listbox = QListWidget(parent)
        self.listbox.setWindowFlags(Qt.WindowType.Popup)
        self.listbox.hide()
        
        # 连接信号
        self.textChanged.connect(self.on_text_changed)
        self.listbox.itemClicked.connect(self.on_listbox_select)
        self.listbox.itemDoubleClicked.connect(self.on_listbox_select)
        
        # 键盘事件处理
        self.installEventFilter(self)
        
    def refresh_headers(self):
        """从app_data更新列名列表"""
        if self.app_data and self.app_data.imported_data is not None:
            self.headers = list(self.app_data.imported_data.columns)
        else:
            self.headers = []
        self.update_listbox_content()
        
    def on_text_changed(self, text):
        """文本变化事件"""
        if text:
            self.update_listbox_content()
            if self.listbox.count() > 0:
                self.show_listbox()
            else:
                self.hide_listbox()
        else:
            self.hide_listbox()
            self.update_listbox_content()
            
    def update_listbox_content(self):
        """更新列表框内容"""
        self.listbox.clear()
        text = self.text().lower()
        if not self.headers:
            return
            
        if text:
            for header in self.headers:
                if text in header.lower():
                    self.listbox.addItem(header)
        else:
            for header in self.headers:
                self.listbox.addItem(header)
                
        if self.listbox.count() > 0:
            self.listbox.setCurrentRow(0)
            
    def show_listbox(self):
        """显示列表框"""
        if not self.listbox.isVisible():
            pos = self.mapToGlobal(self.rect().bottomLeft())
            self.listbox.move(pos)
            self.listbox.resize(self.width(), min(200, self.listbox.count() * 25))
            self.listbox.show()
            self.listbox.raise_()
            
    def hide_listbox(self):
        """隐藏列表框"""
        self.listbox.hide()
        
    def on_listbox_select(self, item):
        """列表框选择事件"""
        if item:
            self.setText(item.text())
            self.hide_listbox()
            self.setFocus()
            
    def eventFilter(self, obj, event):
        """事件过滤器处理键盘事件"""
        if obj == self and event.type() == event.Type.KeyPress:
            if event.key() == Qt.Key.Key_Up:
                if self.listbox.isVisible():
                    current_row = self.listbox.currentRow()
                    if current_row > 0:
                        self.listbox.setCurrentRow(current_row - 1)
                    return True
            elif event.key() == Qt.Key.Key_Down:
                if self.listbox.isVisible():
                    current_row = self.listbox.currentRow()
                    if current_row < self.listbox.count() - 1:
                        self.listbox.setCurrentRow(current_row + 1)
                    return True
            elif event.key() == Qt.Key.Key_Return or event.key() == Qt.Key.Key_Enter:
                if self.listbox.isVisible() and self.listbox.currentItem():
                    self.on_listbox_select(self.listbox.currentItem())
                    return True
                else:
                    self.hide_listbox()
                    return True
            elif event.key() == Qt.Key.Key_Escape:
                self.hide_listbox()
                return True
        return super().eventFilter(obj, event)
        
    def set_completion_list(self, completion_list):
        """设置自动完成列表"""
        self.headers = completion_list
        self.update_listbox_content()

class DataAnalysisDialog(QDialog):
    """数据分析与可视化对话框"""
    
    def __init__(self, parent=None, app_data=None):
        super().__init__(parent)
        self.app_data = app_data
        self.setWindowTitle("数据分析与可视化")
        self.setGeometry(100, 100, 1200, 800)
        
        self.headers = []
        self.x_axis_data_col = ""
        self.y_axis_data_cols = []
        self.marked_points_x = []
        
        self.fig, self.ax = None, None
        self.ax2 = None
        self.canvas_widget = None
        self.base_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        self.color_cycle = cycle(self.base_colors)
        
        self.create_widgets()
        self.load_data_for_analysis()
        
    def load_data_for_analysis(self):
        """加载数据进行分析"""
        # 检查是否有导入数据
        has_imported_data = self.app_data.imported_data is not None

        # 检查是否有充电分析结果
        has_charging_data = (hasattr(self.app_data, 'charging_analysis_results') and
                           self.app_data.charging_analysis_results and
                           'analysis_results' in self.app_data.charging_analysis_results)

        if not has_imported_data and not has_charging_data:
            QMessageBox.warning(self, "无数据", "没有导入数据或充电分析结果可供分析。")
            self.reject()
            return

        # 默认使用导入数据，如果没有则使用充电分析结果
        if has_imported_data:
            self.current_data = self.app_data.imported_data
            self.current_data_source = "原始数据"
        else:
            self.current_data = self.app_data.charging_analysis_results['analysis_results']
            self.current_data_source = "充电分析结果"

        self.headers = list(self.current_data.columns)
        self.x_axis_entry.refresh_headers()
        self.y_axis_entry.refresh_headers()

    def on_data_source_changed(self):
        """数据源切换事件处理"""
        # 检查哪个数据源被选中
        if hasattr(self, 'original_data_radio') and self.original_data_radio.isChecked():
            self.current_data = self.app_data.imported_data
            self.current_data_source = "原始数据"
        elif hasattr(self, 'charging_data_radio') and self.charging_data_radio.isChecked():
            self.current_data = self.app_data.charging_analysis_results['analysis_results']
            self.current_data_source = "充电分析结果"

        # 更新列名列表
        self.headers = list(self.current_data.columns)
        self.x_axis_entry.refresh_headers()
        self.y_axis_entry.refresh_headers()

        # 清空当前选择
        self.x_axis_entry.clear()
        self.y_axis_entry.clear()
        self.y_axis_data_cols.clear()
        self.update_y_axis_tree()

        # 清空图表
        self.clear_plot()
            
    def create_widgets(self):
        """创建界面组件"""
        main_layout = QHBoxLayout(self)
        
        # 左侧控制面板
        left_panel = QWidget()
        left_panel.setFixedWidth(350)
        left_layout = QVBoxLayout(left_panel)
        
        # 数据源选择区
        data_source_group = QGroupBox("1. 选择数据源")
        data_source_layout = QVBoxLayout(data_source_group)

        # 检查可用数据源
        has_imported_data = self.app_data.imported_data is not None
        has_charging_data = (hasattr(self.app_data, 'charging_analysis_results') and
                           self.app_data.charging_analysis_results and
                           'analysis_results' in self.app_data.charging_analysis_results)

        from PyQt6.QtWidgets import QRadioButton, QButtonGroup
        self.data_source_group = QButtonGroup()

        if has_imported_data:
            self.original_data_radio = QRadioButton("原始导入数据")
            self.original_data_radio.setChecked(True)
            self.data_source_group.addButton(self.original_data_radio, 0)
            data_source_layout.addWidget(self.original_data_radio)

        if has_charging_data:
            self.charging_data_radio = QRadioButton("充电分析结果")
            if not has_imported_data:  # 如果没有原始数据，默认选择充电分析结果
                self.charging_data_radio.setChecked(True)
            self.data_source_group.addButton(self.charging_data_radio, 1)
            data_source_layout.addWidget(self.charging_data_radio)

        # 连接数据源切换事件
        self.data_source_group.buttonClicked.connect(self.on_data_source_changed)

        left_layout.addWidget(data_source_group)

        # 输入区
        input_group = QGroupBox("2. 选择数据列")
        input_layout = QGridLayout(input_group)
        
        input_layout.addWidget(QLabel("X轴数据:"), 0, 0)
        self.x_axis_entry = AutoCompleteLineEdit(self, self.app_data)
        input_layout.addWidget(self.x_axis_entry, 0, 1)
        
        input_layout.addWidget(QLabel("Y轴数据:"), 1, 0)
        self.y_axis_entry = AutoCompleteLineEdit(self, self.app_data)
        input_layout.addWidget(self.y_axis_entry, 1, 1)
        
        self.add_y_btn = QPushButton("添加Y轴")
        self.add_y_btn.clicked.connect(self.add_y_axis)
        input_layout.addWidget(self.add_y_btn, 1, 2)
        
        input_layout.setColumnStretch(1, 1)
        left_layout.addWidget(input_group)
        
        # Y轴管理区
        y_manage_group = QGroupBox("3. 管理Y轴")
        y_manage_layout = QVBoxLayout(y_manage_group)

        self.y_axis_tree = QTreeWidget()
        self.y_axis_tree.setHeaderLabels(["Y轴列", "轴位置"])
        y_manage_layout.addWidget(self.y_axis_tree)

        y_btn_layout = QHBoxLayout()
        self.toggle_y_side_btn = QPushButton("切换左右轴")
        self.toggle_y_side_btn.clicked.connect(self.toggle_y_axis_side)
        self.remove_y_btn = QPushButton("删除选中")
        self.remove_y_btn.clicked.connect(self.remove_selected_y_axis)
        y_btn_layout.addWidget(self.toggle_y_side_btn)
        y_btn_layout.addWidget(self.remove_y_btn)
        y_manage_layout.addLayout(y_btn_layout)

        left_layout.addWidget(y_manage_group)
        
        # 图表设置区
        plot_control_group = QGroupBox("4. 图表设置与操作")
        plot_control_layout = QGridLayout(plot_control_group)

        plot_control_layout.addWidget(QLabel("图表标题:"), 0, 0)
        self.plot_title_entry = QLineEdit("数据分析图表")
        plot_control_layout.addWidget(self.plot_title_entry, 0, 1)

        plot_control_layout.addWidget(QLabel("X轴标签:"), 1, 0)
        self.x_label_entry = QLineEdit()
        plot_control_layout.addWidget(self.x_label_entry, 1, 1)

        plot_control_layout.addWidget(QLabel("Y1轴(左)标签:"), 2, 0)
        self.y1_label_entry = QLineEdit()
        plot_control_layout.addWidget(self.y1_label_entry, 2, 1)

        plot_control_layout.addWidget(QLabel("Y2轴(右)标签:"), 3, 0)
        self.y2_label_entry = QLineEdit()
        plot_control_layout.addWidget(self.y2_label_entry, 3, 1)

        plot_control_layout.setColumnStretch(1, 1)

        self.plot_btn = QPushButton("绘制/更新图表")
        self.plot_btn.clicked.connect(self.plot_data)
        plot_control_layout.addWidget(self.plot_btn, 4, 0, 1, 2)

        left_layout.addWidget(plot_control_group)
        
        # 标记点管理区
        mark_group = QGroupBox("5. 标记点管理与导出")
        mark_layout = QVBoxLayout(mark_group)

        # 标记点列表
        self.marked_points_list = QListWidget()
        mark_layout.addWidget(self.marked_points_list)

        # 标记点按钮
        mark_btn_layout = QHBoxLayout()
        self.remove_marker_btn = QPushButton("删除标记点")
        self.remove_marker_btn.clicked.connect(self.remove_marked_point)
        self.export_data_btn = QPushButton("导出标记数据")
        self.export_data_btn.clicked.connect(self.export_analyzed_data)
        mark_btn_layout.addWidget(self.remove_marker_btn)
        mark_btn_layout.addWidget(self.export_data_btn)

        mark_layout.addLayout(mark_btn_layout)
        left_layout.addWidget(mark_group)
        
        left_layout.addStretch()
        main_layout.addWidget(left_panel)
        
        # 右侧绘图区域
        self.plot_frame = QWidget()
        plot_layout = QVBoxLayout(self.plot_frame)
        
        # 创建matplotlib图表
        self.fig = Figure(figsize=(10, 6))
        self.ax = None
        self.ax2 = None
        self.canvas_widget = FigureCanvas(self.fig)

        # 连接点击事件
        self.canvas_widget.mpl_connect('button_press_event', self.on_plot_click)

        plot_layout.addWidget(self.canvas_widget)
        
        main_layout.addWidget(self.plot_frame)
        
    def add_y_axis(self):
        """添加Y轴 - 参考原始文件"""
        y_col = self.y_axis_entry.text().strip()
        if not y_col:
            QMessageBox.warning(self, "警告", "请输入Y轴列名")
            return

        if y_col not in self.headers:
            QMessageBox.warning(self, "警告", f"列 '{y_col}' 不存在")
            return

        # 检查是否已存在
        for col_info in self.y_axis_data_cols:
            if col_info['name'] == y_col:
                QMessageBox.warning(self, "警告", f"Y轴 '{y_col}' 已存在")
                return

        # 添加到列表 - 默认添加到左轴
        side = 'left'
        self.y_axis_data_cols.append({'name': y_col, 'side': side})

        # 更新树形控件显示
        self.update_y_axis_listbox()

        # 清空输入框
        self.y_axis_entry.clear()

    def update_y_axis_listbox(self):
        """更新Y轴列表框的显示 - 参考原始文件"""
        self.y_axis_tree.clear()
        for y_col in self.y_axis_data_cols:
            side_text = '左轴' if y_col['side'] == 'left' else '右轴'
            item = QTreeWidgetItem([y_col['name'], side_text])
            self.y_axis_tree.addTopLevelItem(item)

    def toggle_y_axis_side(self):
        """切换选中Y轴的左右位置 - 参考原始文件"""
        current_item = self.y_axis_tree.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要切换的Y轴")
            return

        col_name = current_item.text(0)

        # 找到对应的数据并切换side
        for col_info in self.y_axis_data_cols:
            if col_info['name'] == col_name:
                col_info['side'] = 'right' if col_info['side'] == 'left' else 'left'
                break

        # 更新显示
        self.update_y_axis_listbox()

    def remove_selected_y_axis(self):
        """删除选中的Y轴 - 参考原始文件"""
        current_item = self.y_axis_tree.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请选择要删除的Y轴")
            return

        col_name = current_item.text(0)

        # 从数据列表中删除
        self.y_axis_data_cols = [col for col in self.y_axis_data_cols if col['name'] != col_name]

        # 更新显示
        self.update_y_axis_listbox()

    def clear_all_y_axis(self):
        """清空所有Y轴 - 参考原始文件"""
        self.y_axis_data_cols.clear()
        self.update_y_axis_listbox()

    def plot_data(self):
        """生成图表 - 参考原始文件"""
        # 检查X轴数据
        x_col = self.x_axis_entry.text().strip()
        if not x_col:
            QMessageBox.warning(self, "警告", "请选择X轴数据列")
            return

        if x_col not in self.headers:
            QMessageBox.warning(self, "警告", f"X轴列 '{x_col}' 不存在")
            return

        # 检查Y轴数据
        if not self.y_axis_data_cols:
            QMessageBox.warning(self, "警告", "请至少添加一个Y轴数据列")
            return

        try:
            # 获取当前选择的数据源
            data = self.current_data
            x_data = data[x_col]

            # 清空之前的图表
            self.fig.clear()
            self.ax = self.fig.add_subplot(111)
            self.ax2 = None

            # 重置颜色循环
            self.color_cycle = cycle(self.base_colors)

            # 绘制左轴数据
            left_cols = [col for col in self.y_axis_data_cols if col['side'] == 'left']
            for col_info in left_cols:
                y_data = data[col_info['name']]
                color = next(self.color_cycle)
                self.ax.plot(x_data, y_data, label=col_info['name'], color=color)

            if left_cols:
                y1_label = self.y1_label_entry.text().strip() or 'Left Y-axis'
                self.ax.set_ylabel(y1_label)
                self.ax.legend(loc='upper left')

            # 绘制右轴数据
            right_cols = [col for col in self.y_axis_data_cols if col['side'] == 'right']
            if right_cols:
                self.ax2 = self.ax.twinx()
                for col_info in right_cols:
                    y_data = data[col_info['name']]
                    color = next(self.color_cycle)
                    self.ax2.plot(x_data, y_data, label=col_info['name'], color=color, linestyle='--')

                y2_label = self.y2_label_entry.text().strip() or 'Right Y-axis'
                self.ax2.set_ylabel(y2_label)
                self.ax2.legend(loc='upper right')

            # 设置标签和标题
            x_label = self.x_label_entry.text().strip() or x_col
            self.ax.set_xlabel(x_label)

            plot_title = self.plot_title_entry.text().strip() or "数据分析图表"
            self.ax.set_title(plot_title)

            # 添加标记点
            for mark_x in self.marked_points_x:
                self.ax.axvline(x=mark_x, color='red', linestyle=':', alpha=0.7)

            # 更新画布
            self.canvas_widget.draw()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"绘图时发生错误: {str(e)}")

    def clear_plot(self):
        """清空图表 - 参考原始文件"""
        self.fig.clear()
        self.ax = None
        self.ax2 = None
        self.canvas_widget.draw()

    def add_mark_point(self):
        """添加标记点 - 参考原始文件"""
        try:
            mark_x = float(self.mark_x_entry.text().strip())
            if mark_x not in self.marked_points_x:
                self.marked_points_x.append(mark_x)
                self.mark_x_entry.clear()

                # 如果图表已存在，重新绘制
                if self.ax is not None:
                    self.plot_data()
            else:
                QMessageBox.warning(self, "警告", "该标记点已存在")
        except ValueError:
            QMessageBox.warning(self, "警告", "请输入有效的数值")

    def clear_mark_points(self):
        """清空标记点 - 参考原始文件"""
        self.marked_points_x.clear()
        self.marked_points_list.clear()
        # 如果图表已存在，重新绘制
        if self.ax is not None:
            self.plot_data()

    def on_plot_click(self, event):
        """图表点击事件 - 参考原始文件"""
        if event.inaxes and event.button == 1:  # 左键点击
            x_coord = event.xdata
            if x_coord is not None:
                # 添加标记点
                if x_coord not in self.marked_points_x:
                    self.marked_points_x.append(x_coord)
                    # 更新标记点列表显示
                    self.marked_points_list.addItem(f"X = {x_coord:.2f}")
                    # 重新绘制图表
                    if self.ax is not None:
                        self.plot_data()

    def remove_marked_point(self):
        """删除选中的标记点 - 参考原始文件"""
        current_row = self.marked_points_list.currentRow()
        if current_row >= 0:
            # 从数据中删除
            del self.marked_points_x[current_row]
            # 从列表中删除
            self.marked_points_list.takeItem(current_row)
            # 重新绘制图表
            if self.ax is not None:
                self.plot_data()
        else:
            QMessageBox.warning(self, "警告", "请选择要删除的标记点")

    def export_analyzed_data(self):
        """导出分析数据 - 参考原始文件"""
        if not self.marked_points_x:
            QMessageBox.warning(self, "警告", "没有标记点可导出")
            return

        try:
            from PyQt6.QtWidgets import QFileDialog
            import pandas as pd

            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存分析数据", "", "CSV文件 (*.csv);;Excel文件 (*.xlsx)"
            )

            if file_path:
                # 获取当前数据
                current_data = self.app_data.filtered_data if self.app_data.filter_applied else self.app_data.imported_data
                if current_data is None:
                    QMessageBox.warning(self, "警告", "没有数据可导出")
                    return

                x_col = self.x_axis_entry.text().strip()
                if not x_col or x_col not in current_data.columns:
                    QMessageBox.warning(self, "警告", "X轴列无效")
                    return

                # 创建导出数据
                export_data = []
                for x_point in self.marked_points_x:
                    # 找到最接近的数据点
                    x_data = current_data[x_col]
                    closest_idx = (x_data - x_point).abs().idxmin()

                    # 获取该行的所有数据
                    row_data = {'标记点X': x_point, '最接近X值': x_data.iloc[closest_idx]}

                    # 添加Y轴数据
                    for y_col_info in self.y_axis_data_cols:
                        col_name = y_col_info['name']
                        if col_name in current_data.columns:
                            row_data[f'{col_name}({y_col_info["side"]})'] = current_data[col_name].iloc[closest_idx]

                    export_data.append(row_data)

                # 转换为DataFrame并保存
                df = pd.DataFrame(export_data)

                if file_path.endswith('.xlsx'):
                    df.to_excel(file_path, index=False)
                else:
                    df.to_csv(file_path, index=False, encoding='utf-8-sig')

                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}\n共导出 {len(export_data)} 个标记点的数据")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")
