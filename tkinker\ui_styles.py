def get_app_stylesheet():
    return """
        QM<PERSON><PERSON><PERSON><PERSON>, QDialog {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex; /* space above the group box */
            padding-top: 10px; /* space for the title */
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #4CAF50; /* Green */
            border: none;
            color: white;
            padding: 8px 16px;
            text-align: center;
            font-size: 14px;
            border-radius: 4px;
        }
        QPushButton:hover {
            background-color: #45a049;
        }
        QPushButton:pressed {
            background-color: #3d8b40;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QTableWidget {
            gridline-color: #d0d0d0;
            background-color: white;
            alternate-background-color: #f9f9f9;
        }
        QTableWidget::item:selected {
            background-color: #3daee9; /* Blue for selection */
            color: white;
        }
        QCheckBox {
            spacing: 5px;
            padding: 2px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 2px solid #cccccc;
            background-color: white;
            border-radius: 3px;
        }
        QCheckBox::indicator:checked {
            border: 2px solid #4CAF50; /* Green border for checked */
            background-color: #4CAF50; /* Green background for checked */
            border-radius: 3px;
        }
        QTabWidget::pane {
            border: 1px solid #cccccc;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e0e0e0; /* Light gray for non-selected tabs */
            padding: 8px 16px;
            margin-right: 2px;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        QTabBar::tab:selected {
            background-color: white; /* White for selected tab */
            border-bottom: 2px solid #2196F3; /* Blue underline for selected tab */
        }
        QTextEdit[readOnly="true"] { /* Style for read-only QTextEdit used for logs/summaries */
            background-color: #f0f0f0; /* Slightly different background for read-only text edits */
            border: 1px solid #c0c0c0;
        }
        /* Specific QLabel styles from main_pyqt - can be applied via objectName if needed */
        /* QLabel#data_info_label { color: gray; font-size: 12px; } */
        /* QLabel#filter_status_label { color: #999; font-size: 12px; } */
    """
