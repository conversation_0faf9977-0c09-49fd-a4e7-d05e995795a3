# -*- coding: utf-8 -*-
"""
导出充电分析界面 - PyQt6版本
支持多种格式导出、数据预览和确认功能
"""

import os
import json
from datetime import datetime
import pandas as pd
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton,
    QLabel, QGroupBox, QComboBox, QCheckBox, QTextEdit, QTableWidget,
    QTableWidgetItem, QTabWidget, QWidget, QMessageBox, QFileDialog,
    QProgressBar, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
from PyQt6.QtGui import QFont


class ExportChargingAnalysisWorker(QThread):
    """导出工作线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    export_completed = pyqtSignal(bool, str)  # success, message
    
    def __init__(self, app_data, export_config, file_path):
        super().__init__()
        self.app_data = app_data
        self.export_config = export_config
        self.file_path = file_path
        
    def run(self):
        """执行导出"""
        try:
            self.status_updated.emit("准备导出数据...")
            self.progress_updated.emit(10)
            
            # 检查是否有充电分析结果
            if not self.app_data.charging_analysis_results:
                self.export_completed.emit(False, "没有可导出的充电分析结果")
                return
            
            self.status_updated.emit("处理导出数据...")
            self.progress_updated.emit(30)
            
            # 准备导出数据
            export_data = self.prepare_export_data()
            
            self.status_updated.emit("写入文件...")
            self.progress_updated.emit(60)
            
            # 根据文件格式导出
            if self.file_path.endswith('.xlsx'):
                self.export_to_excel(export_data)
            elif self.file_path.endswith('.csv'):
                self.export_to_csv(export_data)
            elif self.file_path.endswith('.json'):
                self.export_to_json(export_data)
            else:
                self.export_completed.emit(False, "不支持的文件格式")
                return
            
            self.status_updated.emit("导出完成")
            self.progress_updated.emit(100)
            self.export_completed.emit(True, f"成功导出到: {self.file_path}")
            
        except Exception as e:
            self.export_completed.emit(False, f"导出失败: {str(e)}")
    
    def prepare_export_data(self):
        """准备导出数据"""
        results = self.app_data.charging_analysis_results
        export_data = {}
        
        # 分析结果数据
        if 'analysis_results' in results and self.export_config.get('include_analysis_data', True):
            analysis_df = results['analysis_results']
            if hasattr(analysis_df, 'to_dict'):
                export_data['analysis_data'] = analysis_df
            
        # 摘要信息
        if 'summary' in results and self.export_config.get('include_summary', True):
            export_data['summary'] = results['summary']
            
        # 添加导出元信息
        export_data['export_info'] = {
            'export_time': datetime.now().isoformat(),
            'source': '充电分析模块',
            'format_version': '1.0'
        }
        
        return export_data
    
    def export_to_excel(self, export_data):
        """导出到Excel"""
        with pd.ExcelWriter(self.file_path, engine='openpyxl') as writer:
            # 分析数据工作表
            if 'analysis_data' in export_data:
                analysis_df = export_data['analysis_data']
                analysis_df.to_excel(writer, sheet_name='充电分析数据', index=False)
            
            # 摘要工作表
            if 'summary' in export_data:
                summary_df = pd.DataFrame([export_data['summary']])
                summary_df.to_excel(writer, sheet_name='分析摘要', index=False)
            
            # 导出信息工作表
            if 'export_info' in export_data:
                info_df = pd.DataFrame([export_data['export_info']])
                info_df.to_excel(writer, sheet_name='导出信息', index=False)
    
    def export_to_csv(self, export_data):
        """导出到CSV"""
        if 'analysis_data' in export_data:
            analysis_df = export_data['analysis_data']
            analysis_df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
        else:
            # 如果没有分析数据，创建一个包含摘要的CSV
            summary_data = export_data.get('summary', {})
            df = pd.DataFrame([summary_data])
            df.to_csv(self.file_path, index=False, encoding='utf-8-sig')
    
    def export_to_json(self, export_data):
        """导出到JSON"""
        # 转换DataFrame为可序列化的格式
        json_data = {}
        for key, value in export_data.items():
            if hasattr(value, 'to_dict'):
                json_data[key] = value.to_dict('records')
            else:
                json_data[key] = value
        
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)


class ExportChargingAnalysisDialog(QDialog):
    """导出充电分析对话框"""
    
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.setWindowTitle("导出充电分析数据")
        self.setGeometry(200, 200, 800, 600)
        self.setModal(True)
        
        self.worker = None
        self.init_ui()
        self.check_data_availability()
        
    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 配置标签页
        self.create_config_tab()
        
        # 预览标签页
        self.create_preview_tab()
        
        # 导出标签页
        self.create_export_tab()
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.export_btn = QPushButton("开始导出")
        self.export_btn.clicked.connect(self.start_export)
        self.export_btn.setMinimumHeight(40)
        button_layout.addWidget(self.export_btn)
        
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(button_layout)
        
    def create_config_tab(self):
        """创建配置标签页"""
        config_widget = QWidget()
        layout = QVBoxLayout(config_widget)
        
        # 数据状态组
        status_group = QGroupBox("数据状态")
        status_layout = QVBoxLayout(status_group)
        
        self.data_status_label = QLabel("检查中...")
        status_layout.addWidget(self.data_status_label)
        
        layout.addWidget(status_group)
        
        # 导出选项组
        options_group = QGroupBox("导出选项")
        options_layout = QGridLayout(options_group)
        
        # 格式选择
        options_layout.addWidget(QLabel("导出格式:"), 0, 0)
        self.format_combo = QComboBox()
        self.format_combo.addItems(["Excel (.xlsx)", "CSV (.csv)", "JSON (.json)"])
        options_layout.addWidget(self.format_combo, 0, 1)
        
        # 内容选择
        self.include_analysis_data = QCheckBox("包含分析数据")
        self.include_analysis_data.setChecked(True)
        options_layout.addWidget(self.include_analysis_data, 1, 0)
        
        self.include_summary = QCheckBox("包含摘要信息")
        self.include_summary.setChecked(True)
        options_layout.addWidget(self.include_summary, 1, 1)
        
        layout.addWidget(options_group)
        layout.addStretch()
        
        self.tab_widget.addTab(config_widget, "导出配置")
        
    def create_preview_tab(self):
        """创建预览标签页"""
        preview_widget = QWidget()
        layout = QVBoxLayout(preview_widget)
        
        # 预览说明
        info_label = QLabel("数据预览 (前10行)")
        info_label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
        layout.addWidget(info_label)
        
        # 预览表格
        self.preview_table = QTableWidget()
        self.preview_table.setAlternatingRowColors(True)
        layout.addWidget(self.preview_table)
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新预览")
        refresh_btn.clicked.connect(self.update_preview)
        layout.addWidget(refresh_btn)
        
        self.tab_widget.addTab(preview_widget, "数据预览")
        
    def create_export_tab(self):
        """创建导出标签页"""
        export_widget = QWidget()
        layout = QVBoxLayout(export_widget)
        
        # 进度显示
        progress_group = QGroupBox("导出进度")
        progress_layout = QVBoxLayout(progress_group)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)
        
        self.status_label = QLabel("准备就绪")
        progress_layout.addWidget(self.status_label)
        
        layout.addWidget(progress_group)
        
        # 日志显示
        log_group = QGroupBox("导出日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        self.tab_widget.addTab(export_widget, "导出进度")
        
    def check_data_availability(self):
        """检查数据可用性"""
        if hasattr(self.app_data, 'charging_analysis_results') and self.app_data.charging_analysis_results:
            results = self.app_data.charging_analysis_results
            analysis_time = getattr(self.app_data, 'charging_analysis_timestamp', '未知')
            
            status_text = f"✓ 充电分析数据可用\n"
            status_text += f"分析时间: {analysis_time}\n"
            
            if 'analysis_results' in results:
                df = results['analysis_results']
                if hasattr(df, 'shape'):
                    status_text += f"数据规模: {df.shape[0]} 行 × {df.shape[1]} 列\n"
            
            if 'summary' in results:
                status_text += "✓ 包含摘要信息\n"
            
            self.data_status_label.setText(status_text)
            self.export_btn.setEnabled(True)
            
            # 更新预览
            self.update_preview()
        else:
            self.data_status_label.setText("✗ 没有可用的充电分析数据\n请先在充电分析界面完成分析")
            self.export_btn.setEnabled(False)
            
    def update_preview(self):
        """更新数据预览"""
        if not hasattr(self.app_data, 'charging_analysis_results') or not self.app_data.charging_analysis_results:
            return
            
        results = self.app_data.charging_analysis_results
        if 'analysis_results' not in results:
            return
            
        df = results['analysis_results']
        if not hasattr(df, 'head'):
            return
            
        # 显示前10行
        preview_df = df.head(10)
        
        self.preview_table.setRowCount(len(preview_df))
        self.preview_table.setColumnCount(len(preview_df.columns))
        self.preview_table.setHorizontalHeaderLabels(preview_df.columns.tolist())
        
        for i, (_, row) in enumerate(preview_df.iterrows()):
            for j, col_name in enumerate(preview_df.columns):
                value = row[col_name]
                if pd.isna(value):
                    item_text = "NaN"
                elif isinstance(value, float):
                    item_text = f"{value:.3f}"
                else:
                    item_text = str(value)
                
                item = QTableWidgetItem(item_text)
                self.preview_table.setItem(i, j, item)
        
        self.preview_table.resizeColumnsToContents()
        
    def start_export(self):
        """开始导出"""
        # 选择保存位置
        format_text = self.format_combo.currentText()
        if "Excel" in format_text:
            file_filter = "Excel文件 (*.xlsx)"
            default_ext = ".xlsx"
        elif "CSV" in format_text:
            file_filter = "CSV文件 (*.csv)"
            default_ext = ".csv"
        else:  # JSON
            file_filter = "JSON文件 (*.json)"
            default_ext = ".json"
        
        default_filename = f"charging_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}{default_ext}"
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存充电分析数据",
            default_filename,
            file_filter
        )
        
        if not file_path:
            return
        
        # 准备导出配置
        export_config = {
            'include_analysis_data': self.include_analysis_data.isChecked(),
            'include_summary': self.include_summary.isChecked(),
            'format': format_text
        }
        
        # 切换到导出标签页
        self.tab_widget.setCurrentIndex(2)
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.export_btn.setEnabled(False)
        
        # 启动导出线程
        self.worker = ExportChargingAnalysisWorker(self.app_data, export_config, file_path)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.status_updated.connect(self.status_label.setText)
        self.worker.export_completed.connect(self.on_export_completed)
        self.worker.start()
        
    def on_export_completed(self, success, message):
        """导出完成处理"""
        self.progress_bar.setVisible(False)
        self.export_btn.setEnabled(True)
        
        self.log_text.append(f"[{datetime.now().strftime('%H:%M:%S')}] {message}")
        
        if success:
            QMessageBox.information(self, "导出成功", message)
        else:
            QMessageBox.critical(self, "导出失败", message)
