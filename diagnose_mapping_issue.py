#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断一对多数据映射问题
帮助确定问题出现在哪个环节
"""

def diagnose_mapping_issue():
    """诊断映射问题"""
    print("🔍 一对多数据映射问题诊断")
    print("=" * 50)
    
    print("\n📋 请按照以下步骤进行诊断：")
    
    print("\n1️⃣ 检查数据映射配置")
    print("   - 打开数据映射界面")
    print("   - 配置一个一对多映射，例如：")
    print("     CSV列: BMS_BattTempMin")
    print("     逻辑名称: MinT, TempMin, 最小温度")
    print("   - 点击'预览映射'按钮，检查配置是否正确显示")
    
    print("\n2️⃣ 检查映射应用结果")
    print("   - 点击'应用并返回'")
    print("   - 查看成功提示信息中的统计数据")
    print("   - 确认显示的列数变化是否正确")
    
    print("\n3️⃣ 检查主界面数据显示")
    print("   - 返回主界面后，查看数据预览表格")
    print("   - 检查表格列标题是否包含新的映射列")
    print("   - 原始列（如BMS_BattTempMin）应该仍然存在")
    print("   - 新增列（如MinT, TempMin, 最小温度）应该出现")
    
    print("\n4️⃣ 检查数据分析界面")
    print("   - 打开数据分析视图")
    print("   - 在X轴或Y轴选择框中输入字符")
    print("   - 检查下拉列表是否包含新的映射列名")
    print("   - 确认可以选择映射后的列名进行分析")
    
    print("\n5️⃣ 检查数据筛选界面")
    print("   - 打开数据筛选界面")
    print("   - 检查列选择列表是否包含新的映射列")
    print("   - 确认可以选择映射后的列进行筛选")
    
    print("\n6️⃣ 检查数据导出功能")
    print("   - 尝试导出数据")
    print("   - 检查导出的文件是否包含所有映射列")
    print("   - 确认数据内容的一致性")
    
    print("\n🔧 常见问题及解决方案：")
    
    print("\n❌ 问题1: 映射配置无法保存")
    print("   原因: 可能存在重复的逻辑名称")
    print("   解决: 检查映射配置，确保所有逻辑名称唯一")
    
    print("\n❌ 问题2: 主界面不显示新列")
    print("   原因: 界面可能没有刷新")
    print("   解决: 重新导入数据文件，或重启程序")
    
    print("\n❌ 问题3: 数据分析界面看不到新列")
    print("   原因: 列名列表可能没有更新")
    print("   解决: 关闭并重新打开数据分析界面")
    
    print("\n❌ 问题4: 筛选后数据丢失映射列")
    print("   原因: 先筛选后映射，或筛选数据没有同步更新")
    print("   解决: 先应用数据映射，再进行数据筛选")
    
    print("\n❌ 问题5: 映射列数据不一致")
    print("   原因: 映射逻辑错误或数据处理问题")
    print("   解决: 检查映射配置，重新应用映射")
    
    print("\n💡 调试技巧：")
    
    print("\n🔍 技巧1: 使用预览功能")
    print("   - 在应用映射前，先使用'预览映射'功能")
    print("   - 检查配置是否符合预期")
    print("   - 确认映射关系和统计信息")
    
    print("\n🔍 技巧2: 分步验证")
    print("   - 先配置简单的一对一映射测试")
    print("   - 确认基本功能正常后，再测试一对多映射")
    print("   - 逐步增加映射复杂度")
    
    print("\n🔍 技巧3: 检查配置文件")
    print("   - 保存映射配置到文件")
    print("   - 用文本编辑器打开配置文件")
    print("   - 检查JSON格式是否正确")
    
    print("\n🔍 技巧4: 重启程序")
    print("   - 如果遇到界面显示问题")
    print("   - 保存当前配置后重启程序")
    print("   - 重新加载配置文件测试")
    
    print("\n📝 问题报告模板：")
    print("   如果问题仍然存在，请提供以下信息：")
    print("   1. 具体的操作步骤")
    print("   2. 预期的结果")
    print("   3. 实际的结果")
    print("   4. 使用的映射配置")
    print("   5. 错误信息（如果有）")
    print("   6. 在哪个界面发现的问题")
    
    print("\n✅ 验证清单：")
    verification_items = [
        "数据映射配置界面支持逗号分隔的多个名称",
        "预览映射功能显示正确的一对多关系",
        "应用映射后主界面显示所有列（原始+映射）",
        "数据分析界面可以选择映射后的列名",
        "数据筛选界面包含所有映射后的列",
        "导出的数据文件包含所有映射列",
        "映射列的数据与原始列完全一致",
        "自定义变量可以使用映射后的列名"
    ]
    
    for i, item in enumerate(verification_items, 1):
        print(f"   □ {i}. {item}")
    
    print(f"\n🎯 如果以上{len(verification_items)}项都能正常工作，说明一对多映射功能完全正常。")
    print("   如果某些项目有问题，请重点检查对应的功能模块。")

def create_test_config():
    """创建测试配置文件"""
    test_config = {
        "time_column": "Time",
        "mappings": {
            "BMS_BattTempMin": ["MinT", "TempMin", "最小温度"],
            "BMS_BattTempMax": ["MaxT", "TempMax"],
            "BMS_PackSOC": "SOC",
            "BMS_BattCurrent": ["Batt_Current", "电流"]
        },
        "custom_variables": [
            {
                "name": "TempDiff",
                "formula": "MaxT - MinT"
            }
        ]
    }
    
    import json
    with open("test_one_to_many_config.json", "w", encoding="utf-8") as f:
        json.dump(test_config, f, ensure_ascii=False, indent=2)
    
    print("✅ 已创建测试配置文件: test_one_to_many_config.json")
    print("   您可以在数据映射界面中导入此配置进行测试")

if __name__ == "__main__":
    diagnose_mapping_issue()
    print("\n" + "="*50)
    create_test_config()
    print("\n🎉 诊断完成！请按照上述步骤逐一检查。")
