#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据映射功能
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_mapping():
    """测试数据映射功能"""
    print("=== 测试数据映射功能 ===")
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'col1': [1, 2, 3, 4, 5],
        'col2': [10, 20, 30, 40, 50],
        'col3': [100, 200, 300, 400, 500],
        'time': [0, 1, 2, 3, 4]
    })
    
    print("原始数据:")
    print(test_data)
    print(f"原始列名: {list(test_data.columns)}")
    
    # 模拟数据映射配置
    mapping_config = {
        'time_column': 'time',
        'mappings': {
            'col1': 'MinT',
            'col2': 'MaxV',
            'col3': 'CellVoltage_Max'
        },
        'custom_variables': [
            {
                'name': 'test_var1',
                'formula': 'MinT + MaxV'  # 使用映射后的名称
            },
            {
                'name': 'test_var2', 
                'formula': 'col1 + col2'  # 使用原始名称
            },
            {
                'name': 'test_var3',
                'formula': 'CellVoltage_Max * 2'  # 使用映射后的名称
            }
        ]
    }
    
    # 应用数据映射逻辑（模拟main_pyqt.py中的apply_data_mapping方法）
    data = test_data.copy()
    mapping_count = 0
    custom_vars_count = 0
    
    # 1. 应用数据映射（新增列，不改名）
    if 'mappings' in mapping_config and mapping_config['mappings']:
        for csv_col, logic_name in mapping_config['mappings'].items():
            if csv_col in data.columns and logic_name.strip():
                # 新增映射列，保留原始列
                data[logic_name] = data[csv_col].copy()
                mapping_count += 1
                print(f"映射: {csv_col} -> {logic_name}")
    
    print(f"\n映射后的数据 (新增了 {mapping_count} 个映射列):")
    print(data)
    print(f"映射后列名: {list(data.columns)}")
    
    # 2. 应用自定义变量计算
    if 'custom_variables' in mapping_config and mapping_config['custom_variables']:
        for var_config in mapping_config['custom_variables']:
            var_name = var_config.get('name', '').strip()
            formula = var_config.get('formula', '').strip()
            
            if not var_name or not formula:
                continue
            
            try:
                # 创建安全的计算环境
                safe_dict = {
                    'np': np,
                    'abs': abs,
                    'max': max,
                    'min': min,
                    'sum': sum,
                    'len': len
                }
                
                # 添加所有数据列到计算环境（包括原始列和映射后的列）
                for col in data.columns:
                    safe_dict[col] = data[col]
                
                # 计算自定义变量
                result = eval(formula, {"__builtins__": {}}, safe_dict)
                data[var_name] = result
                custom_vars_count += 1
                print(f"自定义变量: {var_name} = {formula}")
                
            except Exception as e:
                print(f"计算自定义变量 {var_name} 时出错: {str(e)}")
                data[var_name] = np.nan
    
    print(f"\n最终数据 (新增了 {custom_vars_count} 个自定义变量):")
    print(data)
    print(f"最终列名: {list(data.columns)}")
    
    # 验证结果
    print("\n=== 验证结果 ===")
    
    # 检查原始列是否保留
    original_cols = ['col1', 'col2', 'col3', 'time']
    for col in original_cols:
        if col in data.columns:
            print(f"✓ 原始列 '{col}' 已保留")
        else:
            print(f"✗ 原始列 '{col}' 丢失")
    
    # 检查映射列是否新增
    mapped_cols = ['MinT', 'MaxV', 'CellVoltage_Max']
    for col in mapped_cols:
        if col in data.columns:
            print(f"✓ 映射列 '{col}' 已新增")
        else:
            print(f"✗ 映射列 '{col}' 未新增")
    
    # 检查自定义变量是否计算正确
    custom_vars = ['test_var1', 'test_var2', 'test_var3']
    for var in custom_vars:
        if var in data.columns:
            print(f"✓ 自定义变量 '{var}' 已计算")
        else:
            print(f"✗ 自定义变量 '{var}' 未计算")
    
    # 检查数值是否正确
    if 'test_var1' in data.columns and 'MinT' in data.columns and 'MaxV' in data.columns:
        expected = data['MinT'] + data['MaxV']
        if data['test_var1'].equals(expected):
            print("✓ test_var1 计算正确 (使用映射后名称)")
        else:
            print("✗ test_var1 计算错误")
    
    if 'test_var2' in data.columns:
        expected = data['col1'] + data['col2']
        if data['test_var2'].equals(expected):
            print("✓ test_var2 计算正确 (使用原始名称)")
        else:
            print("✗ test_var2 计算错误")
    
    print("\n测试完成!")
    return data

if __name__ == "__main__":
    test_data_mapping()
