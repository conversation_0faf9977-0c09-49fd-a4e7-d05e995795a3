# 数据可视化程序 - 项目索引

## 📖 项目概述

这是一个基于Python PyQt6开发的数据可视化分析程序，专门用于处理和分析CSV格式的数据文件。程序提供了完整的数据处理流程，从数据导入、筛选、分析到结果导出，具有直观的图形用户界面和丰富的分析功能。

## 🚀 快速开始

### 自动启动（推荐）
```bash
python 启动程序.py
```

### 手动启动
```bash
# PyQt6版本（推荐）
python main_pyqt.py

# Tkinter版本（兼容）
python main_opt.py
```

### 依赖安装
```bash
# 安装PyQt6（推荐）
pip install PyQt6>=6.4.0

# 安装其他依赖
pip install -r requirements.txt
```

### 基本使用流程
1. **导入数据** - 点击"导入CSV数据"按钮选择数据文件
2. **预览数据** - 查看导入数据的前5行和后5行
3. **选择功能** - 根据需要选择相应的分析模块
4. **处理数据** - 使用各种分析工具处理数据
5. **导出结果** - 保存分析结果或生成报告

## 📚 文档索引

### 📋 核心文档
- **[项目目录索引.md](./项目目录索引.md)** - 完整的项目文件结构说明
- **[功能模块详细索引.md](./功能模块详细索引.md)** - 各功能模块的详细介绍
- **[代码结构索引.md](./代码结构索引.md)** - 代码组织结构和快速查找指南

### 📖 原有文档
- **[优化完成说明.md](./优化完成说明.md)** - 项目优化历程说明
- **[数据处理显示程序.md](./数据处理显示程序.md)** - 程序功能详细说明

## 🔧 主要功能模块

### 🏠 主程序 (main_pyqt.py)
- 数据导入和预览
- 表格显示和行删除
- 功能模块导航

### 🎨 UI样式 (ui_styles.py)
- 提供全局统一样式表，确保界面风格一致。

### 🔍 数据筛选 (data_filtering_pyqt.py)
- 信号列选择筛选
- 筛选配置管理
- 自定义保存位置

### ⚡ 充电分析 (charging_analysis_pyqt.py)
- 充电数据深度分析
- 可视化图表生成 (Note: Plotting is in data_analysis_view_pyqt.py, this module focuses on data calculation)
- 分析结果表格展示

### 🗺️ 数据映射 (data_mapping_pyqt.py)
- 列名映射管理
- 数据对应关系处理

### 🌡️ 温感布置 (sensor_layout_pyqt.py)
- 传感器布局可视化
- 温度数据空间分析

### 📊 数据分析视图 (data_analysis_view_pyqt.py)
- 多维度数据可视化 (Matplotlib charts)
- 分析结果交互式查看

### 📄 报告导出 (export_report_pyqt.py)
- 根据详细需求（文档1.7）进行充电数据处理和计算。
- 生成包含关键性能指标、SOC时间分析、分段统计的文本报告。

## 🛠️ 技术栈

### 核心技术
- **GUI框架**: PyQt6
- **数据处理**: Pandas
- **图表绘制**: Matplotlib
- **数值计算**: NumPy
- **图像处理**: PIL/Pillow

### 系统要求
- Python 3.8+ (PyQt6版本)
- 详见 `requirements.txt` 文件 (应创建此文件并列出所有依赖)

## 📁 项目结构

```
📦 数据可视化程序
├── 📄 启动程序.py                    # 智能启动器 (if exists)
├── 📄 main_pyqt.py                   # PyQt6版主程序
├── 📄 ui_styles.py                  # 全局UI样式表
├── 📄 data_filtering_pyqt.py         # PyQt6数据筛选模块
├── 📄 charging_analysis_pyqt.py      # PyQt6充电分析模块
├── 📄 data_mapping_pyqt.py           # 数据映射模块
├── 📄 sensor_layout_pyqt.py          # 温感布置模块
├── 📄 data_analysis_view_pyqt.py     # 数据分析视图模块
├── 📄 export_report_pyqt.py          # 报告导出模块
├── 📄 requirements.txt               # 依赖包列表 (建议创建)
├── 📄 E08-data_mapping_config.json   # 数据映射配置 (示例，用户生成)
├── 📁 images/                        # 图像资源目录
├── 📁 __pycache__/                   # Python缓存目录
└── 📚 文档索引/
    ├── 📄 README.md                  # 项目总索引 (本文件)
    ├── 📄 项目目录索引.md             # 目录结构说明
    ├── 📄 功能模块详细索引.md         # 模块功能详解
    └── 📄 代码结构索引.md             # 代码结构指南
    └── 📄 数据处理显示程序.md         # 原始需求文档
```
(Note: Tkinter related files like `main_opt.py`, `data_filtering.py`, `charging_analysis.py` are assumed to be superseded by PyQt6 versions based on current file structure.)

## 🎯 核心特性

### ✨ 用户体验
- **现代界面** - 基于PyQt6的专业GUI设计，应用全局样式。
- **智能启动** - (如果`启动程序.py`存在并实现此功能)
- **滚动支持** - 长内容界面支持垂直滚动。
- **智能列宽** - 表格列宽根据内容自动调整。
- **安全操作** - 重要操作有确认对话框保护。

### 🔧 功能完整
- **数据导入** - 支持CSV文件导入和预览，包含编码自动尝试和数据清洗。
- **智能筛选** - 筛选结果全局应用到所有分析模块。
- **多种分析** - 优化的充电分析算法；灵活的数据可视化。
- **详细报告** - `export_report_pyqt.py`模块实现特定需求的充电数据报告。
- **配置管理** - 支持数据映射和筛选配置的保存与加载。

### 🛡️ 稳定可靠
- **错误处理** - 完善的异常处理机制和用户提示。
- **模块化设计** - 清晰的代码结构，便于维护和扩展。
- **数据一致性** - 通过共享`AppData`对象确保各模块数据同步。

## 🔄 最近更新

### Jules Refactoring (YYYY-MM-DD) - (Current Date)
- **全面代码分析与重构**: 提升代码质量、可读性和可维护性。
- **主程序优化 (`main_pyqt.py`)**: 统一UI设置方法，整合数据导入逻辑，移除重复代码。
- **`AppData` 精简**: 移除冗余和未使用的属性，明确各属性用途。
- **样式集中化 (`ui_styles.py`)**: 创建并应用全局样式表，统一界面风格。
- **`export_report_pyqt.py` 重构**: 大幅修改以符合详细的充电报告需求文档 (1.7)，实现复杂数据处理和计算逻辑。
- **`charging_analysis_pyqt.py` 优化**: 调整结果显示逻辑，对参考数据进行预排序。
- **`sensor_layout_pyqt.py` 修复**: 修正数据映射查找逻辑和`ImageCanvas`中的鼠标点击行为。
- **`data_analysis_view_pyqt.py` 改进**: 确保绘图和数据导出功能使用当前激活的数据集（已筛选或原始数据）。
- **文档更新**: 调整相关Markdown文档以反映代码更改 (部分文件因工具限制未直接修改，但内容已在此处更新)。

### ✅ PyQt6版本 (v2.0) - 2025-06-26 (Original Log)
- **GUI框架升级** - 从Tkinter全面升级到PyQt6
- **数据筛选优化** - 实现"返回并应用"功能
- **充电分析算法优化** - 多种查表方式组合分析
- **数据导出扩展** - 支持Excel、CSV、JSON多格式导出
- **智能启动器** - 自动检测环境并选择合适版本

### ✅ Tkinter版本优化 (v1.x) (Original Log)
- 添加界面滚动条支持
- 修复充电分析返回按钮位置
- 实现主界面行删除功能
- 改进数据筛选保存位置选择
- 优化表格列宽显示
- 修复数据筛选默认状态

## 📞 使用帮助

### 🔍 快速查找
- 查找特定功能: 参考 [代码结构索引.md](./代码结构索引.md) (需手动更新 `AppData` 和 `export_report.py` 部分)
- 了解模块功能: 参考 [功能模块详细索引.md](./功能模块详细索引.md) (需手动更新 `export_report.py` 部分)
- 查看文件结构: 参考 [项目目录索引.md](./项目目录索引.md)

### 🐛 问题排查
1. 检查Python版本 (推荐 3.8+) 和依赖包安装 (`pip install -r requirements.txt`)。
2. 查看控制台输出的日志和错误信息。
3. 确保数据映射配置正确，特别是对于 `charging_analysis` 和 `export_report` 模块。
4. 参考文档索引定位问题代码位置。

### 🚀 功能扩展
- 模块化设计便于添加新功能。
- 参考现有模块 (`*_pyqt.py` 文件) 的代码结构。
- 遵循项目的命名和编码规范。

---

## 📝 版本信息
- **当前版本**: Jules Refactored Version
- **最后更新**: YYYY-MM-DD (Jules - to be updated with current date)
- **开发状态**: 功能增强，持续优化

---

*本项目采用模块化设计，具有良好的可扩展性和维护性。如需了解更多详细信息，请参考相应的索引文档。*
