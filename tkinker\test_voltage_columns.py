#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的电压计算过程列
"""

print("=== 充电分析新增列验证 ===")
print("验证新增的电压计算过程列是否正确输出到表格")
print("=" * 50)

print("\n📊 新增的输出列:")
print("1. prev_voltage - 每行计算前的prev_voltage值")
print("2. v_for_min_calc - MinV经过电压更新策略后的计算用电压")
print("3. v_for_max_calc - MaxV经过电压更新策略后的计算用电压")

print("\n🔍 电压更新策略逻辑:")
print("对于MinV和MaxV，都应用chargevoltage.py的策略:")
print("if voltage >= prev_voltage or marker_a == 1:")
print("    使用原始电压")
print("else:")
print("    使用 prev_voltage - 0.001")

print("\n📋 完整的输出列列表:")
output_columns = [
    "原始输入列 (MinT, MaxT, MinV, MaxV, a)",
    "curr_all - 最终选择的最小电流",
    "curr_minTminV - MinT+MinV组合的电流",
    "curr_maxTmaxV - MaxT+MaxV组合的电流", 
    "curr_minTmaxV - MinT+MaxV组合的电流",
    "curr_maxTminV - MaxT+MinV组合的电流",
    "使用电压 - 最终选择的电压",
    "prev_voltage - 计算前的prev_voltage值 (新增)",
    "v_for_min_calc - MinV的计算用电压 (新增)",
    "v_for_max_calc - MaxV的计算用电压 (新增)"
]

for i, col in enumerate(output_columns, 1):
    print(f"{i:2d}. {col}")

print("\n🧪 验证方法:")
print("1. 运行充电分析功能")
print("2. 查看结果表格，确认包含所有10个输出列")
print("3. 检查前几行的数值:")
print("   - prev_voltage应该从0开始，然后是上一行的使用电压")
print("   - v_for_min_calc和v_for_max_calc应该根据电压更新策略计算")
print("   - 当marker_a=1或电压>=prev_voltage时，应该使用原始电压")
print("   - 否则应该使用prev_voltage-0.001")

print("\n📈 预期的数据模式:")
print("行0: prev_voltage=0, v_for_min_calc=MinV, v_for_max_calc=MaxV (通常)")
print("行1: prev_voltage=行0的使用电压, v_for_min_calc根据策略计算")
print("行2: prev_voltage=行1的使用电压, v_for_max_calc根据策略计算")
print("...")

print("\n✅ 如果看到这些列并且数值符合逻辑，说明修改成功!")
print("❌ 如果缺少列或数值不符合预期，请检查代码实现")

print("\n" + "=" * 50)
print("测试说明完成，请运行实际程序进行验证")
