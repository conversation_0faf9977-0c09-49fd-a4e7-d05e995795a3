# 数据可视化程序文件索引 - 2025最新版

## 📁 项目概述
基于PyQt6的数据可视化分析程序，主要用于处理和分析CSV格式的数据文件，提供多种数据分析和可视化功能。

**最后更新时间**: 2025-06-30
**项目路径**: `f:\kk\07-datavisualprogram-feature-gui\`

## 🏗️ 完整文件结构

### 📂 根目录文件 (26个主要文件)

#### 🚀 主程序文件
- **`main_pyqt.py`** - 主程序入口文件 (PyQt6版本)
  - 应用程序主界面和核心功能
  - 数据导入、预览、表格显示
  - 各功能模块的导航入口
  - 行删除、数据筛选应用等核心功能
  - **最新更新**: 支持一对多数据映射，增强筛选数据同步

#### 🔧 核心功能模块 (PyQt6版本)
- **`data_filtering_pyqt.py`** - 数据筛选模块
  - 信号列选择和筛选功能
  - 筛选配置保存/加载
  - 筛选数据导出
  - 支持批量选择和自定义保存位置

- **`data_mapping_pyqt.py`** - 数据映射模块 ⭐ **最新优化**
  - 逻辑列名与物理列名映射
  - **新功能**: 一对多映射支持 (一个CSV列映射为多个逻辑名称)
  - 映射关系配置管理和预览功能
  - 自定义变量计算功能
  - 配置文件导入/导出
  - 智能验证和错误检测

- **`charging_analysis_pyqt.py`** - 充电分析模块
  - 充电数据深度分析
  - 充电曲线可视化
  - 分析报告生成和导出
  - 滚动界面支持

- **`sensor_layout_pyqt.py`** - 温感布置模块
  - 传感器布局可视化
  - 温度数据空间分布
  - 图像处理和显示功能
  - 交互式传感器点标记

- **`data_analysis_view_pyqt.py`** - 数据分析视图模块
  - 数据分析结果展示
  - 多维度数据可视化
  - 分析图表生成
  - 交互式数据探索

- **`export_report_pyqt.py`** - 报告导出模块
  - 分析结果报告生成
  - 多格式导出支持 (Excel, CSV)
  - 报告模板管理
  - 进度显示和错误处理

#### 🛠️ 辅助工具文件
- **`columnnamechange.py`** - 列名处理工具
  - 数据列名标准化处理
  - 列名映射和转换功能

#### 🧪 测试和调试文件 ⭐ **新增**
- **`test_one_to_many_mapping.py`** - 一对多映射功能测试程序
- **`verify_mapping_logic.py`** - 映射逻辑验证脚本
- **`debug_mapping_test.py`** - 映射调试测试
- **`diagnose_mapping_issue.py`** - 映射问题诊断工具

#### 📋 配置文件
- **`requirements_pyqt.txt`** - PyQt6版本依赖列表
- **`E08-data_mapping_config.json`** - E08项目数据映射配置
- **`E0X-data_mapping_config.json`** - E0X项目数据映射配置
- **`example_one_to_many_mapping.json`** - 一对多映射示例配置 ⭐ **新增**
- **`test_one_to_many_config.json`** - 一对多映射测试配置 ⭐ **新增**

#### 📖 文档文件
- **`README.md`** - 项目说明文档
- **`文件索引_完整版.md`** - 原有文件索引
- **`最新文件索引_2025.md`** - 本文件，最新完整索引 ⭐ **新增**
- **`快速参考指南.md`** - 快速使用指南
- **`功能模块详细索引.md`** - 功能模块详细说明
- **`项目目录索引.md`** - 项目目录结构说明
- **`项目文件索引_更新.md`** - 项目文件索引更新版
- **`代码结构索引.md`** - 代码结构详细索引
- **`数据处理显示程序.md`** - 数据处理程序说明
- **`优化完成说明.md`** - 优化完成情况说明
- **`一对多数据映射使用说明.md`** - 一对多映射功能详细使用说明 ⭐ **新增**

### 📂 子目录

#### 📁 tkinker/ - Tkinter版本文件 (历史版本)
包含原始的Tkinter版本实现文件，已迁移到PyQt6版本：

**主程序文件:**
- `main_opt.py` - Tkinter版本主程序
- `启动程序.py` - 程序启动脚本

**功能模块:**
- `data_filtering.py` - 数据筛选 (Tkinter版)
- `data_mapping.py` - 数据映射 (Tkinter版)
- `charging_analysis.py` - 充电分析 (Tkinter版)
- `sensor_layout.py` - 温感布置 (Tkinter版)
- `data_analysis_view.py` - 数据分析视图 (Tkinter版)
- `export_report.py` - 报告导出 (Tkinter版)

**辅助文件:**
- `beautifulbattery.py` - 电池美化显示
- `chargevoltage.py` - 充电电压处理
- `ui_styles.py` - UI样式定义
- `requirements.txt` - Tkinter版本依赖

**测试文件:**
- `test_*.py` - 各种测试脚本
- `PyQt6_*.md` - PyQt6迁移相关文档

#### 📁 images/ - 图像资源文件
- 6个JPEG格式的图像文件
- 用于传感器布置和界面显示

#### 📁 使用的数据/ - 测试数据文件
- **`QRQ_E08_REEV__53kwh_1P102S_电芯参数表_ V1.4_20241122-（10S）TO客户-快充倍率导出2.csv`** - E08项目数据
- **`logging-52kWh-fastcharge.csv`** - 52kWh快充数据
- **`logging-52kWh-fastcharge_charging_analysis_result.csv`** - 充电分析结果
- **`奇瑞E03标定温度后，SOC10-80%快充。2024_07_24_10_11_21.csv`** - E03项目数据
- **`E08 NTC.png`** - E08 NTC温度传感器布置图

#### 📁 __pycache__/ - Python缓存文件
- 编译后的Python字节码文件 (.pyc)
- 自动生成，用于提高程序加载速度

## 🔄 版本信息

### 当前版本: PyQt6
- **主要特点**: 现代化GUI框架，更好的性能和外观
- **状态**: 活跃开发中
- **推荐使用**: ✅
- **最新更新**: 一对多数据映射功能优化完成

### 历史版本: Tkinter
- **位置**: `tkinker/` 目录
- **状态**: 已停止维护
- **用途**: 参考和备份

## 📊 文件统计

### 根目录文件统计
- Python源文件: 12个 (PyQt6版本 + 测试文件)
- 配置文件: 5个
- 文档文件: 10个
- 总计: 27个主要文件

### 子目录统计
- tkinker/: 20+ 个文件 (Tkinter版本)
- images/: 6个图像文件
- 使用的数据/: 5个数据文件
- __pycache__/: 多个缓存文件

## 🎯 主要功能模块

1. **数据导入与预览** - `main_pyqt.py`
2. **数据筛选** - `data_filtering_pyqt.py`
3. **数据映射** - `data_mapping_pyqt.py` ⭐ **支持一对多映射**
4. **充电分析** - `charging_analysis_pyqt.py`
5. **传感器布置** - `sensor_layout_pyqt.py`
6. **数据分析视图** - `data_analysis_view_pyqt.py`
7. **报告导出** - `export_report_pyqt.py`

## ⭐ 最新功能亮点

### 一对多数据映射功能
- **功能**: 一个CSV列可以同时映射为多个逻辑名称
- **使用方法**: 在逻辑名称输入框中用逗号分隔多个名称
- **示例**: `BMS_BattTempMin → MinT, TempMin, 最小温度`
- **特点**: 
  - 保留原始CSV列
  - 创建多个相同数据的新列
  - 支持中英文混合命名
  - 向后兼容原有配置
  - 智能验证和预览功能

## 🔧 技术栈

- **GUI框架**: PyQt6
- **数据处理**: pandas, numpy
- **可视化**: matplotlib
- **图像处理**: PIL (Pillow)
- **文件格式**: CSV, Excel, JSON

## 📝 使用说明

1. 确保安装了所需依赖: `pip install -r requirements_pyqt.txt`
2. 运行主程序: `python main_pyqt.py`
3. 导入CSV数据文件开始分析
4. 使用各功能模块进行数据处理和分析
5. **新功能**: 使用一对多数据映射创建多个逻辑名称

---

*本索引文件会随着项目的更新而持续维护和更新。*
