# PyQt6数据可视化应用程序使用指南

## 快速开始

### 1. 环境准备
```bash
# 安装依赖包
pip install -r requirements_pyqt.txt
```

### 2. 启动应用程序
```bash
python main_pyqt.py
```

## 界面布局

应用程序采用左右分栏布局：
- **左侧**: 功能按钮面板（200px宽度）
- **右侧**: 数据显示和预览区域

## 功能模块

### 1. 导入数据
- 点击"导入数据"按钮
- 选择CSV数据文件
- 系统自动显示数据预览（前5行+后5行）
- 支持编码自动检测

### 2. 数据映射
- 点击"数据映射"按钮
- 配置CSV列名到逻辑名的映射关系
- 支持时间列自动检测
- 可保存/加载映射配置

### 3. 数据筛选
- 点击"数据筛选"按钮
- 设置筛选条件
- 支持多条件组合筛选
- 可自定义保存位置

### 4. 充电分析
- 点击"充电分析"按钮
- **重要**: 需要先导入充电基准数据
- 基准数据必须包含：temp, voltage, current列
- 执行复杂的充电分析算法
- 支持多线程处理

### 5. 温感布置
- 点击"温感布置"按钮
- 在图像上布置温度传感器点位
- 支持多种显示模式：delta_t, max_val, min_val, slider
- 可保存/加载项目文件

### 6. 导出充电数据报告
- 点击"导出充电数据报告"按钮
- 功能开发中...

### 7. 数据分析
- 点击"数据分析"按钮
- 多列数据选择（支持自动完成）
- 多种图表类型：线图、散点图、柱状图、直方图
- 支持原始数据和筛选后数据

## 数据流程

推荐的使用流程：

1. **导入主数据** → 导入CSV数据文件
2. **导入基准数据** → 在充电分析模块中导入充电基准数据
3. **配置映射** → 设置列名映射关系
4. **数据筛选** → 根据需要筛选数据
5. **执行分析** → 进行充电分析或数据分析
6. **温感布置** → 配置传感器布局
7. **导出结果** → 保存分析结果

## 注意事项

### 充电分析模块
- 必须先导入包含temp, voltage, current列的基准数据
- 输入数据需要包含MinT, MaxV列
- 算法会自动执行温度层选择和电压筛选

### 数据格式要求
- 支持UTF-8编码的CSV文件
- 列名不能包含特殊字符
- 数值列应为数字格式

### 性能优化
- 大数据集建议先进行筛选
- 充电分析使用多线程，避免界面冻结
- 图表渲染可能需要一些时间

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查是否安装了所有依赖包
   - 运行: `python test_pyqt_modules.py`

2. **数据导入失败**
   - 检查CSV文件编码
   - 确保文件格式正确

3. **充电分析失败**
   - 确保已导入基准数据
   - 检查基准数据是否包含必要列

4. **界面显示异常**
   - 检查PyQt6版本
   - 重启应用程序

### 技术支持

如遇到问题，请检查：
1. Python版本（推荐3.8+）
2. PyQt6版本（推荐6.4.0+）
3. 依赖包完整性
4. 数据文件格式

## 版本信息

- **框架**: PyQt6
- **Python**: 3.8+
- **主要依赖**: pandas, numpy, matplotlib
- **状态**: 生产就绪

---

*本指南基于PyQt6迁移版本，所有功能均已完成并测试通过。*
