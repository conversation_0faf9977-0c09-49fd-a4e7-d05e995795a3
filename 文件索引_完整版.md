# 数据可视化程序 - 完整文件索引

## 📁 项目根目录结构

```
f:\kk\07-datavisualprogram-feature-gui\
├── 📄 main_pyqt.py                          # 主程序入口
├── 📄 charging_analysis_pyqt.py             # 充电分析模块
├── 📄 data_mapping_pyqt.py                  # 数据映射配置
├── 📄 data_filtering_pyqt.py                # 数据筛选模块
├── 📄 sensor_layout_pyqt.py                 # 温感布置模块
├── 📄 data_analysis_view_pyqt.py            # 数据分析视图
├── 📄 export_report_pyqt.py                 # 报告导出模块
├── 📄 columnnamechange.py                   # 列名变更工具
├── 📄 requirements_pyqt.txt                 # 依赖包列表
├── 📄 README.md                             # 项目说明文档
├── 📄 E08-data_mapping_config.json          # 数据映射配置示例
├── 📄 E0X-data_mapping_config.json          # 数据映射配置示例
├── 📁 __pycache__/                          # Python缓存目录
├── 📁 images/                               # 图像资源目录
├── 📁 tkinker/                              # Tkinter版本代码（历史版本）
├── 📁 使用的数据/                           # 测试数据目录
├── 📄 代码结构索引.md                       # 代码结构说明
├── 📄 优化完成说明.md                       # 优化历程说明
├── 📄 功能模块详细索引.md                   # 功能模块详解
├── 📄 数据处理显示程序.md                   # 程序功能说明
├── 📄 项目文件索引_更新.md                  # 文件索引更新版
└── 📄 项目目录索引.md                       # 目录结构索引
```

## 🔧 核心Python模块详解

### 1. main_pyqt.py (主程序)
**功能**: 程序主入口，提供数据导入、预览和模块导航
**核心类**:
- `AppData`: 应用程序共享数据类
- `DataImportWorker`: 数据导入工作线程
- `MainWindow`: 主窗口类

**主要功能**:
- CSV数据导入和编码检测
- 数据预览（前5行/后5行）
- 表格显示和行删除
- 各功能模块的启动入口
- 数据映射应用逻辑

### 2. charging_analysis_pyqt.py (充电分析)
**功能**: 实现充电数据的深度分析和可视化
**核心类**:
- `ChargingAnalysisWorker`: 充电分析工作线程
- `ChargingAnalysisDialog`: 充电分析对话框

**主要功能**:
- 四种温度/电压组合查询
- chargevoltage.py算法集成
- 独立prev_voltage状态管理
- 5种电流计算方式
- 分析结果表格展示

### 3. data_mapping_pyqt.py (数据映射)
**功能**: 管理CSV列名到逻辑名称的映射关系
**核心类**:
- `MappingWindow`: 数据映射配置窗口

**主要功能**:
- 时间列选择和映射
- 信号列映射配置
- 自定义变量定义
- 映射配置保存/加载
- 映射预览和验证

### 4. data_filtering_pyqt.py (数据筛选)
**功能**: 提供数据列的筛选和过滤功能
**核心类**:
- `DataFilteringDialog`: 数据筛选对话框
- `FilterWorker`: 筛选工作线程

**主要功能**:
- 信号列选择筛选
- 筛选配置管理
- "返回并应用"功能
- 筛选结果全局应用

### 5. sensor_layout_pyqt.py (温感布置)
**功能**: 传感器布局的可视化和管理
**核心类**:
- `ImageCanvas`: 可缩放拖拽的图片画布
- `SensorLayoutDialog`: 温感布置对话框

**主要功能**:
- 图像加载和显示
- 传感器点位标记
- 缩放和拖拽操作
- 布局状态保存

### 6. data_analysis_view_pyqt.py (数据分析视图)
**功能**: 数据的多维度可视化分析
**核心类**:
- `AutoCompleteLineEdit`: 自动完成输入框
- `DataAnalysisViewDialog`: 数据分析视图对话框

**主要功能**:
- Matplotlib图表绘制
- 多信号对比分析
- 自动完成输入
- 图表导出功能

### 7. export_report_pyqt.py (报告导出)
**功能**: 生成详细的充电数据分析报告
**核心类**:
- `ExportReportWorker`: 报告导出工作线程
- `ExportReportDialog`: 报告导出对话框

**主要功能**:
- 充电数据处理和计算
- 关键性能指标分析
- SOC时间分析
- 分段统计报告生成

### 8. columnnamechange.py (列名变更工具)
**功能**: 基于Tkinter的列名变更和数据处理工具
**主要功能**:
- CSV文件加载
- 列名重命名
- 数据处理和计算
- 结果导出

## 📋 配置文件说明

### requirements_pyqt.txt
**用途**: Python依赖包列表
**内容**:
- PyQt6>=6.4.0 (GUI框架)
- pandas>=1.5.0 (数据处理)
- numpy>=1.21.0 (数值计算)
- matplotlib>=3.5.0 (图表绘制)
- openpyxl>=3.0.0 (Excel支持)

### E08-data_mapping_config.json
**用途**: 数据映射配置示例
**结构**:
```json
{
  "time_column": "Time",
  "mappings": {
    "BMS_BattTempMin": "MinT",
    "BMS_CellMaxVolt": "MaxV",
    ...
  },
  "custom_variables": [
    {
      "name": "CellVoltage_diff",
      "formula": "CellVoltage_Max-CellVoltage_Min"
    }
  ]
}
```

## 📁 子目录详解

### __pycache__/
**用途**: Python字节码缓存目录
**内容**: 编译后的.pyc文件，提高程序启动速度

### images/
**用途**: 图像资源存储目录
**内容**: 程序中使用的图片文件（JPEG格式）

### tkinker/
**用途**: Tkinter版本的历史代码
**说明**: 包含原始Tkinter实现的各个模块，已被PyQt6版本替代

### 使用的数据/
**用途**: 测试数据和示例文件
**内容**:
- CSV数据文件
- 充电分析结果文件
- 温度传感器布置图

## 📚 文档文件说明

### README.md
**用途**: 项目总体介绍和使用指南
**内容**: 项目概述、快速开始、功能模块、技术栈等

### 代码结构索引.md
**用途**: 代码组织结构和快速查找指南
**内容**: 类和函数的详细索引

### 功能模块详细索引.md
**用途**: 各功能模块的详细介绍
**内容**: 每个模块的功能说明和使用方法

### 优化完成说明.md
**用途**: 项目优化历程记录
**内容**: 各版本的改进和优化内容

### 数据处理显示程序.md
**用途**: 程序功能的详细说明
**内容**: 原始需求和功能规格

## 🔄 数据流程图

```
数据导入 (main_pyqt.py)
    ↓
数据映射 (data_mapping_pyqt.py)
    ↓
数据筛选 (data_filtering_pyqt.py)
    ↓
分析处理 (charging_analysis_pyqt.py / data_analysis_view_pyqt.py)
    ↓
结果导出 (export_report_pyqt.py)
```

## 🔍 关键功能实现细节

### AppData类 (main_pyqt.py)
**共享数据管理**:
- `imported_data`: 原始导入数据
- `filtered_data`: 筛选后数据
- `data_mapping_config`: 数据映射配置
- `charging_analysis_results`: 充电分析结果
- `sensor_layout_state`: 温感布置状态

### 数据处理流程
1. **数据导入**: 自动编码检测，支持多种CSV格式
2. **数据映射**: 列名映射，自定义变量计算
3. **数据筛选**: 列选择，全局应用
4. **数据分析**: 充电分析，可视化分析
5. **结果导出**: 报告生成，多格式导出

### 充电分析算法特点
- **四组合查询**: 支持4种温度/电压组合
- **独立状态**: 每组合独立的prev_voltage状态
- **电流计算**: 5种不同的电流计算方式
- **算法集成**: 基于chargevoltage.py的核心算法

## 🛠️ 开发环境配置

### 必需依赖
```bash
pip install PyQt6>=6.4.0
pip install pandas>=1.5.0
pip install numpy>=1.21.0
pip install matplotlib>=3.5.0
```

### 可选依赖
```bash
pip install openpyxl>=3.0.0  # Excel文件支持
pip install chardet>=5.0.0   # 编码检测
```

### 启动方式
```bash
# 直接启动主程序
python main_pyqt.py

# 或使用依赖文件安装后启动
pip install -r requirements_pyqt.txt
python main_pyqt.py
```

## 🚀 使用建议

### 新用户入门
1. 首先阅读README.md了解项目概况
2. 安装依赖包：`pip install -r requirements_pyqt.txt`
3. 运行主程序：`python main_pyqt.py`
4. 导入测试数据进行功能体验

### 开发者指南
1. 查看代码结构索引.md了解代码组织
2. 参考现有模块的实现模式
3. 遵循PyQt6的编程规范
4. 使用AppData类进行数据共享

### 功能扩展
- 新增模块时参考现有*_pyqt.py文件的结构
- 使用QThread进行耗时操作
- 通过pyqtSignal进行模块间通信
- 保持与AppData的数据同步

## 📝 版本信息
- **当前版本**: PyQt6 重构版
- **最后更新**: 2025-06-30
- **开发状态**: 功能完善，持续维护
- **技术栈**: Python 3.8+ / PyQt6 / Pandas / Matplotlib

---

*此索引文档提供了项目的完整文件结构概览，便于开发者快速定位和理解各个组件的功能。如需了解具体实现细节，请查看对应的源代码文件。*
