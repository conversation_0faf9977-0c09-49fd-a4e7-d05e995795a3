#!/usr/bin/env python3
"""
测试修复的功能
"""

import sys
import os

def test_imports():
    """测试模块导入"""
    print("=== 测试模块导入 ===")
    
    try:
        print("测试数据分析模块...")
        import data_analysis_view_pyqt
        print("✅ data_analysis_view_pyqt 导入成功")
    except Exception as e:
        print(f"❌ data_analysis_view_pyqt 导入失败: {e}")
    
    try:
        print("测试充电分析模块...")
        import charging_analysis_pyqt
        print("✅ charging_analysis_pyqt 导入成功")
    except Exception as e:
        print(f"❌ charging_analysis_pyqt 导入失败: {e}")
    
    try:
        print("测试温感布置模块...")
        import sensor_layout_pyqt
        print("✅ sensor_layout_pyqt 导入成功")
    except Exception as e:
        print(f"❌ sensor_layout_pyqt 导入失败: {e}")

def test_color_gradient():
    """测试颜色梯度功能"""
    print("\n=== 测试颜色梯度功能 ===")
    
    try:
        from sensor_layout_pyqt import ImageCanvas
        from PyQt6.QtGui import QColor
        
        # 创建测试画布
        canvas = ImageCanvas()
        
        # 添加测试点
        canvas.temp_points = {
            'point1': {'x': 100, 'y': 100, 'value': 25.5},
            'point2': {'x': 200, 'y': 200, 'value': 30.0},
            'point3': {'x': 300, 'y': 300, 'value': 25.5},  # 相同值
        }
        
        # 测试颜色计算
        color1 = canvas.get_color_for_value(25.5)
        color2 = canvas.get_color_for_value(30.0)
        color3 = canvas.get_color_for_value('N/A')
        
        print(f"✅ 数值 25.5 的颜色: RGB({color1.red()}, {color1.green()}, {color1.blue()})")
        print(f"✅ 数值 30.0 的颜色: RGB({color2.red()}, {color2.green()}, {color2.blue()})")
        print(f"✅ 无效值的颜色: RGB({color3.red()}, {color3.green()}, {color3.blue()})")
        
        # 测试相同值的情况
        canvas.temp_points = {
            'point1': {'x': 100, 'y': 100, 'value': 25.0},
            'point2': {'x': 200, 'y': 200, 'value': 25.0},
            'point3': {'x': 300, 'y': 300, 'value': 25.0},
        }
        
        color_same = canvas.get_color_for_value(25.0)
        print(f"✅ 相同值 25.0 的颜色: RGB({color_same.red()}, {color_same.green()}, {color_same.blue()})")
        
        # 验证不是灰色
        if color_same.red() != 128 or color_same.green() != 128 or color_same.blue() != 128:
            print("✅ 相同值不再显示灰色")
        else:
            print("❌ 相同值仍然显示灰色")
            
    except Exception as e:
        print(f"❌ 颜色梯度测试失败: {e}")

def test_mapping_status():
    """测试映射状态显示"""
    print("\n=== 测试映射状态显示 ===")

    try:
        # 测试映射配置转换逻辑
        print("测试映射配置转换...")

        # 模拟data_mapping_config格式
        data_mapping_config = {
            'mappings': {
                'BMS_BattTempMin': 'MinT',
                'BMS_CellMaxVolt': 'MaxV',
                'BMS_CellMinVolt': 'MinV',
                'BMS_BattTempMax': 'MaxT'
            }
        }

        # 转换逻辑测试
        mappings = data_mapping_config.get('mappings', {})
        mapping_config = {logic_name: csv_col for csv_col, logic_name in mappings.items()}

        print(f"✅ 原始映射: {mappings}")
        print(f"✅ 转换后映射: {mapping_config}")

        # 验证转换结果
        expected = {
            'MinT': 'BMS_BattTempMin',
            'MaxV': 'BMS_CellMaxVolt',
            'MinV': 'BMS_CellMinVolt',
            'MaxT': 'BMS_BattTempMax'
        }

        if mapping_config == expected:
            print("✅ 映射配置转换正确")
        else:
            print(f"❌ 映射配置转换错误，期望: {expected}")

    except Exception as e:
        print(f"❌ 映射状态测试失败: {e}")

def test_export_fix():
    """测试导出修复"""
    print("\n=== 测试导出修复 ===")

    try:
        print("检查导出方法修复...")

        # 读取修复后的代码
        with open('data_analysis_view_pyqt.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # 检查是否使用了正确的属性名
        if 'self.x_axis_entry.text()' in content:
            print("✅ X轴控件属性名已修复")
        else:
            print("❌ X轴控件属性名未修复")

        # 检查是否有完整的导出逻辑
        if 'export_data.append(row_data)' in content:
            print("✅ 导出数据逻辑已实现")
        else:
            print("❌ 导出数据逻辑未实现")

        if 'df.to_csv' in content and 'df.to_excel' in content:
            print("✅ 支持CSV和Excel导出")
        else:
            print("❌ 导出格式支持不完整")

    except Exception as e:
        print(f"❌ 导出修复测试失败: {e}")

if __name__ == "__main__":
    print("🔧 开始测试修复功能...")

    test_imports()
    test_color_gradient()
    test_mapping_status()
    test_export_fix()

    print("\n🎉 测试完成!")
