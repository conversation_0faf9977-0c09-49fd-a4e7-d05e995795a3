#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试一对多数据映射功能
验证data_mapping_pyqt.py的优化效果
"""

import sys
import pandas as pd
import json
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QMessageBox

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("一对多数据映射测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建测试数据
        self.create_test_data()
        
        # 设置UI
        self.setup_ui()
        
    def create_test_data(self):
        """创建测试数据"""
        # 模拟AppData类
        class AppData:
            def __init__(self):
                self.imported_data = None
                self.data_mapping_config = {}
        
        self.app_data = AppData()
        
        # 创建测试CSV数据
        test_data = {
            'Time': ['2024-01-01 10:00:00', '2024-01-01 10:01:00', '2024-01-01 10:02:00'],
            'BMS_BattTempMin': [25.5, 26.0, 25.8],
            'BMS_BattTempMax': [28.2, 28.5, 28.1],
            'BMS_CellMaxVolt': [4.15, 4.16, 4.14],
            'BMS_CellMinVolt': [4.10, 4.11, 4.09],
            'BMS_PackSOC': [85.5, 85.2, 84.9],
            'BMS_BattCurrent': [12.5, 12.3, 12.1]
        }
        
        self.app_data.imported_data = pd.DataFrame(test_data)
        print("测试数据创建完成:")
        print(self.app_data.imported_data.head())
        
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 说明文本
        info_text = QTextEdit()
        info_text.setMaximumHeight(150)
        info_text.setPlainText("""
一对多数据映射测试程序

测试功能：
1. 一个CSV列可以映射为多个逻辑名称
2. 支持逗号分隔的多个名称输入
3. 配置验证和预览功能
4. 向后兼容原有的单一映射

测试数据包含：Time, BMS_BattTempMin, BMS_BattTempMax, BMS_CellMaxVolt, BMS_CellMinVolt, BMS_PackSOC, BMS_BattCurrent

点击下方按钮开始测试数据映射功能。
        """)
        layout.addWidget(info_text)
        
        # 测试按钮
        test_btn = QPushButton("打开数据映射配置")
        test_btn.clicked.connect(self.open_data_mapping)
        layout.addWidget(test_btn)
        
        # 结果显示
        self.result_text = QTextEdit()
        self.result_text.setPlaceholderText("映射结果将在这里显示...")
        layout.addWidget(self.result_text)
        
        # 应用映射按钮
        apply_btn = QPushButton("应用映射并查看结果")
        apply_btn.clicked.connect(self.apply_mapping_and_show_result)
        layout.addWidget(apply_btn)
        
    def open_data_mapping(self):
        """打开数据映射配置窗口"""
        try:
            from data_mapping_pyqt import MappingWindow
            self.mapping_window = MappingWindow(self, self.app_data)
            self.mapping_window.show()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"无法打开数据映射窗口: {str(e)}")
            
    def apply_data_mapping(self):
        """应用数据映射配置（模拟main_pyqt.py中的方法）"""
        if not hasattr(self.app_data, 'data_mapping_config') or not self.app_data.data_mapping_config:
            return
            
        config = self.app_data.data_mapping_config
        
        try:
            import pandas as pd
            import numpy as np
            
            data = self.app_data.imported_data.copy()
            mapping_count = 0
            
            if 'mappings' in config and config['mappings']:
                for csv_col, logic_names in config['mappings'].items():
                    if csv_col in data.columns:
                        # 支持一对多映射：logic_names可以是字符串或列表
                        if isinstance(logic_names, list):
                            # 多个逻辑名称
                            for logic_name in logic_names:
                                if logic_name.strip():
                                    data[logic_name.strip()] = data[csv_col].copy()
                                    mapping_count += 1
                        else:
                            # 单个逻辑名称（向后兼容）
                            if logic_names.strip():
                                data[logic_names.strip()] = data[csv_col].copy()
                                mapping_count += 1
            
            # 处理自定义变量
            if 'custom_variables' in config and config['custom_variables']:
                for var_config in config['custom_variables']:
                    var_name = var_config.get('name', '').strip()
                    formula = var_config.get('formula', '').strip()
                    
                    if not var_name or not formula:
                        continue
                        
                    try:
                        safe_dict = {
                            'np': np, 'abs': abs, 'max': max,
                            'min': min, 'sum': sum, 'len': len
                        }
                        for col in data.columns:
                            safe_dict[col] = data[col]
                            
                        result = eval(formula, {"__builtins__": {}}, safe_dict)
                        data[var_name] = result
                        
                    except Exception as e:
                        print(f"计算自定义变量 {var_name} 时出错: {str(e)}")
                        data[var_name] = np.nan
            
            self.app_data.imported_data = data
            print(f"应用了 {mapping_count} 个数据映射")
            
        except Exception as e:
            print(f"应用数据映射时出错: {str(e)}")
            
    def apply_mapping_and_show_result(self):
        """应用映射并显示结果"""
        if not hasattr(self.app_data, 'data_mapping_config') or not self.app_data.data_mapping_config:
            QMessageBox.warning(self, "警告", "请先配置数据映射")
            return
            
        # 应用映射
        self.apply_data_mapping()
        
        # 显示结果
        result_text = "🔍 映射应用结果:\n\n"
        
        # 显示配置信息
        config = self.app_data.data_mapping_config
        result_text += "📋 当前配置:\n"
        result_text += json.dumps(config, ensure_ascii=False, indent=2)
        result_text += "\n\n"
        
        # 显示数据列信息
        result_text += f"📊 数据信息:\n"
        result_text += f"行数: {len(self.app_data.imported_data)}\n"
        result_text += f"列数: {len(self.app_data.imported_data.columns)}\n"
        result_text += f"列名: {list(self.app_data.imported_data.columns)}\n\n"
        
        # 显示前几行数据
        result_text += "📋 数据预览:\n"
        result_text += str(self.app_data.imported_data.head())
        
        self.result_text.setPlainText(result_text)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("一对多数据映射测试")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = TestMainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
