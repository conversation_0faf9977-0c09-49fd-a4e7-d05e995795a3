{"time_column": "Time", "mappings": {"BMS_SOCDis": "SOC", "BMS_HvBattCellVoltMax": "CellVoltage_Max", "BMS_HvBattCellVoltMin": "CellVoltage_Min", "CellVoltage_diff": "CellVoltage_diff", "BMS_HvBattMaxT": "BattT_Max", "BMS_HvBattMinT": "BattT_Min", "BMS_HvBattDynChrgnILim": "Batt_Current_req", "BMS_PackCurrent": "Batt_Current", "BMS_PackVoltage": "Batt_Voltage", "FRZCU_3_ClntTHvBattIn": "Inlet", "FRZCU_3_ClntTHvBattOut": "Outlet", "BMS_ChgRemTime": "Time_remain", "Time": "time"}, "custom_variables": [{"name": "TempWater_diff", "formula": "Outlet-Inlet"}, {"name": "Temp_diff", "formula": "BattT_Max-BattT_<PERSON>"}]}