# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
import traceback

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTextEdit, QProgressBar, QFileDialog, QMessageBox,
        QTabWidget, QTableWidget, QTableWidgetItem,
        QScrollArea, QWidget, QGridLayout, QComboBox
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QThread
    from PyQt6.QtGui import QFont
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6")
    sys.exit(1)


class DataProcessingWorker(QThread):
    """数据处理工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str, bool)  # message, is_error
    data_processed = pyqtSignal(object)  # processed_data
    finished = pyqtSignal(bool)  # success

    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data

    def run(self):
        """执行数据处理"""
        try:
            self.log_message.emit("开始处理数据...", False)
            self.progress_updated.emit(10)

            # 获取数据
            if self.app_data.filtered_data is not None:
                data = self.app_data.filtered_data.copy()
                self.log_message.emit("使用筛选后的数据", False)
            else:
                data = self.app_data.imported_data.copy()
                self.log_message.emit("使用原始导入数据", False)

            self.progress_updated.emit(20)

            # 数据处理和分析
            self.log_message.emit("执行数据分析...", False)
            processed_data = self.process_data_with_columnnamechange_logic(data)
            self.progress_updated.emit(80)

            self.log_message.emit("数据处理完成", False)
            self.progress_updated.emit(100)
            self.data_processed.emit(processed_data)
            self.finished.emit(True)

        except Exception as e:
            error_msg = f"数据处理时发生错误: {str(e)}\n{traceback.format_exc()}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)

    def process_data_with_columnnamechange_logic(self, df):
        """基于columnnamechange.py的完整数据处理逻辑"""
        self.log_message.emit("开始应用columnnamechange.py逻辑...", False)
        self.progress_updated.emit(30)

        # 第一步：基础数据处理 (process_data)
        df_processed = self.process_data(df.copy())
        self.progress_updated.emit(50)

        # 第二步：计算时间差 (calculate_time_differences)
        time_diff_1, time_diff_2 = self.calculate_time_differences(df_processed)
        self.progress_updated.emit(60)

        # 第三步：生成分段统计结果 (generate_results)
        results_df = self.generate_results(df_processed, time_diff_1, time_diff_2)
        self.progress_updated.emit(75)

        # 返回包含所有结果的字典
        return {
            'processed_data': df_processed,
            'results_summary': results_df,
            'time_differences': {'98-99': time_diff_1, '99-100': time_diff_2},
            'plotdata': df_processed[['SD7_BBAT_SOC_HVS', 'POWER', 'BattT_Max', 'time', 'Batt_Current', 'Batt_Voltage']] if all(col in df_processed.columns for col in ['SD7_BBAT_SOC_HVS', 'POWER', 'BattT_Max', 'time', 'Batt_Current', 'Batt_Voltage']) else None
        }

    def process_data(self, df_B):
        """处理数据：删除行、计算新列 (对应columnnamechange.py的process_data函数)"""
        self.log_message.emit("执行基础数据处理...", False)

        # 0. 彻底处理重复列名和多级索引问题
        try:
            # 确保列名是字符串类型
            df_B.columns = [str(col) for col in df_B.columns]

            # 处理重复列名
            if df_B.columns.duplicated().any():
                self.log_message.emit("检测到重复列名，正在处理...", False)
                # 手动处理重复列名
                new_columns = []
                seen = {}
                for col in df_B.columns:
                    if col in seen:
                        seen[col] += 1
                        new_columns.append(f"{col}_{seen[col]}")
                    else:
                        seen[col] = 0
                        new_columns.append(col)
                df_B.columns = new_columns
                self.log_message.emit("重复列名已处理", False)

            # 重置索引确保没有多级索引问题
            df_B = df_B.reset_index(drop=True)

        except Exception as e:
            self.log_message.emit(f"处理列名时出错: {str(e)}", True)

        # 1. 处理SOC列
        if 'SD7_BBAT_SOC_HVS' not in df_B.columns:
            if 'BMS_SOCDis' in df_B.columns:
                df_B['SD7_BBAT_SOC_HVS'] = df_B['BMS_SOCDis']
                self.log_message.emit("使用BMS_SOCDis作为SD7_BBAT_SOC_HVS", False)
            else:
                self.log_message.emit("警告: 缺少SOC相关列", True)

        # 添加SOC别名以兼容原始逻辑
        if 'SD7_BBAT_SOC_HVS' in df_B.columns:
            df_B['SOC'] = df_B['SD7_BBAT_SOC_HVS']
            df_B['SOC_real'] = df_B['SD7_BBAT_SOC_HVS']

        # 2. 计算所有带 'CellU' 的列的最大值和最小值，排除大于 60 的数据
        cellu_columns = [col for col in df_B.columns if 'CellU' in col]
        if cellu_columns:
            self.log_message.emit(f"找到{len(cellu_columns)}个CellU列", False)
            df_cellu = df_B[cellu_columns].applymap(lambda x: np.nan if x > 60 else x)
            df_B['BMS_HvBattCellU_max'] = df_cellu.max(axis=1)
            df_B['BMS_HvBattCellU_min'] = df_cellu.min(axis=1)
        else:
            self.log_message.emit("警告: 未找到CellU列", True)

        # 3. 计算所有带 'TempSensorTemp' 的列的最大值和最小值，排除大于 100 的数据
        tempsensortemp_columns = [col for col in df_B.columns if 'TempSensorTemp' in col]
        if tempsensortemp_columns:
            self.log_message.emit(f"找到{len(tempsensortemp_columns)}个TempSensorTemp列", False)
            df_tempsensortemp = df_B[tempsensortemp_columns].applymap(lambda x: np.nan if x > 100 else x)
            df_B['BMS_HvBattTempSensorTemp_max'] = df_tempsensortemp.max(axis=1)
            df_B['BMS_HvBattTempSensorTemp_min'] = df_tempsensortemp.min(axis=1)
        else:
            self.log_message.emit("警告: 未找到TempSensorTemp列", True)

        # 4. 处理 'CellVoltage_Max'
        if 'CellVoltage_Max' not in df_B.columns:
            # 如果没有CellVoltage_Max列，尝试从其他列获取
            if 'BMS_HvBattCellVoltMax' in df_B.columns:
                df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellVoltMax']
                self.log_message.emit("使用BMS_HvBattCellVoltMax作为CellVoltage_Max", False)
            elif 'BMS_HvBattCellU_max' in df_B.columns:
                df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellU_max']
                self.log_message.emit("使用BMS_HvBattCellU_max作为CellVoltage_Max", False)
        else:
            # 如果有CellVoltage_Max列但全为0，尝试替换
            try:
                if df_B['CellVoltage_Max'].eq(0).all():
                    if 'BMS_HvBattCellU_max' in df_B.columns:
                        df_B['CellVoltage_Max_old'] = df_B['CellVoltage_Max']
                        df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellU_max']
                        self.log_message.emit("CellVoltage_Max全为0，使用BMS_HvBattCellU_max替换", False)
                    elif 'BMS_HvBattCellVoltMax' in df_B.columns:
                        df_B['CellVoltage_Max_old'] = df_B['CellVoltage_Max']
                        df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellVoltMax']
                        self.log_message.emit("CellVoltage_Max全为0，使用BMS_HvBattCellVoltMax替换", False)
                else:
                    self.log_message.emit("使用原有CellVoltage_Max列", False)
            except Exception as e:
                self.log_message.emit(f"检查CellVoltage_Max时出错: {str(e)}", True)

        # 5. 处理 'CellVoltage_Min'
        if 'CellVoltage_Min' not in df_B.columns:
            # 如果没有CellVoltage_Min列，尝试从其他列获取
            if 'BMS_HvBattCellVoltMin' in df_B.columns:
                df_B['CellVoltage_Min'] = df_B['BMS_HvBattCellVoltMin']
                self.log_message.emit("使用BMS_HvBattCellVoltMin作为CellVoltage_Min", False)
            elif 'BMS_HvBattCellU_min' in df_B.columns:
                df_B['CellVoltage_Min'] = df_B['BMS_HvBattCellU_min']
                self.log_message.emit("使用BMS_HvBattCellU_min作为CellVoltage_Min", False)
        else:
            # 如果有CellVoltage_Min列但全为0，尝试替换
            try:
                if df_B['CellVoltage_Min'].eq(0).all():
                    if 'BMS_HvBattCellU_min' in df_B.columns:
                        df_B['CellVoltage_Min_old'] = df_B['CellVoltage_Min']
                        df_B['CellVoltage_Min'] = df_B['BMS_HvBattCellU_min']
                        self.log_message.emit("CellVoltage_Min全为0，使用BMS_HvBattCellU_min替换", False)
                    elif 'BMS_HvBattCellVoltMin' in df_B.columns:
                        df_B['CellVoltage_Min_old'] = df_B['CellVoltage_Min']
                        df_B['CellVoltage_Min'] = df_B['BMS_HvBattCellVoltMin']
                        self.log_message.emit("CellVoltage_Min全为0，使用BMS_HvBattCellVoltMin替换", False)
                else:
                    self.log_message.emit("使用原有CellVoltage_Min列", False)
            except Exception as e:
                self.log_message.emit(f"检查CellVoltage_Min时出错: {str(e)}", True)

        # 6. 数据筛选
        original_rows = len(df_B)
        try:
            if 'Batt_Current_req' in df_B.columns:
                # 安全的数据筛选
                mask = df_B['Batt_Current_req'] < 0
                df_B = df_B[mask].copy()
                self.log_message.emit("已筛选Batt_Current_req < 0的数据", False)

            if 'CellVoltage_Max' in df_B.columns:
                # 安全的数据筛选
                mask = df_B['CellVoltage_Max'] != 0
                df_B = df_B[mask].copy()
                self.log_message.emit("已筛选CellVoltage_Max != 0的数据", False)

            # 重置索引
            df_B = df_B.reset_index(drop=True)

            filtered_rows = len(df_B)
            self.log_message.emit(f"数据筛选: {original_rows} -> {filtered_rows} 行", False)

        except Exception as e:
            self.log_message.emit(f"数据筛选时出错: {str(e)}", True)
            self.log_message.emit("跳过数据筛选步骤", False)

        # 7. 计算新列
        try:
            df_B['time'] = range(len(df_B))

            # 安全的列计算，确保只操作单列
            if 'CellVoltage_Max' in df_B.columns and 'CellVoltage_Min' in df_B.columns:
                # 确保获取的是Series而不是DataFrame
                max_col = df_B['CellVoltage_Max']
                min_col = df_B['CellVoltage_Min']
                if hasattr(max_col, 'iloc') and hasattr(min_col, 'iloc'):
                    df_B['CellVoltage_diff'] = abs(max_col - min_col)
                    self.log_message.emit("计算CellVoltage_diff完成", False)

            if 'BattT_Max' in df_B.columns and 'BattT_Min' in df_B.columns:
                max_col = df_B['BattT_Max']
                min_col = df_B['BattT_Min']
                if hasattr(max_col, 'iloc') and hasattr(min_col, 'iloc'):
                    df_B['Temp_diff'] = abs(max_col - min_col)
                    self.log_message.emit("计算Temp_diff完成", False)

            if 'Outlet' in df_B.columns and 'Inlet' in df_B.columns:
                outlet_col = df_B['Outlet']
                inlet_col = df_B['Inlet']
                if hasattr(outlet_col, 'iloc') and hasattr(inlet_col, 'iloc'):
                    df_B['TempWater_diff'] = abs(outlet_col - inlet_col)
                    self.log_message.emit("计算TempWater_diff完成", False)

            if 'Batt_Current' in df_B.columns:
                current_col = df_B['Batt_Current']
                if hasattr(current_col, 'iloc'):
                    df_B['Batt_Current'] = abs(current_col)
                    self.log_message.emit("处理Batt_Current完成", False)

            if 'Batt_Current_req' in df_B.columns:
                current_req_col = df_B['Batt_Current_req']
                if hasattr(current_req_col, 'iloc'):
                    df_B['Batt_Current_req'] = abs(current_req_col)
                    self.log_message.emit("处理Batt_Current_req完成", False)

            if 'Batt_Current' in df_B.columns and 'Batt_Voltage' in df_B.columns:
                current_col = df_B['Batt_Current']
                voltage_col = df_B['Batt_Voltage']
                if hasattr(current_col, 'iloc') and hasattr(voltage_col, 'iloc'):
                    df_B['POWER'] = -current_col * voltage_col / 1000
                    self.log_message.emit("计算POWER完成", False)

            self.log_message.emit("基础数据处理完成", False)

        except Exception as e:
            self.log_message.emit(f"计算新列时出错: {str(e)}", True)
            self.log_message.emit("部分新列可能未能正确计算", False)

        return df_B

    def calculate_time_differences(self, df_B):
        """计算SOC从98到99和99到100的时间差 (对应columnnamechange.py的calculate_time_differences函数)"""
        self.log_message.emit("计算SOC时间差...", False)
        time_difference_1, time_difference_2 = 0, 0

        if 'SOC' not in df_B.columns:
            self.log_message.emit("警告: 缺少SOC列，无法计算时间差", True)
            return time_difference_1, time_difference_2

        try:
            # 计算SOC从98到99的时间差
            if not df_B[df_B['SOC'] >= 98].empty:
                first_row_soc_ge_98 = df_B[df_B['SOC'] >= 98].iloc[0]
                time_first = first_row_soc_ge_98['time']
                if not df_B[df_B['SOC'] < 99].empty:
                    last_row_soc_lt_99 = df_B[df_B['SOC'] < 99].iloc[-1]
                    time_last = last_row_soc_lt_99['time']
                    time_difference_1 = time_last - time_first
                    self.log_message.emit(f"SOC 98-99时间差: {time_difference_1}", False)

            # 计算SOC从99到100的时间差
            if not df_B[df_B['SOC'] >= 99].empty:
                first_row_soc_ge_99 = df_B[df_B['SOC'] >= 99].iloc[0]
                time_first2 = first_row_soc_ge_99['time']
                last_row_soc_ge_99 = df_B[df_B['SOC'] >= 99].iloc[-1]
                time_last2 = last_row_soc_ge_99['time']
                time_difference_2 = time_last2 - time_first2
                self.log_message.emit(f"SOC 99-100时间差: {time_difference_2}", False)

        except Exception as e:
            self.log_message.emit(f"计算时间差时出错: {str(e)}", True)

        return time_difference_1, time_difference_2

    def generate_results(self, df_B, time_difference_1, time_difference_2):
        """生成分段统计结果 (对应columnnamechange.py的generate_results函数)"""
        self.log_message.emit("生成分段统计结果...", False)

        # 定义需要统计的列
        columns = ['CellVoltage_Max', 'CellVoltage_Min', 'CellVoltage_diff',
                   'SOC_real', 'BattT_Max', 'BattT_Min', 'Temp_diff',
                   'Batt_Current_req', 'Batt_Current', 'Batt_Voltage',
                   'Inlet', 'Outlet', 'TempWater_diff', 'Time_remain', 'time',
                   'BMS_HvBattCellU_max', 'BMS_HvBattCellU_min',
                   'BMS_HvBattTempSensorTemp_max', 'BMS_HvBattTempSensorTemp_min']

        # 过滤出实际存在的列
        available_columns = [col for col in columns if col in df_B.columns]
        missing_columns = [col for col in columns if col not in df_B.columns]

        if missing_columns:
            self.log_message.emit(f"缺少列: {missing_columns}", True)

        results = []
        curr_soc = 0

        if 'SOC' not in df_B.columns:
            self.log_message.emit("警告: 缺少SOC列，无法生成分段统计", True)
            return pd.DataFrame()

        try:
            # 安全获取最大SOC值
            if len(df_B) > 0 and 'SOC' in df_B.columns:
                soc_series = df_B['SOC']
                max_soc = float(soc_series.max()) if not soc_series.empty else 100
            else:
                max_soc = 100

            while curr_soc < max_soc:
                # 安全的数据筛选
                soc_col = df_B['SOC']
                mask1 = soc_col >= curr_soc
                mask2 = soc_col < curr_soc + 10
                soc_range = df_B[mask1 & mask2].copy()

                row = []

                for column in available_columns:
                    if column in soc_range.columns:
                        column_values = soc_range[column]
                        if len(column_values) > 0:
                            try:
                                # 确保返回纯数值，而不是pandas Series
                                first_val = column_values.iloc[0]
                                last_val = column_values.iloc[-1]
                                max_val = column_values.max()

                                start = float(first_val) if pd.notna(first_val) else None
                                end = float(last_val) if pd.notna(last_val) else None
                                max_value = float(max_val) if pd.notna(max_val) else None
                            except (ValueError, TypeError):
                                start = end = max_value = None
                        else:
                            start = end = max_value = None
                    else:
                        start = end = max_value = None
                    row.extend([start, end, max_value])

                results.append(row)
                curr_soc += 10

            # 创建列名
            new_columns = [f"{col}_{stat}" for col in available_columns for stat in ['start', 'end', 'max']]
            results_df = pd.DataFrame(results, columns=new_columns)

            # 补充缺失的行到11行
            rows_to_insert = max(0, 11 - len(results_df))
            if rows_to_insert > 0:
                new_rows = pd.DataFrame({col: [None] for col in results_df.columns}, index=[0] * rows_to_insert)
                results_df = pd.concat([new_rows, results_df], axis=0, ignore_index=True)

            # 添加时间差列
            results_df['98-99'] = time_difference_1
            results_df['99-100'] = time_difference_2

            self.log_message.emit(f"生成了{len(results_df)}行统计结果", False)
            return results_df

        except Exception as e:
            self.log_message.emit(f"生成统计结果时出错: {str(e)}", True)
            return pd.DataFrame()


class ExportWorker(QThread):
    """数据导出工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str, bool)  # message, is_error
    finished = pyqtSignal(bool)  # success

    def __init__(self, data, export_path, file_format):
        super().__init__()
        self.data = data
        self.export_path = export_path
        self.file_format = file_format

    def run(self):
        """执行数据导出"""
        try:
            self.log_message.emit("开始导出数据...", False)
            self.progress_updated.emit(10)

            # 处理新的数据结构（字典）
            if isinstance(self.data, dict):
                self.export_multiple_datasets()
            else:
                # 兼容旧的数据结构（直接是DataFrame）
                self.export_single_dataset()

            self.progress_updated.emit(100)
            self.finished.emit(True)

        except Exception as e:
            error_msg = f"导出数据时发生错误: {str(e)}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)

    def export_multiple_datasets(self):
        """导出多个数据集"""
        import os

        # 获取基础路径和扩展名
        base_path = os.path.splitext(self.export_path)[0]
        extension = os.path.splitext(self.export_path)[1]

        exported_files = []
        total_datasets = 0

        # 计算总数据集数量
        if self.data.get('processed_data') is not None:
            total_datasets += 1
        if self.data.get('results_summary') is not None:
            total_datasets += 1
        if self.data.get('plotdata') is not None:
            total_datasets += 1

        current_dataset = 0

        # 导出主处理数据
        if self.data.get('processed_data') is not None:
            current_dataset += 1
            main_path = f"{base_path}_processed_data{extension}"
            self.export_dataframe(self.data['processed_data'], main_path, "主处理数据")
            exported_files.append(main_path)
            self.progress_updated.emit(int(30 * current_dataset / total_datasets))

        # 导出统计结果
        if self.data.get('results_summary') is not None:
            current_dataset += 1
            results_path = f"{base_path}_results_summary{extension}"
            # 转置统计结果以匹配columnnamechange.py的输出格式，并确保第一列是列名
            results_transposed = self.data['results_summary'].transpose()
            # 重置索引，使原来的索引（列名）成为第一列
            results_transposed = results_transposed.reset_index()
            # 重命名第一列为更清晰的名称
            results_transposed.columns = ['指标'] + [f'SOC_{i*10}-{(i+1)*10}' for i in range(len(results_transposed.columns)-1)]
            self.export_dataframe(results_transposed, results_path, "统计结果")
            exported_files.append(results_path)
            self.progress_updated.emit(int(60 * current_dataset / total_datasets))

        # 导出绘图数据
        if self.data.get('plotdata') is not None:
            current_dataset += 1
            plot_path = f"{base_path}_plotdata{extension}"
            self.export_dataframe(self.data['plotdata'], plot_path, "绘图数据")
            exported_files.append(plot_path)
            self.progress_updated.emit(int(90 * current_dataset / total_datasets))

        # 显示导出结果
        self.log_message.emit(f"✅ 成功导出 {len(exported_files)} 个文件:", False)
        for file_path in exported_files:
            self.log_message.emit(f"  📄 {os.path.basename(file_path)}", False)

    def export_single_dataset(self):
        """导出单个数据集（兼容模式）"""
        self.export_dataframe(self.data, self.export_path, "数据")
        self.progress_updated.emit(90)

    def export_dataframe(self, df, file_path, description):
        """导出单个DataFrame"""
        try:
            # 确保数据是纯数值，清理任何可能的pandas对象显示
            df_clean = df.copy()

            # 对于数值列，确保转换为纯数值
            for col in df_clean.columns:
                if col != '指标':  # 保留指标列的文本
                    try:
                        # 尝试转换为数值，如果失败则保持原值
                        df_clean[col] = pd.to_numeric(df_clean[col], errors='coerce')
                    except:
                        pass

            if self.file_format.lower() == 'csv':
                df_clean.to_csv(file_path, index=False, encoding='utf-8-sig')
            elif self.file_format.lower() == 'xlsx':
                df_clean.to_excel(file_path, index=False)
            else:
                raise ValueError(f"不支持的文件格式: {self.file_format}")

            rows, cols = df_clean.shape
            self.log_message.emit(f"📊 {description}: {rows} 行 × {cols} 列 -> {file_path}", False)

        except Exception as e:
            self.log_message.emit(f"导出{description}时出错: {str(e)}", True)
            raise


class SignalStatusWidget(QWidget):
    """信号状态检查组件"""

    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("📊 信号状态检查")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.grid_layout = QGridLayout(scroll_widget)

        # 设置表头
        headers = ["信号名称", "状态", "数据源", "备注"]
        for i, header in enumerate(headers):
            label = QLabel(header)
            label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
            self.grid_layout.addWidget(label, 0, i)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新信号状态")
        refresh_btn.clicked.connect(self.refresh_signal_status)
        layout.addWidget(refresh_btn)

        self.setLayout(layout)
        self.refresh_signal_status()

    def refresh_signal_status(self):
        """刷新信号状态"""
        # 清除现有内容（保留表头）
        for i in reversed(range(1, self.grid_layout.rowCount())):
            for j in range(self.grid_layout.columnCount()):
                item = self.grid_layout.itemAtPosition(i, j)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

        if not self.app_data.imported_data is not None:
            return

        # 定义需要检查的信号
        required_signals = [
            "SD7_BBAT_SOC_HVS", "BMS_SOCDis", "SOC",
            "Batt_Current", "Batt_Current_req", "Batt_Voltage",
            "CellVoltage_Max", "CellVoltage_Min",
            "BattT_Max", "BattT_Min",
            "Outlet", "Inlet",
            "Time", "time"
        ]

        # 获取当前数据列
        current_columns = list(self.app_data.imported_data.columns)

        # 获取映射配置
        mappings = self.app_data.data_mapping_config.get('mappings', {}) if hasattr(self.app_data, 'data_mapping_config') else {}

        row = 1
        for signal in required_signals:
            # 检查信号状态
            status, source, note = self.check_signal_status(signal, current_columns, mappings)

            # 信号名称
            name_label = QLabel(signal)
            name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(name_label, row, 0)

            # 状态
            status_label = QLabel(status)
            if "✅" in status:
                status_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "⚠️" in status:
                status_label.setStyleSheet("color: orange; padding: 3px; border: 1px solid #ddd;")
            else:
                status_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(status_label, row, 1)

            # 数据源
            source_label = QLabel(source)
            source_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(source_label, row, 2)

            # 备注
            note_label = QLabel(note)
            note_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(note_label, row, 3)

            row += 1

        # 检查CellU和TempSensorTemp列
        cellu_columns = [col for col in current_columns if 'CellU' in col]
        tempsensor_columns = [col for col in current_columns if 'TempSensorTemp' in col]

        # CellU列状态
        if cellu_columns:
            status = f"✅ 已找到 ({len(cellu_columns)}个)"
            source = "原始数据"
            note = f"将计算最大值和最小值"
        else:
            status = "❌ 未找到"
            source = "-"
            note = "无法计算电池电压极值"

        for i, (_, content) in enumerate([
            ("CellU列", status), ("", source), ("", note)
        ]):
            if i == 0:
                name_label = QLabel("CellU相关列")
                name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
                self.grid_layout.addWidget(name_label, row, 0)

            content_label = QLabel(content)
            if "✅" in content:
                content_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "❌" in content:
                content_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            else:
                content_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(content_label, row, i + 1)

        row += 1

        # TempSensorTemp列状态
        if tempsensor_columns:
            status = f"✅ 已找到 ({len(tempsensor_columns)}个)"
            source = "原始数据"
            note = f"将计算最大值和最小值"
        else:
            status = "❌ 未找到"
            source = "-"
            note = "无法计算温度传感器极值"

        name_label = QLabel("TempSensorTemp相关列")
        name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
        self.grid_layout.addWidget(name_label, row, 0)

        for i, content in enumerate([status, source, note]):
            content_label = QLabel(content)
            if "✅" in content:
                content_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "❌" in content:
                content_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            else:
                content_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(content_label, row, i + 1)

    def check_signal_status(self, signal, columns, mappings):
        """检查单个信号的状态"""
        # 直接存在于数据中
        if signal in columns:
            return "✅ 直接可用", "原始数据", "列直接存在于数据中"

        # 通过映射可用
        for csv_col, logic_names in mappings.items():
            if isinstance(logic_names, list):
                if signal in logic_names and csv_col in columns:
                    return "✅ 映射可用", f"映射自 {csv_col}", "通过数据映射获得"
            else:
                if signal == logic_names and csv_col in columns:
                    return "✅ 映射可用", f"映射自 {csv_col}", "通过数据映射获得"

        # 特殊处理SOC相关信号
        if signal == "SD7_BBAT_SOC_HVS" and "BMS_SOCDis" in columns:
            return "⚠️ 可替代", "BMS_SOCDis", "可使用BMS_SOCDis替代"

        if signal == "SOC":
            if "SD7_BBAT_SOC_HVS" in columns:
                return "⚠️ 可替代", "SD7_BBAT_SOC_HVS", "可使用SD7_BBAT_SOC_HVS"
            elif "BMS_SOCDis" in columns:
                return "⚠️ 可替代", "BMS_SOCDis", "可使用BMS_SOCDis"

        return "❌ 缺失", "-", "信号不可用，可能影响分析结果"


class DataPreviewWidget(QWidget):
    """数据预览组件"""

    def __init__(self):
        super().__init__()
        self.processed_data = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题和控制按钮
        header_layout = QHBoxLayout()

        title_label = QLabel("📋 处理后数据预览")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新预览")
        self.refresh_btn.clicked.connect(self.refresh_preview)
        self.refresh_btn.setEnabled(False)
        header_layout.addWidget(self.refresh_btn)

        layout.addLayout(header_layout)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 1. 主数据标签页
        main_data_widget = QWidget()
        main_data_layout = QVBoxLayout()

        # 数据信息
        self.info_label = QLabel("暂无数据")
        self.info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        main_data_layout.addWidget(self.info_label)

        # 数据表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 设置表格样式
        table_style = """
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 5px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """
        self.table.setStyleSheet(table_style)
        main_data_layout.addWidget(self.table)
        main_data_widget.setLayout(main_data_layout)
        self.tab_widget.addTab(main_data_widget, "📊 主数据")

        # 2. 统计结果标签页
        summary_widget = QWidget()
        summary_layout = QVBoxLayout()

        # 统计信息
        self.summary_info_label = QLabel("暂无统计数据")
        self.summary_info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        summary_layout.addWidget(self.summary_info_label)

        # 统计表格
        self.summary_table = QTableWidget()
        self.summary_table.setAlternatingRowColors(True)
        self.summary_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.summary_table.setStyleSheet(table_style)
        summary_layout.addWidget(self.summary_table)
        summary_widget.setLayout(summary_layout)
        self.tab_widget.addTab(summary_widget, "📈 统计结果")

        layout.addWidget(self.tab_widget)
        self.setLayout(layout)

    def update_data(self, processed_data):
        """更新预览数据"""
        self.processed_data = processed_data
        self.refresh_btn.setEnabled(True)
        self.refresh_preview()
        self.refresh_summary()

    def refresh_preview(self):
        """刷新主数据预览"""
        if self.processed_data is None:
            self.info_label.setText("暂无数据")
            self.table.clear()
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return

        # 处理新的数据结构（字典）
        if isinstance(self.processed_data, dict):
            main_data = self.processed_data.get('processed_data')
            if main_data is None:
                self.info_label.setText("暂无主数据")
                self.table.clear()
                self.table.setRowCount(0)
                self.table.setColumnCount(0)
                return
        else:
            # 兼容旧的数据结构（直接是DataFrame）
            main_data = self.processed_data

        # 更新信息标签
        rows, cols = main_data.shape
        self.info_label.setText(f"主数据维度: {rows} 行 × {cols} 列")

        # 显示前100行数据（避免界面卡顿）
        display_data = main_data.head(100)

        # 设置表格
        self.table.setRowCount(len(display_data))
        self.table.setColumnCount(len(display_data.columns))

        # 设置列标题
        self.table.setHorizontalHeaderLabels(list(display_data.columns))

        # 填充数据
        for i, (_, row) in enumerate(display_data.iterrows()):
            for j, value in enumerate(row):
                if pd.isna(value):
                    item_text = "NaN"
                elif isinstance(value, float):
                    item_text = f"{value:.3f}"
                else:
                    item_text = str(value)

                item = QTableWidgetItem(item_text)
                self.table.setItem(i, j, item)

        # 调整列宽
        self.table.resizeColumnsToContents()

        # 如果数据超过100行，显示提示
        if rows > 100:
            self.info_label.setText(f"主数据维度: {rows} 行 × {cols} 列 (仅显示前100行)")

    def refresh_summary(self):
        """刷新统计结果预览"""
        if self.processed_data is None or not isinstance(self.processed_data, dict):
            self.summary_info_label.setText("暂无统计数据")
            self.summary_table.clear()
            self.summary_table.setRowCount(0)
            self.summary_table.setColumnCount(0)
            return

        summary_data = self.processed_data.get('results_summary')
        if summary_data is None or summary_data.empty:
            self.summary_info_label.setText("暂无统计数据")
            self.summary_table.clear()
            self.summary_table.setRowCount(0)
            self.summary_table.setColumnCount(0)
            return

        # 转置数据以匹配导出格式
        summary_transposed = summary_data.transpose().reset_index()
        summary_transposed.columns = ['指标'] + [f'SOC_{i*10}-{(i+1)*10}' for i in range(len(summary_transposed.columns)-1)]

        # 更新信息标签
        rows, cols = summary_transposed.shape
        self.summary_info_label.setText(f"统计结果: {rows} 个指标 × {cols-1} 个SOC区间")

        # 设置表格
        self.summary_table.setRowCount(len(summary_transposed))
        self.summary_table.setColumnCount(len(summary_transposed.columns))

        # 设置列标题
        self.summary_table.setHorizontalHeaderLabels(list(summary_transposed.columns))

        # 填充数据
        for i, (_, row) in enumerate(summary_transposed.iterrows()):
            for j, value in enumerate(row):
                if j == 0:  # 第一列是指标名称
                    item_text = str(value)
                elif pd.isna(value):
                    item_text = "NaN"
                elif isinstance(value, float):
                    item_text = f"{value:.3f}"
                else:
                    item_text = str(value)

                item = QTableWidgetItem(item_text)
                self.summary_table.setItem(i, j, item)

        # 调整列宽
        self.summary_table.resizeColumnsToContents()


class ExportControlWidget(QWidget):
    """导出控制组件"""

    export_requested = pyqtSignal(str, str)  # file_path, format

    def __init__(self):
        super().__init__()
        self.processed_data = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("💾 数据导出")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 导出格式选择
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("导出格式:"))

        self.format_combo = QComboBox()
        self.format_combo.addItems(["CSV", "XLSX"])
        self.format_combo.setCurrentText("CSV")
        format_layout.addWidget(self.format_combo)

        format_layout.addStretch()
        layout.addLayout(format_layout)

        # 导出按钮
        button_layout = QHBoxLayout()

        self.export_btn = QPushButton("📁 选择位置并导出")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.export_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 导出状态
        self.status_label = QLabel("请先处理数据")
        self.status_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(self.status_label)

        layout.addStretch()
        self.setLayout(layout)

    def update_data(self, processed_data):
        """更新可导出的数据"""
        self.processed_data = processed_data
        self.export_btn.setEnabled(True)

        # 处理新的数据结构（字典）
        if isinstance(processed_data, dict):
            main_data = processed_data.get('processed_data')
            if main_data is not None:
                rows, cols = main_data.shape
                # 计算可导出的数据集数量
                export_count = 1  # 主数据
                if processed_data.get('results_summary') is not None:
                    export_count += 1  # 统计结果
                if processed_data.get('plotdata') is not None:
                    export_count += 1  # 绘图数据

                self.status_label.setText(f"准备导出 {export_count} 个数据集 (主数据: {rows} 行 × {cols} 列)")
            else:
                self.status_label.setText("数据处理结果为空")
        else:
            # 兼容旧的数据结构（直接是DataFrame）
            rows, cols = processed_data.shape
            self.status_label.setText(f"准备导出 {rows} 行 × {cols} 列数据")

    def export_data(self):
        """导出数据"""
        if self.processed_data is None:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return

        # 获取导出格式
        file_format = self.format_combo.currentText().lower()

        # 选择保存目录（而不是单个文件）
        save_dir = QFileDialog.getExistingDirectory(
            self, "选择保存目录",
            "",
            QFileDialog.Option.ShowDirsOnly
        )

        if save_dir:
            # 生成基础文件名（不含扩展名）
            base_name = "charging_analysis_export"

            # 构建完整的文件路径
            if file_format == "csv":
                file_path = f"{save_dir}/{base_name}.csv"
            else:
                file_path = f"{save_dir}/{base_name}.xlsx"

            self.export_requested.emit(file_path, file_format)


class ExportReportDialog(QDialog):
    """优化后的导出报告对话框"""

    def __init__(self, app_data, parent=None):
        super().__init__(parent)
        self.app_data = app_data
        self.processed_data = None
        self.processing_worker = None
        self.export_worker = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("充电分析数据导出")
        self.setGeometry(100, 100, 1200, 800)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("⚡ 充电分析数据导出")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        main_layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 1. 信号状态检查标签页
        self.signal_status_widget = SignalStatusWidget(self.app_data)
        self.tab_widget.addTab(self.signal_status_widget, "📊 信号状态")

        # 2. 数据预览标签页
        self.data_preview_widget = DataPreviewWidget()
        self.tab_widget.addTab(self.data_preview_widget, "📋 数据预览")

        # 3. 导出控制标签页
        self.export_control_widget = ExportControlWidget()
        self.export_control_widget.export_requested.connect(self.start_export)
        self.tab_widget.addTab(self.export_control_widget, "💾 数据导出")

        main_layout.addWidget(self.tab_widget)

        # 控制按钮区域
        control_layout = QHBoxLayout()

        # 处理数据按钮
        self.process_btn = QPushButton("🔄 处理数据")
        self.process_btn.clicked.connect(self.start_data_processing)
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        control_layout.addWidget(self.process_btn)

        control_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("❌ 关闭")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        control_layout.addWidget(close_btn)

        main_layout.addLayout(control_layout)

        # 进度条和日志区域
        progress_layout = QVBoxLayout()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("操作日志将在这里显示...")
        progress_layout.addWidget(self.log_text)

        main_layout.addLayout(progress_layout)

        self.setLayout(main_layout)

        # 检查数据可用性
        self.check_data_availability()

    def check_data_availability(self):
        """检查数据可用性"""
        if self.app_data.imported_data is None:
            self.log_message("❌ 错误: 没有导入的数据", True)
            self.process_btn.setEnabled(False)
        else:
            rows, cols = self.app_data.imported_data.shape
            self.log_message(f"✅ 数据已加载: {rows} 行 × {cols} 列", False)
            self.process_btn.setEnabled(True)

    def start_data_processing(self):
        """开始数据处理"""
        if self.processing_worker and self.processing_worker.isRunning():
            self.log_message("⚠️ 数据处理正在进行中...", True)
            return

        self.log_message("🔄 开始数据处理...", False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.process_btn.setEnabled(False)

        # 启动数据处理线程
        self.processing_worker = DataProcessingWorker(self.app_data)
        self.processing_worker.progress_updated.connect(self.progress_bar.setValue)
        self.processing_worker.log_message.connect(self.log_message)
        self.processing_worker.data_processed.connect(self.on_data_processed)
        self.processing_worker.finished.connect(self.on_processing_finished)
        self.processing_worker.start()

    def on_data_processed(self, processed_data):
        """数据处理完成"""
        # processed_data 现在是一个字典，包含多种处理结果
        self.processed_data = processed_data

        # 获取主要的处理数据用于预览
        main_data = processed_data.get('processed_data')
        if main_data is not None:
            self.data_preview_widget.update_data(main_data)
            self.export_control_widget.update_data(processed_data)  # 传递完整的结果字典

            rows, cols = main_data.shape
            self.log_message(f"✅ 数据处理完成: {rows} 行 × {cols} 列", False)

            # 显示统计结果信息
            results_summary = processed_data.get('results_summary')
            if results_summary is not None:
                summary_rows, summary_cols = results_summary.shape
                self.log_message(f"📊 生成统计结果: {summary_rows} 行 × {summary_cols} 列", False)

            # 显示时间差信息
            time_diffs = processed_data.get('time_differences', {})
            if time_diffs:
                self.log_message(f"⏱️ SOC时间差 - 98-99: {time_diffs.get('98-99', 0)}, 99-100: {time_diffs.get('99-100', 0)}", False)
        else:
            self.log_message("❌ 数据处理结果为空", True)

        # 自动切换到数据预览标签页
        self.tab_widget.setCurrentIndex(1)

    def on_processing_finished(self, success):
        """数据处理线程结束"""
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)

        if success:
            self.log_message("🎉 数据处理成功完成", False)
        else:
            self.log_message("❌ 数据处理失败", True)

    def start_export(self, file_path, file_format):
        """开始数据导出"""
        if self.processed_data is None:
            self.log_message("❌ 错误: 没有处理后的数据可导出", True)
            return

        if self.export_worker and self.export_worker.isRunning():
            self.log_message("⚠️ 数据导出正在进行中...", True)
            return

        self.log_message(f"📁 开始导出数据到: {file_path}", False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动导出线程
        self.export_worker = ExportWorker(self.processed_data, file_path, file_format)
        self.export_worker.progress_updated.connect(self.progress_bar.setValue)
        self.export_worker.log_message.connect(self.log_message)
        self.export_worker.finished.connect(self.on_export_finished)
        self.export_worker.start()

    def on_export_finished(self, success):
        """数据导出完成"""
        self.progress_bar.setVisible(False)

        if success:
            self.log_message("🎉 数据导出成功完成", False)
            QMessageBox.information(self, "导出成功", "数据已成功导出！")
        else:
            self.log_message("❌ 数据导出失败", True)
            QMessageBox.critical(self, "导出失败", "数据导出过程中发生错误，请查看日志。")

    def log_message(self, message, is_error=False):
        """添加日志消息"""
        if is_error:
            formatted_message = f'<span style="color: red;">{message}</span>'
        else:
            formatted_message = f'<span style="color: black;">{message}</span>'

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)


class ExportReportWindow(ExportReportDialog):
    """导出报告窗口 - 兼容性别名，直接继承ExportReportDialog"""

    def __init__(self, parent, app_data):
        # 直接调用ExportReportDialog的初始化方法
        super().__init__(app_data, parent)
        # 可以在这里覆盖一些设置，如果需要的话
        self.setWindowTitle("导出充电数据报告")


def show_export_dialog(app_data, parent=None):
    """显示导出对话框"""
    dialog = ExportReportDialog(app_data, parent)
    dialog.exec()


if __name__ == "__main__":
    # 测试代码
    import sys
    from PyQt6.QtWidgets import QApplication

    class MockAppData:
        def __init__(self):
            # 创建测试数据
            self.imported_data = pd.DataFrame({
                'SD7_BBAT_SOC_HVS': np.random.uniform(0, 100, 1000),
                'Batt_Current': np.random.uniform(-50, 0, 1000),
                'Batt_Current_req': np.random.uniform(-60, 0, 1000),
                'Batt_Voltage': np.random.uniform(300, 400, 1000),
                'CellU_001': np.random.uniform(3.0, 4.2, 1000),
                'CellU_002': np.random.uniform(3.0, 4.2, 1000),
                'CellU_003': np.random.uniform(3.0, 4.2, 1000),
                'TempSensorTemp_01': np.random.uniform(20, 80, 1000),
                'TempSensorTemp_02': np.random.uniform(20, 80, 1000),
                'BattT_Max': np.random.uniform(25, 45, 1000),
                'BattT_Min': np.random.uniform(20, 40, 1000),
                'Outlet': np.random.uniform(25, 35, 1000),
                'Inlet': np.random.uniform(20, 30, 1000),
            })
            self.filtered_data = None
            self.data_mapping_config = {
                'mappings': {
                    'SD7_BBAT_SOC_HVS': 'SOC',
                    'CellU_001': 'CellU1',
                    'CellU_002': 'CellU2',
                    'CellU_003': 'CellU3',
                }
            }

    app = QApplication(sys.argv)

    # 创建测试数据
    mock_data = MockAppData()

    # 显示对话框
    show_export_dialog(mock_data)

    sys.exit(app.exec())




