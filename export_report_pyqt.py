# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
import traceback

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTextEdit, QProgressBar, QFileDialog, QMessageBox,
        QTabWidget, QTableWidget, QTableWidgetItem,
        QScrollArea, QWidget, QGridLayout, QComboBox
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QThread
    from PyQt6.QtGui import QFont
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6")
    sys.exit(1)


class DataProcessingWorker(QThread):
    """数据处理工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str, bool)  # message, is_error
    data_processed = pyqtSignal(object)  # processed_data
    finished = pyqtSignal(bool)  # success

    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data

    def run(self):
        """执行数据处理"""
        try:
            self.log_message.emit("开始处理数据...", False)
            self.progress_updated.emit(10)

            # 获取数据
            if self.app_data.filtered_data is not None:
                data = self.app_data.filtered_data.copy()
                self.log_message.emit("使用筛选后的数据", False)
            else:
                data = self.app_data.imported_data.copy()
                self.log_message.emit("使用原始导入数据", False)

            self.progress_updated.emit(20)

            # 数据处理和分析
            self.log_message.emit("执行数据分析...", False)
            processed_data = self.process_data_with_columnnamechange_logic(data)
            self.progress_updated.emit(80)

            self.log_message.emit("数据处理完成", False)
            self.progress_updated.emit(100)
            self.data_processed.emit(processed_data)
            self.finished.emit(True)

        except Exception as e:
            error_msg = f"数据处理时发生错误: {str(e)}\n{traceback.format_exc()}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)

    def process_data_with_columnnamechange_logic(self, df):
        """基于columnnamechange.py的数据处理逻辑"""
        df_processed = df.copy()

        self.log_message.emit("开始应用columnnamechange.py逻辑...", False)
        self.progress_updated.emit(30)

        # 1. 处理SOC列 - 检查SD7_BBAT_SOC_HVS或BMS_SOCDis
        if 'SD7_BBAT_SOC_HVS' not in df_processed.columns:
            if 'BMS_SOCDis' in df_processed.columns:
                df_processed['SD7_BBAT_SOC_HVS'] = df_processed['BMS_SOCDis']
                self.log_message.emit("使用BMS_SOCDis作为SD7_BBAT_SOC_HVS", False)
            else:
                self.log_message.emit("警告: 缺少SOC相关列", True)

        self.progress_updated.emit(40)

        # 2. 计算CellU相关列的最大值和最小值
        cellu_columns = [col for col in df_processed.columns if 'CellU' in col]
        if cellu_columns:
            self.log_message.emit(f"找到{len(cellu_columns)}个CellU列", False)
            df_cellu = df_processed[cellu_columns].applymap(lambda x: np.nan if x > 60 else x)
            df_processed['BMS_HvBattCellU_max'] = df_cellu.max(axis=1)
            df_processed['BMS_HvBattCellU_min'] = df_cellu.min(axis=1)
        else:
            self.log_message.emit("警告: 未找到CellU列", True)

        self.progress_updated.emit(50)

        # 3. 计算TempSensorTemp相关列的最大值和最小值
        tempsensortemp_columns = [col for col in df_processed.columns if 'TempSensorTemp' in col]
        if tempsensortemp_columns:
            self.log_message.emit(f"找到{len(tempsensortemp_columns)}个TempSensorTemp列", False)
            df_tempsensortemp = df_processed[tempsensortemp_columns].applymap(lambda x: np.nan if x > 100 else x)
            df_processed['BMS_HvBattTempSensorTemp_max'] = df_tempsensortemp.max(axis=1)
            df_processed['BMS_HvBattTempSensorTemp_min'] = df_tempsensortemp.min(axis=1)
        else:
            self.log_message.emit("警告: 未找到TempSensorTemp列", True)

        self.progress_updated.emit(60)

        # 4. 处理CellVoltage_Max和CellVoltage_Min
        if 'CellVoltage_Max' not in df_processed.columns or df_processed['CellVoltage_Max'].eq(0).all():
            if 'BMS_HvBattCellU_max' in df_processed.columns:
                if 'CellVoltage_Max' in df_processed.columns:
                    df_processed['CellVoltage_Max_old'] = df_processed['CellVoltage_Max']
                df_processed['CellVoltage_Max'] = df_processed['BMS_HvBattCellU_max']
                self.log_message.emit("使用BMS_HvBattCellU_max作为CellVoltage_Max", False)

        if 'CellVoltage_Min' not in df_processed.columns or df_processed['CellVoltage_Min'].eq(0).all():
            if 'BMS_HvBattCellU_min' in df_processed.columns:
                if 'CellVoltage_Min' in df_processed.columns:
                    df_processed['CellVoltage_Min_old'] = df_processed['CellVoltage_Min']
                df_processed['CellVoltage_Min'] = df_processed['BMS_HvBattCellU_min']
                self.log_message.emit("使用BMS_HvBattCellU_min作为CellVoltage_Min", False)

        self.progress_updated.emit(65)

        # 5. 数据筛选 - 删除不符合条件的行
        original_rows = len(df_processed)

        # 删除Batt_Current_req >= 0的行
        if 'Batt_Current_req' in df_processed.columns:
            df_processed = df_processed[df_processed['Batt_Current_req'] < 0]
            self.log_message.emit(f"删除Batt_Current_req >= 0的行", False)

        # 删除CellVoltage_Max == 0的行
        if 'CellVoltage_Max' in df_processed.columns:
            df_processed = df_processed[df_processed['CellVoltage_Max'] != 0]
            self.log_message.emit(f"删除CellVoltage_Max == 0的行", False)

        filtered_rows = len(df_processed)
        self.log_message.emit(f"数据筛选: {original_rows} -> {filtered_rows} 行", False)

        self.progress_updated.emit(70)

        # 6. 计算新列
        df_processed['time'] = range(len(df_processed))

        if 'CellVoltage_Max' in df_processed.columns and 'CellVoltage_Min' in df_processed.columns:
            df_processed['CellVoltage_diff'] = abs(df_processed['CellVoltage_Max'] - df_processed['CellVoltage_Min'])

        if 'BattT_Max' in df_processed.columns and 'BattT_Min' in df_processed.columns:
            df_processed['Temp_diff'] = abs(df_processed['BattT_Max'] - df_processed['BattT_Min'])

        if 'Outlet' in df_processed.columns and 'Inlet' in df_processed.columns:
            df_processed['TempWater_diff'] = abs(df_processed['Outlet'] - df_processed['Inlet'])

        if 'Batt_Current' in df_processed.columns:
            df_processed['Batt_Current'] = abs(df_processed['Batt_Current'])

        if 'Batt_Current_req' in df_processed.columns:
            df_processed['Batt_Current_req'] = abs(df_processed['Batt_Current_req'])

        if 'Batt_Current' in df_processed.columns and 'Batt_Voltage' in df_processed.columns:
            df_processed['POWER'] = -df_processed['Batt_Current'] * df_processed['Batt_Voltage'] / 1000

        self.log_message.emit("计算新列完成", False)
        self.progress_updated.emit(75)

        return df_processed


class ExportWorker(QThread):
    """数据导出工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str, bool)  # message, is_error
    finished = pyqtSignal(bool)  # success

    def __init__(self, data, export_path, file_format):
        super().__init__()
        self.data = data
        self.export_path = export_path
        self.file_format = file_format

    def run(self):
        """执行数据导出"""
        try:
            self.log_message.emit("开始导出数据...", False)
            self.progress_updated.emit(20)

            if self.file_format.lower() == 'csv':
                self.data.to_csv(self.export_path, index=False, encoding='utf-8-sig')
            elif self.file_format.lower() == 'xlsx':
                self.data.to_excel(self.export_path, index=False)
            else:
                raise ValueError(f"不支持的文件格式: {self.file_format}")

            self.progress_updated.emit(90)
            self.log_message.emit(f"数据已成功导出到: {self.export_path}", False)
            self.progress_updated.emit(100)
            self.finished.emit(True)

        except Exception as e:
            error_msg = f"导出数据时发生错误: {str(e)}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)


class SignalStatusWidget(QWidget):
    """信号状态检查组件"""

    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("📊 信号状态检查")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        self.grid_layout = QGridLayout(scroll_widget)

        # 设置表头
        headers = ["信号名称", "状态", "数据源", "备注"]
        for i, header in enumerate(headers):
            label = QLabel(header)
            label.setFont(QFont("Arial", 10, QFont.Weight.Bold))
            label.setStyleSheet("background-color: #f0f0f0; padding: 5px; border: 1px solid #ccc;")
            self.grid_layout.addWidget(label, 0, i)

        scroll_area.setWidget(scroll_widget)
        scroll_area.setWidgetResizable(True)
        layout.addWidget(scroll_area)

        # 刷新按钮
        refresh_btn = QPushButton("🔄 刷新信号状态")
        refresh_btn.clicked.connect(self.refresh_signal_status)
        layout.addWidget(refresh_btn)

        self.setLayout(layout)
        self.refresh_signal_status()

    def refresh_signal_status(self):
        """刷新信号状态"""
        # 清除现有内容（保留表头）
        for i in reversed(range(1, self.grid_layout.rowCount())):
            for j in range(self.grid_layout.columnCount()):
                item = self.grid_layout.itemAtPosition(i, j)
                if item:
                    widget = item.widget()
                    if widget:
                        widget.setParent(None)

        if not self.app_data.imported_data is not None:
            return

        # 定义需要检查的信号
        required_signals = [
            "SD7_BBAT_SOC_HVS", "BMS_SOCDis", "SOC",
            "Batt_Current", "Batt_Current_req", "Batt_Voltage",
            "CellVoltage_Max", "CellVoltage_Min",
            "BattT_Max", "BattT_Min",
            "Outlet", "Inlet",
            "Time", "time"
        ]

        # 获取当前数据列
        current_columns = list(self.app_data.imported_data.columns)

        # 获取映射配置
        mappings = self.app_data.data_mapping_config.get('mappings', {}) if hasattr(self.app_data, 'data_mapping_config') else {}

        row = 1
        for signal in required_signals:
            # 检查信号状态
            status, source, note = self.check_signal_status(signal, current_columns, mappings)

            # 信号名称
            name_label = QLabel(signal)
            name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(name_label, row, 0)

            # 状态
            status_label = QLabel(status)
            if "✅" in status:
                status_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "⚠️" in status:
                status_label.setStyleSheet("color: orange; padding: 3px; border: 1px solid #ddd;")
            else:
                status_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(status_label, row, 1)

            # 数据源
            source_label = QLabel(source)
            source_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(source_label, row, 2)

            # 备注
            note_label = QLabel(note)
            note_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(note_label, row, 3)

            row += 1

        # 检查CellU和TempSensorTemp列
        cellu_columns = [col for col in current_columns if 'CellU' in col]
        tempsensor_columns = [col for col in current_columns if 'TempSensorTemp' in col]

        # CellU列状态
        if cellu_columns:
            status = f"✅ 已找到 ({len(cellu_columns)}个)"
            source = "原始数据"
            note = f"将计算最大值和最小值"
        else:
            status = "❌ 未找到"
            source = "-"
            note = "无法计算电池电压极值"

        for i, (_, content) in enumerate([
            ("CellU列", status), ("", source), ("", note)
        ]):
            if i == 0:
                name_label = QLabel("CellU相关列")
                name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
                self.grid_layout.addWidget(name_label, row, 0)

            content_label = QLabel(content)
            if "✅" in content:
                content_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "❌" in content:
                content_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            else:
                content_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(content_label, row, i + 1)

        row += 1

        # TempSensorTemp列状态
        if tempsensor_columns:
            status = f"✅ 已找到 ({len(tempsensor_columns)}个)"
            source = "原始数据"
            note = f"将计算最大值和最小值"
        else:
            status = "❌ 未找到"
            source = "-"
            note = "无法计算温度传感器极值"

        name_label = QLabel("TempSensorTemp相关列")
        name_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
        self.grid_layout.addWidget(name_label, row, 0)

        for i, content in enumerate([status, source, note]):
            content_label = QLabel(content)
            if "✅" in content:
                content_label.setStyleSheet("color: green; padding: 3px; border: 1px solid #ddd;")
            elif "❌" in content:
                content_label.setStyleSheet("color: red; padding: 3px; border: 1px solid #ddd;")
            else:
                content_label.setStyleSheet("padding: 3px; border: 1px solid #ddd;")
            self.grid_layout.addWidget(content_label, row, i + 1)

    def check_signal_status(self, signal, columns, mappings):
        """检查单个信号的状态"""
        # 直接存在于数据中
        if signal in columns:
            return "✅ 直接可用", "原始数据", "列直接存在于数据中"

        # 通过映射可用
        for csv_col, logic_names in mappings.items():
            if isinstance(logic_names, list):
                if signal in logic_names and csv_col in columns:
                    return "✅ 映射可用", f"映射自 {csv_col}", "通过数据映射获得"
            else:
                if signal == logic_names and csv_col in columns:
                    return "✅ 映射可用", f"映射自 {csv_col}", "通过数据映射获得"

        # 特殊处理SOC相关信号
        if signal == "SD7_BBAT_SOC_HVS" and "BMS_SOCDis" in columns:
            return "⚠️ 可替代", "BMS_SOCDis", "可使用BMS_SOCDis替代"

        if signal == "SOC":
            if "SD7_BBAT_SOC_HVS" in columns:
                return "⚠️ 可替代", "SD7_BBAT_SOC_HVS", "可使用SD7_BBAT_SOC_HVS"
            elif "BMS_SOCDis" in columns:
                return "⚠️ 可替代", "BMS_SOCDis", "可使用BMS_SOCDis"

        return "❌ 缺失", "-", "信号不可用，可能影响分析结果"


class DataPreviewWidget(QWidget):
    """数据预览组件"""

    def __init__(self):
        super().__init__()
        self.processed_data = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题和控制按钮
        header_layout = QHBoxLayout()

        title_label = QLabel("📋 处理后数据预览")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(title_label)

        header_layout.addStretch()

        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新预览")
        self.refresh_btn.clicked.connect(self.refresh_preview)
        self.refresh_btn.setEnabled(False)
        header_layout.addWidget(self.refresh_btn)

        layout.addLayout(header_layout)

        # 数据信息
        self.info_label = QLabel("暂无数据")
        self.info_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(self.info_label)

        # 数据表格
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 设置表格样式
        table_style = """
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f9f9f9;
            }
            QTableWidget::item:selected {
                background-color: #3daee9;
                color: white;
            }
            QHeaderView::section {
                background-color: #f0f0f0;
                padding: 5px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
        """
        self.table.setStyleSheet(table_style)

        layout.addWidget(self.table)

        self.setLayout(layout)

    def update_data(self, processed_data):
        """更新预览数据"""
        self.processed_data = processed_data
        self.refresh_btn.setEnabled(True)
        self.refresh_preview()

    def refresh_preview(self):
        """刷新数据预览"""
        if self.processed_data is None:
            self.info_label.setText("暂无数据")
            self.table.clear()
            self.table.setRowCount(0)
            self.table.setColumnCount(0)
            return

        # 更新信息标签
        rows, cols = self.processed_data.shape
        self.info_label.setText(f"数据维度: {rows} 行 × {cols} 列")

        # 显示前100行数据（避免界面卡顿）
        display_data = self.processed_data.head(100)

        # 设置表格
        self.table.setRowCount(len(display_data))
        self.table.setColumnCount(len(display_data.columns))

        # 设置列标题
        self.table.setHorizontalHeaderLabels(list(display_data.columns))

        # 填充数据
        for i, (_, row) in enumerate(display_data.iterrows()):
            for j, value in enumerate(row):
                if pd.isna(value):
                    item_text = "NaN"
                elif isinstance(value, float):
                    item_text = f"{value:.3f}"
                else:
                    item_text = str(value)

                item = QTableWidgetItem(item_text)
                self.table.setItem(i, j, item)

        # 调整列宽
        self.table.resizeColumnsToContents()

        # 如果数据超过100行，显示提示
        if rows > 100:
            self.info_label.setText(f"数据维度: {rows} 行 × {cols} 列 (仅显示前100行)")


class ExportControlWidget(QWidget):
    """导出控制组件"""

    export_requested = pyqtSignal(str, str)  # file_path, format

    def __init__(self):
        super().__init__()
        self.processed_data = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        layout = QVBoxLayout()

        # 标题
        title_label = QLabel("💾 数据导出")
        title_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        layout.addWidget(title_label)

        # 导出格式选择
        format_layout = QHBoxLayout()
        format_layout.addWidget(QLabel("导出格式:"))

        self.format_combo = QComboBox()
        self.format_combo.addItems(["CSV", "XLSX"])
        self.format_combo.setCurrentText("CSV")
        format_layout.addWidget(self.format_combo)

        format_layout.addStretch()
        layout.addLayout(format_layout)

        # 导出按钮
        button_layout = QHBoxLayout()

        self.export_btn = QPushButton("📁 选择位置并导出")
        self.export_btn.clicked.connect(self.export_data)
        self.export_btn.setEnabled(False)
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        button_layout.addWidget(self.export_btn)

        button_layout.addStretch()
        layout.addLayout(button_layout)

        # 导出状态
        self.status_label = QLabel("请先处理数据")
        self.status_label.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(self.status_label)

        layout.addStretch()
        self.setLayout(layout)

    def update_data(self, processed_data):
        """更新可导出的数据"""
        self.processed_data = processed_data
        self.export_btn.setEnabled(True)
        rows, cols = processed_data.shape
        self.status_label.setText(f"准备导出 {rows} 行 × {cols} 列数据")

    def export_data(self):
        """导出数据"""
        if self.processed_data is None:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return

        # 获取导出格式
        file_format = self.format_combo.currentText().lower()

        # 选择保存位置
        if file_format == "csv":
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存CSV文件",
                f"processed_data.csv",
                "CSV文件 (*.csv)"
            )
        else:
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存Excel文件",
                f"processed_data.xlsx",
                "Excel文件 (*.xlsx)"
            )

        if file_path:
            self.export_requested.emit(file_path, file_format)


class ExportReportDialog(QDialog):
    """优化后的导出报告对话框"""

    def __init__(self, app_data, parent=None):
        super().__init__(parent)
        self.app_data = app_data
        self.processed_data = None
        self.processing_worker = None
        self.export_worker = None
        self.init_ui()

    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("充电分析数据导出")
        self.setGeometry(100, 100, 1200, 800)

        # 主布局
        main_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("⚡ 充电分析数据导出")
        title_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; padding: 10px;")
        main_layout.addWidget(title_label)

        # 创建标签页
        self.tab_widget = QTabWidget()

        # 1. 信号状态检查标签页
        self.signal_status_widget = SignalStatusWidget(self.app_data)
        self.tab_widget.addTab(self.signal_status_widget, "📊 信号状态")

        # 2. 数据预览标签页
        self.data_preview_widget = DataPreviewWidget()
        self.tab_widget.addTab(self.data_preview_widget, "📋 数据预览")

        # 3. 导出控制标签页
        self.export_control_widget = ExportControlWidget()
        self.export_control_widget.export_requested.connect(self.start_export)
        self.tab_widget.addTab(self.export_control_widget, "💾 数据导出")

        main_layout.addWidget(self.tab_widget)

        # 控制按钮区域
        control_layout = QHBoxLayout()

        # 处理数据按钮
        self.process_btn = QPushButton("🔄 处理数据")
        self.process_btn.clicked.connect(self.start_data_processing)
        self.process_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
                color: #7f8c8d;
            }
        """)
        control_layout.addWidget(self.process_btn)

        control_layout.addStretch()

        # 关闭按钮
        close_btn = QPushButton("❌ 关闭")
        close_btn.clicked.connect(self.close)
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        control_layout.addWidget(close_btn)

        main_layout.addLayout(control_layout)

        # 进度条和日志区域
        progress_layout = QVBoxLayout()

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        progress_layout.addWidget(self.progress_bar)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setPlaceholderText("操作日志将在这里显示...")
        progress_layout.addWidget(self.log_text)

        main_layout.addLayout(progress_layout)

        self.setLayout(main_layout)

        # 检查数据可用性
        self.check_data_availability()

    def check_data_availability(self):
        """检查数据可用性"""
        if self.app_data.imported_data is None:
            self.log_message("❌ 错误: 没有导入的数据", True)
            self.process_btn.setEnabled(False)
        else:
            rows, cols = self.app_data.imported_data.shape
            self.log_message(f"✅ 数据已加载: {rows} 行 × {cols} 列", False)
            self.process_btn.setEnabled(True)

    def start_data_processing(self):
        """开始数据处理"""
        if self.processing_worker and self.processing_worker.isRunning():
            self.log_message("⚠️ 数据处理正在进行中...", True)
            return

        self.log_message("🔄 开始数据处理...", False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.process_btn.setEnabled(False)

        # 启动数据处理线程
        self.processing_worker = DataProcessingWorker(self.app_data)
        self.processing_worker.progress_updated.connect(self.progress_bar.setValue)
        self.processing_worker.log_message.connect(self.log_message)
        self.processing_worker.data_processed.connect(self.on_data_processed)
        self.processing_worker.finished.connect(self.on_processing_finished)
        self.processing_worker.start()

    def on_data_processed(self, processed_data):
        """数据处理完成"""
        self.processed_data = processed_data
        self.data_preview_widget.update_data(processed_data)
        self.export_control_widget.update_data(processed_data)

        rows, cols = processed_data.shape
        self.log_message(f"✅ 数据处理完成: {rows} 行 × {cols} 列", False)

        # 自动切换到数据预览标签页
        self.tab_widget.setCurrentIndex(1)

    def on_processing_finished(self, success):
        """数据处理线程结束"""
        self.progress_bar.setVisible(False)
        self.process_btn.setEnabled(True)

        if success:
            self.log_message("🎉 数据处理成功完成", False)
        else:
            self.log_message("❌ 数据处理失败", True)

    def start_export(self, file_path, file_format):
        """开始数据导出"""
        if self.processed_data is None:
            self.log_message("❌ 错误: 没有处理后的数据可导出", True)
            return

        if self.export_worker and self.export_worker.isRunning():
            self.log_message("⚠️ 数据导出正在进行中...", True)
            return

        self.log_message(f"📁 开始导出数据到: {file_path}", False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 启动导出线程
        self.export_worker = ExportWorker(self.processed_data, file_path, file_format)
        self.export_worker.progress_updated.connect(self.progress_bar.setValue)
        self.export_worker.log_message.connect(self.log_message)
        self.export_worker.finished.connect(self.on_export_finished)
        self.export_worker.start()

    def on_export_finished(self, success):
        """数据导出完成"""
        self.progress_bar.setVisible(False)

        if success:
            self.log_message("🎉 数据导出成功完成", False)
            QMessageBox.information(self, "导出成功", "数据已成功导出！")
        else:
            self.log_message("❌ 数据导出失败", True)
            QMessageBox.critical(self, "导出失败", "数据导出过程中发生错误，请查看日志。")

    def log_message(self, message, is_error=False):
        """添加日志消息"""
        if is_error:
            formatted_message = f'<span style="color: red;">{message}</span>'
        else:
            formatted_message = f'<span style="color: black;">{message}</span>'

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)


class ExportReportWindow(ExportReportDialog):
    """导出报告窗口 - 兼容性别名，直接继承ExportReportDialog"""

    def __init__(self, parent, app_data):
        # 直接调用ExportReportDialog的初始化方法
        super().__init__(app_data, parent)
        # 可以在这里覆盖一些设置，如果需要的话
        self.setWindowTitle("导出充电数据报告")


def show_export_dialog(app_data, parent=None):
    """显示导出对话框"""
    dialog = ExportReportDialog(app_data, parent)
    dialog.exec()


if __name__ == "__main__":
    # 测试代码
    import sys
    from PyQt6.QtWidgets import QApplication

    class MockAppData:
        def __init__(self):
            # 创建测试数据
            self.imported_data = pd.DataFrame({
                'SD7_BBAT_SOC_HVS': np.random.uniform(0, 100, 1000),
                'Batt_Current': np.random.uniform(-50, 0, 1000),
                'Batt_Current_req': np.random.uniform(-60, 0, 1000),
                'Batt_Voltage': np.random.uniform(300, 400, 1000),
                'CellU_001': np.random.uniform(3.0, 4.2, 1000),
                'CellU_002': np.random.uniform(3.0, 4.2, 1000),
                'CellU_003': np.random.uniform(3.0, 4.2, 1000),
                'TempSensorTemp_01': np.random.uniform(20, 80, 1000),
                'TempSensorTemp_02': np.random.uniform(20, 80, 1000),
                'BattT_Max': np.random.uniform(25, 45, 1000),
                'BattT_Min': np.random.uniform(20, 40, 1000),
                'Outlet': np.random.uniform(25, 35, 1000),
                'Inlet': np.random.uniform(20, 30, 1000),
            })
            self.filtered_data = None
            self.data_mapping_config = {
                'mappings': {
                    'SD7_BBAT_SOC_HVS': 'SOC',
                    'CellU_001': 'CellU1',
                    'CellU_002': 'CellU2',
                    'CellU_003': 'CellU3',
                }
            }

    app = QApplication(sys.argv)

    # 创建测试数据
    mock_data = MockAppData()

    # 显示对话框
    show_export_dialog(mock_data)

    sys.exit(app.exec())




