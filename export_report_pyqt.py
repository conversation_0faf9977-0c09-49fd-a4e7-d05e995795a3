# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
import os
import threading
import traceback
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTextEdit, QProgressBar, QGroupBox, QFileDialog, QMessageBox
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QThread, QTimer
    from PyQt6.QtGui import QFont
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6")
    sys.exit(1)


class ExportReportWorker(QThread):
    """报告导出工作线程"""
    progress_updated = pyqtSignal(int)
    log_message = pyqtSignal(str, bool)  # message, is_error
    finished = pyqtSignal(bool)  # success
    
    def __init__(self, app_data, export_path):
        super().__init__()
        self.app_data = app_data
        self.export_path = export_path
        
    def run(self):
        """执行报告导出"""
        try:
            self.log_message.emit("开始处理数据...", False)
            self.progress_updated.emit(10)
            
            # 获取数据
            if self.app_data.filtered_data is not None:
                data = self.app_data.filtered_data.copy()
                self.log_message.emit("使用筛选后的数据", False)
            else:
                data = self.app_data.imported_data.copy()
                self.log_message.emit("使用原始导入数据", False)
            
            self.progress_updated.emit(20)
            
            # 数据处理和分析
            self.log_message.emit("执行数据分析...", False)
            processed_data = self.process_data(data)
            self.progress_updated.emit(60)
            
            # 生成报告
            self.log_message.emit("生成报告文件...", False)
            self.generate_report(processed_data)
            self.progress_updated.emit(90)
            
            self.log_message.emit(f"报告已成功导出到: {self.export_path}", False)
            self.progress_updated.emit(100)
            self.finished.emit(True)
            
        except Exception as e:
            error_msg = f"导出报告时发生错误: {str(e)}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)
    
    def _get_mapped_column_name(self, logical_name, df_columns):
        """Safely get the mapped CSV column name."""
        mappings = self.app_data.data_mapping_config.get('mappings', {})
        # Mappings are CSV_Header: Logical_Name. We need to find CSV_Header for a given Logical_Name.
        for csv_header, mapped_logical_name in mappings.items():
            if mapped_logical_name == logical_name:
                if csv_header in df_columns:
                    return csv_header
                else:
                    self.log_message.emit(f"警告: 映射的列 '{csv_header}' (逻辑名: {logical_name}) 不在数据中。", True)
                    return None # Mapped column not found
        # If no mapping for logical_name or mapped column not present, try logical_name itself if it's a column
        if logical_name in df_columns:
            return logical_name
        self.log_message.emit(f"警告: 逻辑列名 '{logical_name}' 既无有效映射也非实际列名。", True)
        return None

    def process_charging_report_data(self, df_input):
        """
        Processes data according to section 1.7 of a requirements document.
        Returns a dictionary of DataFrames/results for the report.
        """
        df = df_input.copy()
        results = {}
        original_cols = df.columns.tolist()

        self.log_message.emit("开始详细数据处理...", False)
        self.progress_updated.emit(25)

        # Get mapped column names (helpers)
        def get_col(logical_name):
            return self._get_mapped_column_name(logical_name, df.columns)

        # 1. SOC Column Handling (Simplified for worker: assume mapped or direct)
        soc_col_logical = 'SOC' # Logical name from requirements (e.g. SD7_BBAT_SOC_HVS)
        # The requirements mention 'SD7_BBAT_SOC_HVS' and 'BMS_SOCDis'.
        # We'll rely on mapping or direct presence of a column named 'SOC' or mapped to 'SOC'.
        # Users should map 'SD7_BBAT_SOC_HVS' or 'BMS_SOCDis' to 'SOC' in data_mapping module.
        soc_col = get_col('SOC')
        if not soc_col:
            # Try specific names if 'SOC' logical mapping fails
            soc_col = get_col('SD7_BBAT_SOC_HVS')
            if not soc_col:
                soc_col = get_col('BMS_SOCDis')

        if not soc_col:
            self.log_message.emit("错误: 未找到SOC列 (尝试映射 'SOC', 'SD7_BBAT_SOC_HVS', 'BMS_SOCDis')。", True)
            raise ValueError("SOC column not found.")
        df.rename(columns={soc_col: 'SOC_processed'}, inplace=True) # Standardize for internal use
        soc_col = 'SOC_processed'
        self.log_message.emit(f"使用列 '{soc_col}' (来自 '{self._get_mapped_column_name(soc_col_logical, original_cols) or soc_col_logical}') 作为SOC。", False)

        # 2. Cell Voltage Extremes
        self.log_message.emit("计算电池单体电压极值...", False)
        cellu_cols_mapped = []
        for col in df.columns: # Iterate actual columns
            # Try to find if any original column maps to a logical 'CellU*' pattern
            # This is tricky without knowing all possible logical names for CellU.
            # Assuming logical names in mapping are like 'CellU1', 'CellU2' or direct CSV like 'CellU_001'
            # For simplicity, we'll look for columns starting with 'CellU' if not explicitly mapped.
            # A more robust way is to have users map them to logical names like 'CellVoltage1', 'CellVoltage2', etc.
            # And then we'd list those logical names.
            # For now, let's assume direct column names or user maps them to something identifiable.
            # The document implies CellU* columns.

            # Let's refine this: User should map individual cell voltages to e.g., "CellU1", "CellU2", ...
            # Then we collect all columns mapped to names starting with "CellU"
            mappings = self.app_data.data_mapping_config.get('mappings', {})
            for csv_header, logical_name_in_map in mappings.items():
                if logical_name_in_map.startswith('CellU') and csv_header in df.columns:
                    if csv_header not in cellu_cols_mapped: # ensure uniqueness
                         cellu_cols_mapped.append(csv_header)

        if not cellu_cols_mapped: # Fallback if no mappings found
            cellu_cols_mapped = [col for col in df.columns if 'CellU' in col and col not in [soc_col]]

        if cellu_cols_mapped:
            df_cellu = df[cellu_cols_mapped].apply(pd.to_numeric, errors='coerce')
            df_cellu = df_cellu.applymap(lambda x: np.nan if x > 60 else x)
            df['BMS_HvBattCellU_max_calc'] = df_cellu.max(axis=1)
            df['BMS_HvBattCellU_min_calc'] = df_cellu.min(axis=1)
            self.log_message.emit(f"计算了 {len(cellu_cols_mapped)} 个CellU列的极值。", False)
        else:
            self.log_message.emit("警告: 未找到 'CellU*' 列用于电压极值计算。", True)
            df['BMS_HvBattCellU_max_calc'] = np.nan
            df['BMS_HvBattCellU_min_calc'] = np.nan
        self.progress_updated.emit(30)

        # 3. Temperature Sensor Extremes
        self.log_message.emit("计算温度传感器极值...", False)
        # Similar logic for TempSensorTemp* columns
        tempsensor_cols_mapped = []
        mappings = self.app_data.data_mapping_config.get('mappings', {})
        for csv_header, logical_name_in_map in mappings.items():
            if logical_name_in_map.startswith('TempSensorTemp') and csv_header in df.columns:
                 if csv_header not in tempsensor_cols_mapped:
                    tempsensor_cols_mapped.append(csv_header)

        if not tempsensor_cols_mapped: # Fallback
            tempsensor_cols_mapped = [col for col in df.columns if 'TempSensorTemp' in col and col not in [soc_col]]

        if tempsensor_cols_mapped:
            df_temp = df[tempsensor_cols_mapped].apply(pd.to_numeric, errors='coerce')
            # Doc says >100C for temp extremes, but also mentions <-30 or >65 for charging analysis. Using stricter from charging.
            df_temp = df_temp.applymap(lambda x: np.nan if not (-30 <= x <= 65) else x)
            df['BattT_Max_calc'] = df_temp.max(axis=1)
            df['BattT_Min_calc'] = df_temp.min(axis=1)
            self.log_message.emit(f"计算了 {len(tempsensor_cols_mapped)} 个TempSensor列的极值。", False)
        else:
            self.log_message.emit("警告: 未找到 'TempSensorTemp*' 列用于温度极值计算。", True)
            df['BattT_Max_calc'] = np.nan
            df['BattT_Min_calc'] = np.nan
        self.progress_updated.emit(35)

        # 4. Replace Voltage Columns if necessary
        # Standard names we expect (user should map to these logical names)
        cv_max_logical = 'CellVoltage_Max'
        cv_min_logical = 'CellVoltage_Min'

        cv_max_col = get_col(cv_max_logical)
        cv_min_col = get_col(cv_min_logical)

        for logical, actual_col, calc_col_name in [
            (cv_max_logical, cv_max_col, 'BMS_HvBattCellU_max_calc'),
            (cv_min_logical, cv_min_col, 'BMS_HvBattCellU_min_calc')]:
            if actual_col: # Column exists (either mapped or direct)
                df[logical] = pd.to_numeric(df[actual_col], errors='coerce')
                if df[logical].eq(0).all() or df[logical].isnull().all():
                    self.log_message.emit(f"列 '{actual_col}' (为 {logical}) 全为0或全为空, 将尝试使用计算值 '{calc_col_name}'。", False)
                    if calc_col_name in df:
                         df[logical] = df[calc_col_name]
                    else:
                         self.log_message.emit(f"警告: 计算列 '{calc_col_name}' 不存在。", True)
            elif calc_col_name in df : # Original column not found, use calculated
                self.log_message.emit(f"列 '{logical}' 未找到, 将使用计算值 '{calc_col_name}'。", False)
                df[logical] = df[calc_col_name]
            else: # Neither original nor calculated found
                self.log_message.emit(f"警告: 列 '{logical}' 及其计算备用 '{calc_col_name}' 均未找到。", True)
                df[logical] = np.nan

        # Ensure these columns exist even if all attempts failed, fill with NaN
        if cv_max_logical not in df.columns: df[cv_max_logical] = np.nan
        if cv_min_logical not in df.columns: df[cv_min_logical] = np.nan

        self.progress_updated.emit(40)

        # 5. Filter Invalid Rows
        self.log_message.emit("过滤无效数据行...", False)
        initial_rows = len(df)
        batt_current_req_col = get_col('Batt_Current_req')
        if batt_current_req_col:
            df[batt_current_req_col] = pd.to_numeric(df[batt_current_req_col], errors='coerce')
            df = df[df[batt_current_req_col] < 0]
            self.log_message.emit(f"过滤后 (基于 {batt_current_req_col} < 0): {len(df)} 行", False)
        else:
            self.log_message.emit(f"警告: 列 'Batt_Current_req' 未找到, 无法按电流请求过滤。", True)

        if cv_max_logical in df:
            df = df[df[cv_max_logical] != 0] # Assuming it's numeric after previous step
            df = df[df[cv_max_logical].notna()]
            self.log_message.emit(f"过滤后 (基于 {cv_max_logical} != 0 and not NaN): {len(df)} 行", False)
        else:
             self.log_message.emit(f"警告: 列 '{cv_max_logical}' 不存在, 无法按最大电压过滤。", True)
        self.log_message.emit(f"数据行从 {initial_rows} 减少到 {len(df)}。", False)

        if df.empty:
            self.log_message.emit("错误: 过滤后无有效数据。", True)
            raise ValueError("No valid data after filtering.")
        self.progress_updated.emit(45)

        # 6. Derived Metrics
        self.log_message.emit("计算衍生指标...", False)
        df['time_internal_sec'] = range(len(df)) # Assuming 1 row = 1 second, or time col should be mapped

        if cv_max_logical in df and cv_min_logical in df:
            df['CellVoltage_diff_calc'] = (df[cv_max_logical] - df[cv_min_logical]).abs()
        else:
            df['CellVoltage_diff_calc'] = np.nan

        # Use calculated BattT_Max_calc and BattT_Min_calc if they exist
        if 'BattT_Max_calc' in df and 'BattT_Min_calc' in df:
             df['Temp_diff_calc'] = (df['BattT_Max_calc'] - df['BattT_Min_calc']).abs()
        else: # Fallback to mapped BattT_Max/Min if calc versions aren't there
            bt_max_col = get_col('BattT_Max')
            bt_min_col = get_col('BattT_Min')
            if bt_max_col and bt_min_col and bt_max_col in df and bt_min_col in df:
                df['Temp_diff_calc'] = (pd.to_numeric(df[bt_max_col], errors='coerce') - pd.to_numeric(df[bt_min_col], errors='coerce')).abs()
            else:
                df['Temp_diff_calc'] = np.nan

        outlet_col = get_col('Outlet')
        inlet_col = get_col('Inlet')
        if outlet_col and inlet_col and outlet_col in df and inlet_col in df:
            df['TempWater_diff_calc'] = (pd.to_numeric(df[outlet_col], errors='coerce') - pd.to_numeric(df[inlet_col], errors='coerce')).abs()
        else:
            df['TempWater_diff_calc'] = np.nan

        batt_current_col = get_col('Batt_Current')
        batt_voltage_col = get_col('Batt_Voltage')
        if batt_current_col and batt_voltage_col and batt_current_col in df and batt_voltage_col in df:
            df['POWER_calc'] = - pd.to_numeric(df[batt_current_col], errors='coerce') * pd.to_numeric(df[batt_voltage_col], errors='coerce') / 1000
        else:
            df['POWER_calc'] = np.nan
        self.progress_updated.emit(50)

        # 7. SOC Time Differences
        self.log_message.emit("计算SOC时间差...", False)
        time_diff_98_99 = np.nan
        time_diff_99_100 = np.nan

        df[soc_col] = pd.to_numeric(df[soc_col], errors='coerce')

        soc_ge98_df = df[df[soc_col] >= 98]
        if not soc_ge98_df.empty:
            time_at_soc98_start = soc_ge98_df['time_internal_sec'].iloc[0]

            # SOC 98% to 99%
            soc_lt99_after_98_df = soc_ge98_df[soc_ge98_df[soc_col] < 99]
            if not soc_lt99_after_98_df.empty:
                time_at_soc99_end_for_98_99_range = soc_lt99_after_98_df['time_internal_sec'].iloc[-1]
                time_diff_98_99 = time_at_soc99_end_for_98_99_range - time_at_soc98_start
            else: # Might have jumped past 99% or stayed at 98%
                 # If all SOC >= 98 are also >= 99, then 98-99 time is 0 or undefined.
                 if not soc_ge98_df[soc_ge98_df[soc_col] < 99].empty: # Still some values below 99
                     time_diff_98_99 = 0 # implies it hit 99 immediately or was already there
                 else: # All values are >=99 after hitting 98
                     # Check if any point was exactly 98
                     if not df[(df[soc_col] == 98) & (df['time_internal_sec'] >= time_at_soc98_start)].empty:
                         # It means it hit 98 but never went to <99 while being >=98.
                         # This case might need more specific logic based on desired interpretation.
                         # For now, if it hits 98 and then immediately is >=99, time_diff_98_99 could be 0.
                         # If it stays at 98 and never reaches 99, it's NaN.
                         if not df[(df[soc_col] >= 99) & (df['time_internal_sec'] > time_at_soc98_start)].empty:
                            time_at_soc99_start_strict = df[(df[soc_col] >= 99) & (df['time_internal_sec'] > time_at_soc98_start)]['time_internal_sec'].iloc[0]
                            time_diff_98_99 = time_at_soc99_start_strict - time_at_soc98_start


            # SOC 99% to 100%
            soc_ge99_df = df[df[soc_col] >= 99] # Re-filter from original df for this range
            if not soc_ge99_df.empty:
                time_at_soc99_start = soc_ge99_df['time_internal_sec'].iloc[0]

                # Ensure we are looking for time to 100% *after* or *at* hitting 99%
                soc_lt100_after_99_df = soc_ge99_df[soc_ge99_df[soc_col] < 100]
                if not soc_lt100_after_99_df.empty:
                    time_at_soc100_end_for_99_100_range = soc_lt100_after_99_df['time_internal_sec'].iloc[-1]
                    time_diff_99_100 = time_at_soc100_end_for_99_100_range - time_at_soc99_start
                else: # All SOC >= 99 are also >= 100
                    if not df[(df[soc_col] == 99) & (df['time_internal_sec'] >= time_at_soc99_start)].empty:
                         if not df[(df[soc_col] >= 100) & (df['time_internal_sec'] > time_at_soc99_start)].empty:
                            time_at_soc100_start_strict = df[(df[soc_col] >= 100) & (df['time_internal_sec'] > time_at_soc99_start)]['time_internal_sec'].iloc[0]
                            time_diff_99_100 = time_at_soc100_start_strict - time_at_soc99_start


        results['time_diff_98_99_sec'] = time_diff_98_99
        results['time_diff_99_100_sec'] = time_diff_99_100
        self.log_message.emit(f"Time 98-99%: {time_diff_98_99}s, Time 99-100%: {time_diff_99_100}s", False)
        self.progress_updated.emit(55)

        # 8. Segmented Statistics
        self.log_message.emit("生成分段统计...", False)
        segment_stats_list = []
        # Columns for which to generate stats (use logical names, then map)
        # These are examples, user should map them.
        stat_cols_logical = [
            'CellVoltage_Max', 'CellVoltage_Min', 'CellVoltage_diff_calc',
            'BattT_Max_calc', 'BattT_Min_calc', 'Temp_diff_calc',
            'POWER_calc', soc_col # Add SOC itself to see its start/end/max in segment
        ]

        actual_stat_cols = []
        for log_col in stat_cols_logical:
            act_col = None
            if log_col in df.columns: # Already processed/calculated columns
                act_col = log_col
            else: # Try to map original columns
                act_col = get_col(log_col)

            if act_col and act_col in df.columns:
                actual_stat_cols.append(act_col)
                # Ensure numeric for stats
                df[act_col] = pd.to_numeric(df[act_col], errors='coerce')
            else:
                self.log_message.emit(f"警告: 统计列 '{log_col}' 未找到或无法使用。", True)

        if actual_stat_cols:
            max_soc_val = df[soc_col].max() if not df[soc_col].empty else 0
            for curr_soc_segment_start in range(0, 100, 10):
                if curr_soc_segment_start >= max_soc_val + 10 and curr_soc_segment_start > 0 : # stop if past max SOC
                    if curr_soc_segment_start > max_soc_val : break # ensure 90-100 runs if max_soc is e.g. 95

                soc_segment_end = curr_soc_segment_start + 10
                segment_df = df[(df[soc_col] >= curr_soc_segment_start) & (df[soc_col] < soc_segment_end)]

                segment_summary = {'SOC_Segment': f"{curr_soc_segment_start}-{soc_segment_end}%"}
                if segment_df.empty:
                    for col_name in actual_stat_cols:
                        segment_summary[f"{col_name}_start"] = np.nan
                        segment_summary[f"{col_name}_end"] = np.nan
                        segment_summary[f"{col_name}_max"] = np.nan
                else:
                    for col_name in actual_stat_cols:
                        col_series = segment_df[col_name].dropna()
                        if not col_series.empty:
                            segment_summary[f"{col_name}_start"] = col_series.iloc[0]
                            segment_summary[f"{col_name}_end"] = col_series.iloc[-1]
                            segment_summary[f"{col_name}_max"] = col_series.max()
                        else:
                            segment_summary[f"{col_name}_start"] = np.nan
                            segment_summary[f"{col_name}_end"] = np.nan
                            segment_summary[f"{col_name}_max"] = np.nan
                segment_stats_list.append(segment_summary)

        results['segment_statistics'] = pd.DataFrame(segment_stats_list)

        # For "绘图数据" (plotting data), the requirement is "精简格式".
        # This could mean the processed `df` itself, or a subset of its columns.
        # Let's select key columns for the plotting data.
        plot_data_cols_logical = [
            'time_internal_sec', soc_col,
            'CellVoltage_Max', 'CellVoltage_Min', 'CellVoltage_diff_calc',
            'BattT_Max_calc', 'BattT_Min_calc', 'Temp_diff_calc', 'POWER_calc'
        ]
        plot_data_actual_cols = [col for col in plot_data_cols_logical if col in df.columns]
        results['plotting_data'] = df[plot_data_actual_cols]

        self.log_message.emit("详细数据处理完成。", False)
        return results

    def run(self):
        """执行报告导出"""
        try:
            self.log_message.emit("开始处理数据...", False)
            self.progress_updated.emit(10)

            # 获取数据
            if self.app_data.filtered_data is not None:
                data = self.app_data.filtered_data.copy()
                self.log_message.emit("使用筛选后的数据", False)
            elif self.app_data.imported_data is not None:
                data = self.app_data.imported_data.copy()
                self.log_message.emit("使用原始导入数据", False)
            else:
                self.log_message.emit("错误: 无可处理的数据。", True)
                self.finished.emit(False)
                return

            self.progress_updated.emit(20)

            # 数据处理和分析
            self.log_message.emit("执行详细数据分析 (充电报告)...", False)
            # processed_data = self.process_data(data) # Old basic processing
            processed_report_data = self.process_charging_report_data(data)
            self.progress_updated.emit(80) # Processing is the bulk

            # 生成报告
            self.log_message.emit("生成报告文件...", False)
            self.generate_report(processed_report_data) # Pass new processed data
            self.progress_updated.emit(95)

            self.log_message.emit(f"报告已成功导出到: {self.export_path}", False)
            self.progress_updated.emit(100)
            self.finished.emit(True)

        except Exception as e:
            error_msg = f"导出报告时发生错误: {str(e)}\n{traceback.format_exc()}"
            self.log_message.emit(error_msg, True)
            self.finished.emit(False)
    
    def generate_report(self, processed_report_data):
        """生成报告文件 - Updated for detailed charging report"""
        with open(self.export_path, 'w', encoding='utf-8') as f:
            f.write("# 充电数据分析报告\n\n")

            # SOC Time Differences
            f.write("## SOC关键阶段时间差\n")
            f.write(f"- SOC 98% 到 99% 时间: {processed_report_data.get('time_diff_98_99_sec', 'N/A')} 秒\n")
            f.write(f"- SOC 99% 到 100% 时间: {processed_report_data.get('time_diff_99_100_sec', 'N/A')} 秒\n\n")

            # Segment Statistics
            f.write("## 分段统计数据 (按SOC每10%)\n")
            segment_stats_df = processed_report_data.get('segment_statistics')
            if segment_stats_df is not None and not segment_stats_df.empty:
                # For text report, transpose might be better if too many columns
                # The requirements say "转置格式" for stats.
                f.write(segment_stats_df.set_index('SOC_Segment').T.to_string(na_rep='N/A'))
                f.write("\n\n")
            else:
                f.write("无分段统计数据可显示。\n\n")

            # Plotting Data (mention it or save separately if too large)
            # For a text report, we might just state that plotting data is available
            # or save it to a separate CSV. The requirement is "精简格式" for plotting data.
            # For now, let's just indicate its availability.
            # If the user wants the plotting data as a separate CSV, that's another feature.
            plotting_df = processed_report_data.get('plotting_data')
            if plotting_df is not None and not plotting_df.empty:
                f.write("## 绘图用数据摘要\n")
                f.write(f"绘图用数据包含 {len(plotting_df)} 行 和 {len(plotting_df.columns)} 列。\n")
                f.write(f"列名: {', '.join(plotting_df.columns)}\n")
                f.write("前5行数据预览:\n")
                f.write(plotting_df.head().to_string(na_rep='N/A'))
                f.write("\n\n")
            
            f.write("-- 报告结束 --\n")


class ExportReportWindow(QDialog):
    """导出报告窗口 - 参考export_report.py"""
    
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.setWindowTitle("导出充电数据报告")
        self.setGeometry(200, 200, 700, 550)
        self.setModal(True)
        
        self.worker = None
        self.init_ui()
        self.log_message("准备就绪。点击按钮开始生成报告。")
        
    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 操作按钮
        self.action_button = QPushButton("数据处理并导出报告")
        self.action_button.clicked.connect(self.start_report_generation)
        self.action_button.setMinimumHeight(40)
        main_layout.addWidget(self.action_button)
        
        # 日志区域
        log_group = QGroupBox("处理日志和状态")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Courier New", 9))
        self.log_text.setMinimumHeight(300)
        log_layout.addWidget(self.log_text)
        
        main_layout.addWidget(log_group)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        main_layout.addWidget(self.progress_bar)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.return_button = QPushButton("返回主页面")
        self.return_button.clicked.connect(self.close)
        button_layout.addWidget(self.return_button)
        
        main_layout.addLayout(button_layout)
        
    def log_message(self, message, is_error=False):
        """添加日志消息"""
        if is_error:
            self.log_text.append(f"<span style='color: red;'>[错误] {message}</span>")
        else:
            self.log_text.append(f"[信息] {message}")
        
        # 滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
        
    def start_report_generation(self):
        """开始生成报告"""
        # 检查数据
        if self.app_data.imported_data is None:
            QMessageBox.critical(self, "无数据", "请先导入主数据文件。")
            return
        
        # 检查映射配置
        if not hasattr(self.app_data, 'data_mapping_config') or \
           not self.app_data.data_mapping_config or \
           not isinstance(self.app_data.data_mapping_config.get('mappings'), dict) or \
           not self.app_data.data_mapping_config.get('mappings'):
            reply = QMessageBox.question(
                self, 
                "映射不完整?", 
                "数据映射不完整或未进行。报告可能不准确或处理失败。\n是否仍要尝试生成报告?",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                QMessageBox.StandardButton.No
            )
            if reply == QMessageBox.StandardButton.No:
                return
        
        # 选择保存位置
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存报告文件",
            "数据分析报告.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )
        
        if not file_path:
            return
        
        # 禁用按钮
        self.action_button.setEnabled(False)
        self.return_button.setEnabled(False)
        
        # 清空日志
        self.log_text.clear()
        self.progress_bar.setValue(0)
        
        # 启动工作线程
        self.worker = ExportReportWorker(self.app_data, file_path)
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.log_message.connect(self.log_message)
        self.worker.finished.connect(self.on_export_finished)
        self.worker.start()
        
    def on_export_finished(self, success):
        """导出完成处理"""
        self.action_button.setEnabled(True)
        self.return_button.setEnabled(True)
        
        if success:
            QMessageBox.information(self, "导出成功", "报告已成功生成！")
        else:
            QMessageBox.critical(self, "导出失败", "报告生成过程中发生错误，请查看日志。")
