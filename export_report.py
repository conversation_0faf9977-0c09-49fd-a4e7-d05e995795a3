# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import os
import threading
import traceback

class ExportReportWindow(tk.Toplevel):
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.title("导出充电数据报告")
        self.geometry("700x550") # Increased height for more log visibility

        self.transient(parent)
        self.grab_set()

        self.processing_thread = None
        self._create_widgets()
        self.log_message("准备就绪。点击按钮开始生成报告。")
        self.protocol("WM_DELETE_WINDOW", self.custom_destroy)

    def _create_widgets(self):
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        self.action_button = ttk.Button(main_frame, text="数据处理并导出报告", command=self.start_report_generation_thread)
        self.action_button.pack(pady=10, ipady=10, ipadx=10)

        log_frame = ttk.LabelFrame(main_frame, text="处理日志和状态", padding="10")
        log_frame.pack(expand=True, fill=tk.BOTH, pady=5)

        self.log_text = tk.Text(log_frame, height=20, width=80, state=tk.DISABLED, wrap=tk.WORD, font=("Courier New", 9))
        log_scrollbar = ttk.Scrollbar(log_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.config(yscrollcommand=log_scrollbar.set)
        self.log_text.pack(side=tk.LEFT, expand=True, fill=tk.BOTH)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.progress_var = tk.DoubleVar()
        self.progressbar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progressbar.pack(fill=tk.X, pady=(5,0))

        self.return_button = ttk.Button(main_frame, text="返回主页面", command=self.custom_destroy)
        self.return_button.pack(pady=(10,0), side=tk.RIGHT)

    def log_message(self, message, is_error=False):
        if not self.winfo_exists(): return
        self.log_text.config(state=tk.NORMAL)
        timestamp = pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
        prefix = "错误: " if is_error else "信息: "
        self.log_text.insert(tk.END, f"[{timestamp}] {prefix}{message}\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        if hasattr(self.parent, 'update_idletasks'): self.parent.update_idletasks()


    def update_progress(self, value):
        if not self.winfo_exists(): return
        self.progress_var.set(value)
        if hasattr(self.parent, 'update_idletasks'): self.parent.update_idletasks()


    def start_report_generation_thread(self):
        if not hasattr(self.app_data, 'imported_data') or self.app_data.imported_data is None:
            if self.winfo_exists(): messagebox.showerror("无数据", "请先导入主数据文件。", parent=self)
            return

        # Check for mapping config, but allow proceeding if user agrees
        if not hasattr(self.app_data, 'data_mapping_config') or \
           not self.app_data.data_mapping_config or \
           not isinstance(self.app_data.data_mapping_config.get('mappings'), dict) or \
           not self.app_data.data_mapping_config.get('mappings'):
            if self.winfo_exists() and not messagebox.askyesno("映射不完整?", "数据映射不完整或未进行。报告可能不准确或处理失败。\n是否仍要尝试生成报告?", parent=self):
                return

        self.action_button.config(state=tk.DISABLED)
        self.return_button.config(state=tk.DISABLED)
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

        self.log_message("开始生成报告流程...")
        self.update_progress(0)

        self.processing_thread = threading.Thread(target=self.process_and_export_core, daemon=True)
        self.processing_thread.start()

    def _get_mapped_column_name(self, logical_name, default_csv_name=None, is_critical=False, df_columns_list=None):
        if df_columns_list is None:
            if not hasattr(self.app_data, 'imported_data') or self.app_data.imported_data is None:
                if is_critical: raise ValueError(f"无法获取列 '{logical_name}': 主数据未导入。")
                self.log_message(f"警告: 无法检查列 '{logical_name}'，主数据未导入。", True)
                return None
            df_columns_list = self.app_data.imported_data.columns

        # Try to get from global mapping first
        if hasattr(self.app_data, 'data_mapping_config') and \
           self.app_data.data_mapping_config and \
           isinstance(self.app_data.data_mapping_config.get('mappings'), dict):

            mappings = self.app_data.data_mapping_config['mappings'] # csv_col: logical_name
            reverse_mappings = {v: k for k, v in mappings.items()} # logical_name: csv_col

            mapped_csv_col = reverse_mappings.get(logical_name)
            if mapped_csv_col and mapped_csv_col in df_columns_list:
                self.log_message(f"列 '{logical_name}' -> CSV '{mapped_csv_col}' (通过全局映射)")
                return mapped_csv_col
            elif mapped_csv_col:
                 self.log_message(f"信息: 逻辑名 '{logical_name}' 映射到 '{mapped_csv_col}', 但该列不在当前数据中。")


        # Fallback to default_csv_name if mapping fails or not configured
        if default_csv_name and default_csv_name in df_columns_list:
            self.log_message(f"列 '{logical_name}' 使用默认CSV名 '{default_csv_name}' (映射未成功或无配置)")
            return default_csv_name

        if is_critical:
            msg = f"关键列的逻辑名 '{logical_name}' (或默认CSV名 '{default_csv_name}') 在数据中未找到或未映射。"
            self.log_message(msg, is_error=True)
            raise ValueError(msg)

        self.log_message(f"警告: 列 '{logical_name}' (或默认CSV名 '{default_csv_name}') 未找到，相关计算将受影响。")
        return None

    def process_and_export_core(self):
        try:
            if not hasattr(self.app_data, 'imported_data') or self.app_data.imported_data is None:
                 raise ValueError("主数据未导入。")
            df = self.app_data.imported_data.copy()
            self.log_message("1. 数据已复制。")
            self.update_progress(5)

            # --- Step 2: Resolve column names using mapping then defaults ---
            soc_col = self._get_mapped_column_name("SOC_HVS", "SD7_BBAT_SOC_HVS", True, df.columns)
            if not soc_col: # Secondary fallback for SOC as it's very critical
                 soc_col = self._get_mapped_column_name("SOC_Display", "BMS_SOCDis", True, df.columns)

            cv_max_col = self._get_mapped_column_name("CellVoltage_Max", "CellVoltage_Max", True, df.columns)
            cv_min_col = self._get_mapped_column_name("CellVoltage_Min", "CellVoltage_Min", True, df.columns)
            bt_max_col = self._get_mapped_column_name("BattT_Max", "BattT_Max", True, df.columns)
            bt_min_col = self._get_mapped_column_name("BattT_Min", "BattT_Min", True, df.columns)
            bcr_col = self._get_mapped_column_name("Batt_Current_req", "Batt_Current_req", True, df.columns)
            bc_col = self._get_mapped_column_name("Batt_Current", "Batt_Current", True, df.columns)
            bv_col = self._get_mapped_column_name("Batt_Voltage", "Batt_Voltage", True, df.columns)

            outlet_col = self._get_mapped_column_name("Outlet", "Outlet", False, df.columns)
            inlet_col = self._get_mapped_column_name("Inlet", "Inlet", False, df.columns)
            self.log_message("2. 列名解析完成。")
            self.update_progress(10)

            # --- Step 2.5: Cell Voltage Processing (CellU*) ---
            # Determine prefix for CellU columns: use mapped "CellVoltagePrefix" or default "CellU"
            cell_u_prefix_logical = "CellVoltagePrefix"
            cell_u_prefix_default_csv = "CellU"

            # This assumes "CellVoltagePrefix" would be mapped to the *actual prefix string* in the CSV.
            # E.g., if CSVs have "MyPrefix_01", "MyPrefix_02", then "CellVoltagePrefix" -> "MyPrefix_"
            # This is a bit different from other mappings (logical name -> full CSV column name).
            # For now, we'll try to get it. If not directly mapped, use default.

            prefix_from_mapping = None
            if hasattr(self.app_data, 'data_mapping_config') and self.app_data.data_mapping_config and isinstance(self.app_data.data_mapping_config.get('mappings'), dict):
                reverse_map = {v:k for k,v in self.app_data.data_mapping_config['mappings'].items()}
                prefix_from_mapping = reverse_map.get(cell_u_prefix_logical)

            effective_cell_u_prefix = prefix_from_mapping if prefix_from_mapping else cell_u_prefix_default_csv
            if prefix_from_mapping:
                self.log_message(f"单体电压列使用前缀 '{effective_cell_u_prefix}' (来自逻辑名 '{cell_u_prefix_logical}')")
            else:
                self.log_message(f"单体电压列前缀 '{cell_u_prefix_logical}' 未映射, 使用默认前缀 '{effective_cell_u_prefix}'")

            cell_u_cols = [c for c in df.columns if isinstance(c, str) and c.startswith(effective_cell_u_prefix)]
            if cell_u_cols:
                df_cu = df[cell_u_cols].apply(pd.to_numeric, errors='coerce')
                # Filter abnormal cell voltages: > 6.0V or < 0V (typical range for Li-ion cells)
                df_cu = df_cu.applymap(lambda x: np.nan if pd.notna(x) and (x > 6.0 or x < 0) else x)

                df['BMS_HvBattCellU_max_calc'] = df_cu.max(axis=1)
                df['BMS_HvBattCellU_min_calc'] = df_cu.min(axis=1)
                self.log_message(f"从 {len(cell_u_cols)} 个单体电压列 (前缀'{effective_cell_u_prefix}') 计算了最大/最小值。")

                for orig_col, calc_col in [(cv_max_col, 'BMS_HvBattCellU_max_calc'), (cv_min_col, 'BMS_HvBattCellU_min_calc')]:
                    if df[orig_col].isnull().all() or (pd.api.types.is_numeric_dtype(df[orig_col]) and df[orig_col].eq(0).all()):
                        df[orig_col] = df[calc_col]
                        self.log_message(f"原始列 '{orig_col}' 为空或全0, 已用计算值 '{calc_col}' 填充。")
            else:
                self.log_message(f"警告: 未找到任何以 '{effective_cell_u_prefix}' 开头的单体电压列。")
            self.update_progress(20)

            # --- Step 2.6: Temperature Data Cleaning (BattT_Max, BattT_Min) ---
            # Filter abnormal temperatures, e.g., -40 to 100 °C
            for temp_col_name in [bt_max_col, bt_min_col]:
                if temp_col_name and temp_col_name in df.columns:
                    original_type = df[temp_col_name].dtype
                    df[temp_col_name] = pd.to_numeric(df[temp_col_name], errors='coerce')
                    # Apply filter: keep NaNs, filter out-of-range numerics
                    df[temp_col_name] = df[temp_col_name].apply(lambda x: np.nan if pd.notna(x) and (x < -40 or x > 100) else x)
                    if df[temp_col_name].isnull().all() and not pd.to_numeric(self.app_data.imported_data[temp_col_name], errors='coerce').isnull().all() :
                         self.log_message(f"警告: 列 '{temp_col_name}' 在温度过滤后全为空值。", is_error=True)
                    elif original_type != df[temp_col_name].dtype : # If type changed due to NaNs
                         self.log_message(f"信息: 列 '{temp_col_name}' 已进行温度过滤 (-40°C to 100°C)。")


            # --- Step 3: Data Filtering for Charging State ---
            df[bcr_col] = pd.to_numeric(df[bcr_col], errors='coerce')
            df[cv_max_col] = pd.to_numeric(df[cv_max_col], errors='coerce') # Ensure numeric before comparison

            df = df[df[bcr_col] < 0] # Charging current request is negative
            df = df[df[cv_max_col] != 0] # Max cell voltage should not be zero
            df.reset_index(drop=True, inplace=True)
            if df.empty:
                self.log_message("筛选充电状态后无有效数据。", is_error=True)
                raise ValueError("筛选充电状态后无有效数据。")
            self.log_message(f"3. 数据筛选完成, 剩余 {len(df)} 行有效充电数据。")
            self.update_progress(30)

            # --- Step 4: Calculate Derived Metrics ---
            df['time_idx_report'] = range(len(df)) # Represents seconds if data is 1Hz
            df[cv_max_col] = pd.to_numeric(df[cv_max_col], errors='coerce')
            df[cv_min_col] = pd.to_numeric(df[cv_min_col], errors='coerce')
            df[bt_max_col] = pd.to_numeric(df[bt_max_col], errors='coerce')
            df[bt_min_col] = pd.to_numeric(df[bt_min_col], errors='coerce')
            df[bc_col] = pd.to_numeric(df[bc_col], errors='coerce')
            df[bv_col] = pd.to_numeric(df[bv_col], errors='coerce')

            df['CellVoltage_diff'] = (df[cv_max_col] - df[cv_min_col]).abs()
            df['Temp_diff'] = (df[bt_max_col] - df[bt_min_col]).abs()

            if outlet_col and inlet_col and outlet_col in df.columns and inlet_col in df.columns:
                df[outlet_col] = pd.to_numeric(df[outlet_col], errors='coerce')
                df[inlet_col] = pd.to_numeric(df[inlet_col], errors='coerce')
                df['TempWater_diff'] = (df[outlet_col] - df[inlet_col]).abs()
            else:
                df['TempWater_diff'] = np.nan
            df['POWER'] = -df[bc_col] * df[bv_col] / 1000 # Power in kW
            self.log_message("4. 衍生指标计算完成。")
            self.update_progress(50)

            # --- Step 5: Calculate Trickle Charge Time ---
            df[soc_col] = pd.to_numeric(df[soc_col], errors='coerce')
            t98, t99 = np.nan, np.nan

            # Time from first SOC >= 98% to last SOC < 99% (within the >=98% block)
            df_soc98_plus = df[df[soc_col] >= 98].copy() # Use .copy() to avoid SettingWithCopyWarning
            if not df_soc98_plus.empty:
                df_soc98_plus.sort_values('time_idx_report', inplace=True)
                first_time_at_98 = df_soc98_plus['time_idx_report'].iloc[0]

                # Find last point where SOC is still < 99% AFTER first reaching 98%
                df_between_98_99 = df_soc98_plus[df_soc98_plus[soc_col] < 99]
                if not df_between_98_99.empty:
                    last_time_before_99 = df_between_98_99['time_idx_report'].iloc[-1]
                    t98 = last_time_before_99 - first_time_at_98
                elif not df_soc98_plus[df_soc98_plus[soc_col] >= 99].empty: # If it jumps to 99 or more immediately
                    first_time_at_99_or_more_in_block = df_soc98_plus[df_soc98_plus[soc_col] >= 99]['time_idx_report'].iloc[0]
                    t98 = first_time_at_99_or_more_in_block - first_time_at_98

            # Time from first SOC >= 99% to last SOC < 100% (within the >=99% block)
            df_soc99_plus = df[df[soc_col] >= 99].copy()
            if not df_soc99_plus.empty:
                df_soc99_plus.sort_values('time_idx_report', inplace=True)
                first_time_at_99 = df_soc99_plus['time_idx_report'].iloc[0]

                df_between_99_100 = df_soc99_plus[df_soc99_plus[soc_col] < 100]
                if not df_between_99_100.empty:
                    last_time_before_100 = df_between_99_100['time_idx_report'].iloc[-1]
                    t99 = last_time_before_100 - first_time_at_99
                elif not df_soc99_plus[df_soc99_plus[soc_col] >= 100].empty: # Jumps to 100
                    first_time_at_100_in_block = df_soc99_plus[df_soc99_plus[soc_col] >= 100]['time_idx_report'].iloc[0]
                    t99 = first_time_at_100_in_block - first_time_at_99

            self.log_message(f"5. 涓流时间: 98-99% SOC用时约 {t98 if pd.notna(t98) else 'N/A'}s, 99-100% SOC用时约 {t99 if pd.notna(t99) else 'N/A'}s。")
            self.update_progress(70)

            # --- Step 6: Generate Segment Statistics ---
            stat_cols_logical = ['CellVoltage_Max', 'CellVoltage_Min', 'CellVoltage_diff',
                                 'BattT_Max', 'BattT_Min', 'Temp_diff',
                                 'TempWater_diff', 'Batt_Current', 'Batt_Voltage', 'POWER']
            # Get actual CSV column names for these logical names
            stat_cols_actual = []
            for logical in stat_cols_logical:
                # Use the already resolved column variables (e.g. cv_max_col) or re-resolve if necessary
                # For simplicity, we assume the variables like cv_max_col, bc_col etc. are the correct ones to use
                if logical == 'CellVoltage_Max': stat_cols_actual.append(cv_max_col)
                elif logical == 'CellVoltage_Min': stat_cols_actual.append(cv_min_col)
                elif logical == 'CellVoltage_diff': stat_cols_actual.append('CellVoltage_diff') # Derived
                elif logical == 'BattT_Max': stat_cols_actual.append(bt_max_col)
                elif logical == 'BattT_Min': stat_cols_actual.append(bt_min_col)
                elif logical == 'Temp_diff': stat_cols_actual.append('Temp_diff') # Derived
                elif logical == 'TempWater_diff': stat_cols_actual.append('TempWater_diff') if 'TempWater_diff' in df.columns else None
                elif logical == 'Batt_Current': stat_cols_actual.append(bc_col)
                elif logical == 'Batt_Voltage': stat_cols_actual.append(bv_col)
                elif logical == 'POWER': stat_cols_actual.append('POWER') # Derived

            stat_cols_actual = [c for c in stat_cols_actual if c and c in df.columns] # Filter out None and missing

            results_list = []
            for i in range(0, 100, 10):
                sdf = df[(df[soc_col] >= i) & (df[soc_col] < i + 10)]
                row_data = {('Segment', ''): f"{i}-{i+10}%"}
                if not sdf.empty:
                    for col_actual in stat_cols_actual:
                        numeric_series = pd.to_numeric(sdf[col_actual], errors='coerce')
                        # Find the logical name for display in report, if possible
                        display_col_name = col_actual
                        # This reverse lookup is a bit complex here, might simplify report headers later
                        # For now, use actual col name in multi-index

                        row_data[(display_col_name, 'start')] = numeric_series.iloc[0] if not numeric_series.empty else np.nan
                        row_data[(display_col_name, 'end')] = numeric_series.iloc[-1] if not numeric_series.empty else np.nan
                        row_data[(display_col_name, 'max')] = numeric_series.max() if not numeric_series.empty else np.nan
                else:
                    for col_actual in stat_cols_actual:
                        row_data[(col_actual, 'start')] = np.nan
                        row_data[(col_actual, 'end')] = np.nan
                        row_data[(col_actual, 'max')] = np.nan
                results_list.append(row_data)

            stats_df = pd.DataFrame(results_list)
            if not stats_df.empty:
                stats_df = stats_df.set_index(('Segment', ''))
                stats_df.columns = pd.MultiIndex.from_tuples(stats_df.columns)
                # Add trickle times to the DataFrame for export
                stats_df[('SOC_Time', '98-99%(s)')] = t98
                stats_df[('SOC_Time', '99-100%(s)')] = t99

            self.log_message("6. 分段统计数据生成完成。")
            self.update_progress(90)

            # --- Step 7: Export Files ---
            imported_filepath = getattr(self.app_data, 'imported_filepath', None)
            if imported_filepath:
                base = os.path.splitext(os.path.basename(imported_filepath))[0]
                dir_path = os.path.dirname(imported_filepath)
            else:
                base = "report"; dir_path = "."

            stats_filepath = os.path.join(dir_path, f"{base}_stats_report.csv")
            plot_data_filepath = os.path.join(dir_path, f"{base}_plot_data.csv")

            if not stats_df.empty:
                stats_df.T.to_csv(stats_filepath, encoding='utf_8_sig')
                self.log_message(f"统计报告已保存到: {stats_filepath}")
            else:
                self.log_message("警告: 未生成统计报告，因处理后数据为空或分段无数据。", is_error=True)

            df.to_csv(plot_data_filepath, index=False, encoding='utf_8_sig')
            self.log_message(f"详细绘图数据已保存到: {plot_data_filepath}")
            self.update_progress(100)

            if self.winfo_exists():
                messagebox.showinfo("成功", f"报告生成完毕!\n\n统计报告:\n{stats_filepath}\n\n绘图数据:\n{plot_data_filepath}", parent=self)

        except ValueError as e: # Catch specific data/config errors
            self.log_message(f"处理中止: {e}", is_error=True)
            if self.winfo_exists(): messagebox.showerror("处理失败", f"处理因配置或数据问题中止: {e}", parent=self)
        except Exception as e:
            error_details = traceback.format_exc()
            self.log_message(f"发生未知错误: {e}\n{error_details}", is_error=True)
            if self.winfo_exists(): messagebox.showerror("处理失败", f"生成报告时发生未知错误: {e}", parent=self)
        finally:
            self.update_progress(100) # Ensure progress bar completes
            if self.winfo_exists():
                 self.action_button.config(state=tk.NORMAL)
                 self.return_button.config(state=tk.NORMAL)

    def custom_destroy(self):
        if self.processing_thread and self.processing_thread.is_alive():
            if self.winfo_exists() and messagebox.askyesno("确认退出?", "报告生成仍在后台进行中。确定要退出吗？", parent=self):
                # Thread is daemonic, will exit with app. No explicit kill needed/easy.
                super().destroy()
        else:
            super().destroy()

if __name__ == '__main__':
    # --- Mock AppData and Parent for standalone testing ---
    class MockAppData:
        def __init__(self):
            self.imported_data = pd.DataFrame({
                'Time': np.arange(100),
                'SD7_BBAT_SOC_HVS': np.linspace(0, 100, 100),
                'BMS_SOCDis': np.linspace(0, 100, 100), # Alternative SOC
                'CellVoltage_Max': np.random.uniform(3.0, 4.2, 100),
                'CellVoltage_Min': lambda: self.imported_data['CellVoltage_Max'] - np.random.uniform(0, 0.1, 100), # Error here
                'BattT_Max': np.random.uniform(20, 40, 100),
                'BattT_Min': lambda: self.imported_data['BattT_Max'] - np.random.uniform(0, 5, 100), # Error here
                'Batt_Current_req': -np.random.uniform(5, 20, 100),
                'Batt_Current': lambda: self.imported_data['Batt_Current_req'] * np.random.uniform(0.95, 1.0, 100), # Error
                'Batt_Voltage': np.random.uniform(350, 400, 100),
                'Outlet': np.random.uniform(25, 45, 100),
                'Inlet': lambda: self.imported_data['Outlet'] - np.random.uniform(0, 5, 100), # Error
                'CellU_01': np.random.uniform(3.0, 4.2, 100),
                'CellU_02': np.random.uniform(3.0, 4.2, 100),
            })
            # Fix callable issues in mock data
            self.imported_data['CellVoltage_Min'] = self.imported_data['CellVoltage_Max'] - np.random.uniform(0, 0.05, 100)
            self.imported_data['BattT_Min'] = self.imported_data['BattT_Max'] - np.random.uniform(0, 2, 100)
            self.imported_data['Batt_Current'] = self.imported_data['Batt_Current_req'] * np.random.uniform(0.9, 1.0, 100)
            self.imported_data['Inlet'] = self.imported_data['Outlet'] - np.random.uniform(0, 3, 100)


            self.data_mapping_config = {
                 "time_column": "Time", # Assuming 'Time' is the logical name for the time column in mapping UI
                 "mappings": {
                     # CSV_Name : Logical_Name
                     "SD7_BBAT_SOC_HVS": "SOC_HVS", # User maps their CSV col "SD7_BBAT_SOC_HVS" to logical "SOC_HVS"
                     # "BMS_SOCDis": "SOC_Display", # Example if user chose this as display SOC
                     "MyCell_MaxV": "CellVoltage_Max", # Example: CSV has "MyCell_MaxV"
                     "MyCell_MinV": "CellVoltage_Min",
                     "MyBatt_MaxT": "BattT_Max",
                     "MyBatt_MinT": "BattT_Min",
                     # "CellU": "CellVoltagePrefix" # Example if user maps the prefix for cell voltages
                 }
            }
            # Add some columns to imported_data that would match the mapped names if they were CSV names
            self.imported_data['MyCell_MaxV'] = self.imported_data['CellVoltage_Max'] + 0.1 # Simulate a different CSV col name
            self.imported_data.drop(columns=['CellVoltage_Max'], inplace=True) # Remove original if it's not the CSV name

            self.imported_filepath = "mock_report_data.csv"

    class MockParent(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title("Mock Parent for Export Report")
            self.app_data_instance = MockAppData()
            ttk.Button(self, text="Open Export Report", command=self.open_export_win).pack(pady=20)

        def open_export_win(self):
            ExportReportWindow(self, self.app_data_instance)

        def update_idletasks(self): # Essential for UI updates from threads
            super().update_idletasks()

    app = MockParent()
    app.mainloop()
