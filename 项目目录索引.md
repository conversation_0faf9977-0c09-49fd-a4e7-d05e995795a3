# 数据可视化程序项目目录索引

## 项目概述
这是一个基于Python Tkinter的数据可视化分析程序，主要用于处理和分析CSV格式的数据文件，提供多种数据分析和可视化功能。

## 目录结构

### 📁 根目录文件

#### 🚀 主程序文件
- **`main_opt.py`** - 主程序入口文件
  - 应用程序主界面
  - 数据导入和预览功能
  - 表格显示和行删除功能
  - 各功能模块的入口导航

#### 🔧 核心功能模块
- **`data_filtering.py`** - 数据筛选模块
  - 信号列选择和筛选
  - 筛选配置保存/加载
  - 筛选数据导出功能
  - 支持自定义保存位置

- **`charging_analysis.py`** - 充电分析模块
  - 充电数据分析和处理
  - 数据可视化图表生成
  - 分析报告生成和导出
  - 滚动界面支持

- **`data_mapping.py`** - 数据映射模块
  - 逻辑列名与物理列名映射
  - 映射关系配置管理
  - 数据列对应关系处理

- **`sensor_layout.py`** - 温感布置模块
  - 传感器布局可视化
  - 温度数据空间分布
  - 图像处理和显示功能

- **`data_analysis_view.py`** - 数据分析视图模块
  - 数据分析结果展示
  - 多维度数据可视化
  - 分析图表生成

- **`export_report.py`** - 报告导出模块
  - 分析结果报告生成
  - 多格式导出支持
  - 报告模板管理

#### 🧪 测试和工具文件
- **`test_modules.py`** - 模块功能测试
- **`test_syntax.py`** - 语法检查测试
- **`beautifulbattery.py`** - 电池美化显示工具
- **`chargevoltage.py`** - 充电电压分析工具

#### 📋 配置和文档文件
- **`requirements.txt`** - Python依赖包列表
- **`E08-data_mapping_config.json`** - 数据映射配置文件
- **`优化完成说明.md`** - 项目优化完成说明
- **`数据处理显示程序.md`** - 程序功能说明文档

### 📁 子目录

#### 🖼️ images/ - 图像资源目录
存储程序运行过程中生成或使用的图像文件：
- `00bb1a38-5de1-4ca2-89bf-27b9f156be79.jpeg`
- `4fcb52ae-3dcf-4d62-ba60-b6aaaf0e5fd6.jpeg`
- `67ce1203-a7b8-46b2-9d71-47c7ab22ca19.jpeg`
- `72686937-2cb8-4bd5-9ed4-e72279242fca.jpeg`
- `965a7d0d-0451-416b-adea-3a3e17a757fb.jpeg`
- `e68451cb-3598-41f4-a279-cf124381fcbb.jpeg`

#### 🐍 __pycache__/ - Python缓存目录
Python编译后的字节码文件：
- `charging_analysis.cpython-311.pyc`
- `data_analysis_view.cpython-311.pyc`
- `data_filtering.cpython-311.pyc`
- `data_mapping.cpython-311.pyc`
- `export_report.cpython-311.pyc`
- `sensor_layout.cpython-311.pyc`

## 🔄 模块依赖关系

### 主程序流程
```
main_opt.py (主入口)
├── data_filtering.py (数据筛选)
├── charging_analysis.py (充电分析)
├── data_mapping.py (数据映射)
├── sensor_layout.py (温感布置)
├── data_analysis_view.py (数据分析视图)
└── export_report.py (报告导出)
```

### 共享数据结构
- **AppData类** - 全局数据共享对象
  - 存储导入的数据
  - 管理数据状态
  - 模块间数据传递

## 🚀 启动方式
```bash
python main_opt.py
```

## 📦 依赖包
详见 `requirements.txt` 文件，主要包括：
- tkinter (GUI框架)
- pandas (数据处理)
- matplotlib (图表绘制)
- numpy (数值计算)
- PIL/Pillow (图像处理)

## 🔧 最近更新
- ✅ 优化了界面滚动条功能
- ✅ 修复了充电分析界面返回按钮
- ✅ 添加了主界面行删除功能
- ✅ 改进了数据筛选保存位置选择
- ✅ 调整了表格列宽显示
- ✅ 修复了数据筛选默认状态

## 📝 使用说明
1. 运行主程序 `main_opt.py`
2. 导入CSV数据文件
3. 选择相应的分析功能模块
4. 查看分析结果和生成报告
5. 导出处理后的数据或报告

---
*最后更新时间: 2025-06-26*
