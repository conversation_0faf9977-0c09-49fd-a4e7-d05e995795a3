#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据筛选模块 - PyQt6版本
实现数据列筛选和"返回并应用"功能
"""

import json
import os
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QPushButton, 
        QLabel, QCheckBox, QScrollArea, QWidget, QGroupBox, QFileDialog,
        QMessageBox, QProgressBar, QFrame, QSplitter, QTextEdit
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QThread
    from PyQt6.QtGui import QFont
except ImportError:
    print("错误: 未安装PyQt6库")
    import sys
    sys.exit(1)


class DataFilteringDialog(QDialog):
    """数据筛选对话框"""
    
    # 定义信号
    filter_applied = pyqtSignal(object)  # 筛选应用信号，传递筛选后的数据
    
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.app_data = app_data
        self.checkbox_vars = {}
        self.original_columns = []
        
        if self.app_data.imported_data is not None:
            self.original_columns = list(self.app_data.imported_data.columns)
            
        self.init_ui()
        self.create_checkboxes()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("数据筛选 - 选择要保留的信号列")
        self.setGeometry(200, 200, 800, 600)
        self.setModal(True)
        
        # 创建主布局
        main_layout = QVBoxLayout(self)
        
        # 创建顶部信息区域
        self.create_info_area(main_layout)
        
        # 创建控制按钮区域
        self.create_control_buttons(main_layout)
        
        # 创建滚动区域用于显示复选框
        self.create_checkbox_area(main_layout)
        
        # 创建底部操作按钮
        self.create_action_buttons(main_layout)
        
        # self.setup_styles() # Styles are now applied globally
        
    def create_info_area(self, parent_layout):
        """创建信息显示区域"""
        info_group = QGroupBox("筛选信息")
        info_layout = QVBoxLayout(info_group)
        
        # 文件信息
        file_info = f"当前文件: {self.app_data.current_filename}"
        self.file_label = QLabel(file_info)
        info_layout.addWidget(self.file_label)
        
        # 列数信息
        col_count = len(self.original_columns)
        self.col_info_label = QLabel(f"总列数: {col_count}")
        info_layout.addWidget(self.col_info_label)
        
        # 选择状态
        self.selection_label = QLabel("已选择: 0 列")
        self.selection_label.setStyleSheet("color: #4CAF50; font-weight: bold;")
        info_layout.addWidget(self.selection_label)
        
        parent_layout.addWidget(info_group)
        
    def create_control_buttons(self, parent_layout):
        """创建控制按钮区域"""
        control_group = QGroupBox("快速操作")
        control_layout = QHBoxLayout(control_group)
        
        # 全选按钮
        select_all_btn = QPushButton("全选")
        select_all_btn.clicked.connect(self.select_all)
        control_layout.addWidget(select_all_btn)
        
        # 全不选按钮
        deselect_all_btn = QPushButton("全不选")
        deselect_all_btn.clicked.connect(self.deselect_all)
        control_layout.addWidget(deselect_all_btn)
        
        # 反选按钮
        invert_btn = QPushButton("反选")
        invert_btn.clicked.connect(self.invert_selection)
        control_layout.addWidget(invert_btn)
        
        control_layout.addStretch()
        
        # 配置管理按钮
        save_config_btn = QPushButton("保存配置")
        save_config_btn.clicked.connect(self.save_config)
        control_layout.addWidget(save_config_btn)
        
        load_config_btn = QPushButton("加载配置")
        load_config_btn.clicked.connect(self.load_config)
        control_layout.addWidget(load_config_btn)
        
        parent_layout.addWidget(control_group)
        
    def create_checkbox_area(self, parent_layout):
        """创建复选框滚动区域"""
        checkbox_group = QGroupBox("信号列选择")
        checkbox_layout = QVBoxLayout(checkbox_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(300)
        
        # 创建滚动内容窗口部件
        self.scroll_content = QWidget()
        self.scroll_layout = QGridLayout(self.scroll_content)
        
        scroll_area.setWidget(self.scroll_content)
        checkbox_layout.addWidget(scroll_area)
        
        parent_layout.addWidget(checkbox_group)
        
    def create_action_buttons(self, parent_layout):
        """创建底部操作按钮"""
        action_layout = QHBoxLayout()
        
        # 预览按钮
        preview_btn = QPushButton("预览筛选结果")
        preview_btn.clicked.connect(self.preview_filtered_data)
        action_layout.addWidget(preview_btn)
        
        action_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        action_layout.addWidget(cancel_btn)
        
        # 返回并应用按钮（核心功能）
        apply_btn = QPushButton("返回并应用筛选")
        apply_btn.clicked.connect(self.apply_filter_and_return)
        apply_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                font-weight: bold;
                padding: 10px 20px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        action_layout.addWidget(apply_btn)
        
        parent_layout.addLayout(action_layout)
        
    def create_checkboxes(self):
        """创建信号列复选框"""
        if not self.original_columns:
            return
            
        # 清空现有布局
        for i in reversed(range(self.scroll_layout.count())):
            self.scroll_layout.itemAt(i).widget().setParent(None)
            
        self.checkbox_vars.clear()
        
        # 创建复选框，每行显示3个
        row = 0
        col = 0
        for column_name in self.original_columns:
            checkbox = QCheckBox(str(column_name))
            checkbox.stateChanged.connect(self.update_selection_count)
            
            # 设置工具提示
            checkbox.setToolTip(f"列名: {column_name}")
            
            self.checkbox_vars[column_name] = checkbox
            self.scroll_layout.addWidget(checkbox, row, col)
            
            col += 1
            if col >= 3:  # 每行3个复选框
                col = 0
                row += 1
                
        # 更新选择计数
        self.update_selection_count()
        
    def update_selection_count(self):
        """更新选择计数显示"""
        selected_count = sum(1 for cb in self.checkbox_vars.values() if cb.isChecked())
        total_count = len(self.checkbox_vars)
        self.selection_label.setText(f"已选择: {selected_count} / {total_count} 列")
        
    def select_all(self):
        """全选所有复选框"""
        for checkbox in self.checkbox_vars.values():
            checkbox.setChecked(True)
            
    def deselect_all(self):
        """取消选择所有复选框"""
        for checkbox in self.checkbox_vars.values():
            checkbox.setChecked(False)
            
    def invert_selection(self):
        """反选"""
        for checkbox in self.checkbox_vars.values():
            checkbox.setChecked(not checkbox.isChecked())
            
    def get_selected_columns(self):
        """获取选中的列名列表"""
        return [col for col, checkbox in self.checkbox_vars.items() if checkbox.isChecked()]
        
    def preview_filtered_data(self):
        """预览筛选后的数据"""
        selected_columns = self.get_selected_columns()
        
        if not selected_columns:
            QMessageBox.warning(self, "无选择", "请至少选择一个信号列。")
            return
            
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "没有可筛选的数据。")
            return
            
        try:
            # 创建筛选后的数据预览
            filtered_data = self.app_data.imported_data[selected_columns]
            
            # 显示预览对话框
            preview_dialog = FilterPreviewDialog(self, filtered_data, selected_columns)
            preview_dialog.exec()
            
        except Exception as e:
            QMessageBox.critical(self, "预览失败", f"无法预览筛选结果: {str(e)}")
            
    def apply_filter_and_return(self):
        """应用筛选并返回主界面"""
        selected_columns = self.get_selected_columns()
        
        if not selected_columns:
            QMessageBox.warning(self, "无选择", "请至少选择一个信号列。")
            return
            
        if self.app_data.imported_data is None:
            QMessageBox.warning(self, "无数据", "没有可筛选的数据。")
            return
            
        try:
            # 创建筛选后的数据
            filtered_data = self.app_data.imported_data[selected_columns].copy()
            
            # 更新应用数据
            self.app_data.filtered_data = filtered_data
            self.app_data.filter_applied = True
            
            # 发送信号通知主窗口
            self.filter_applied.emit(filtered_data)
            
            # 显示成功消息
            QMessageBox.information(
                self, 
                "筛选成功", 
                f"已成功应用筛选！\n\n"
                f"原始数据: {len(self.app_data.imported_data.columns)} 列\n"
                f"筛选后: {len(selected_columns)} 列\n\n"
                f"筛选后的数据已应用到主界面和所有分析模块。"
            )
            
            # 关闭对话框
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "筛选失败", f"应用筛选时发生错误: {str(e)}")
            
    def save_config(self):
        """保存筛选配置"""
        selected_columns = self.get_selected_columns()
        
        if not selected_columns:
            QMessageBox.warning(self, "无选择", "没有选择任何信号列。")
            return
            
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存筛选配置",
            "filtering_config.json",
            "JSON配置文件 (*.json);;所有文件 (*)"
        )
        
        if not file_path:
            return
            
        try:
            config_data = {
                "selected_columns": selected_columns,
                "total_columns": len(self.original_columns),
                "source_file": self.app_data.current_filename
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
                
            QMessageBox.information(self, "保存成功", f"筛选配置已保存到:\n{file_path}")
            
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"无法保存配置文件: {str(e)}")
            
    def load_config(self):
        """加载筛选配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "加载筛选配置",
            "",
            "JSON配置文件 (*.json);;所有文件 (*)"
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
                
            selected_columns = config_data.get("selected_columns", [])
            
            # 重置所有复选框
            self.deselect_all()
            
            # 设置配置中的选择
            loaded_count = 0
            for col in selected_columns:
                if col in self.checkbox_vars:
                    self.checkbox_vars[col].setChecked(True)
                    loaded_count += 1
                    
            QMessageBox.information(
                self, 
                "加载成功", 
                f"筛选配置已加载！\n\n"
                f"配置中的列数: {len(selected_columns)}\n"
                f"成功加载: {loaded_count} 列"
            )
            
        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"无法加载配置文件: {str(e)}")
            
    def setup_styles(self):
        """设置对话框样式"""
        style = """
        QDialog {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QCheckBox {
            spacing: 5px;
            padding: 2px;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 2px solid #cccccc;
            background-color: white;
            border-radius: 3px;
        }
        QCheckBox::indicator:checked {
            border: 2px solid #4CAF50;
            background-color: #4CAF50;
            border-radius: 3px;
        }
        """
        self.setStyleSheet(style)


class FilterPreviewDialog(QDialog):
    """筛选预览对话框"""
    
    def __init__(self, parent, filtered_data, selected_columns):
        super().__init__(parent)
        self.filtered_data = filtered_data
        self.selected_columns = selected_columns
        self.init_ui()
        
    def init_ui(self):
        """初始化预览界面"""
        self.setWindowTitle("筛选结果预览")
        self.setGeometry(300, 300, 800, 500)
        self.setModal(True)
        
        layout = QVBoxLayout(self)
        
        # 信息标签
        info_text = f"筛选后数据预览 - 共 {len(self.selected_columns)} 列，{len(self.filtered_data)} 行"
        info_label = QLabel(info_text)
        info_label.setStyleSheet("font-weight: bold; color: #2196F3; font-size: 14px;")
        layout.addWidget(info_label)
        
        # 数据预览文本框
        preview_text = QTextEdit()
        preview_text.setReadOnly(True)
        preview_text.setFont(QFont("Consolas", 10))
        
        # 生成预览内容
        preview_content = self.generate_preview_content()
        preview_text.setPlainText(preview_content)
        
        layout.addWidget(preview_text)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.accept)
        layout.addWidget(close_btn)
        
    def generate_preview_content(self):
        """生成预览内容"""
        content = []
        
        # 添加列名
        content.append("选中的列:")
        content.append(", ".join(self.selected_columns))
        content.append("\n" + "="*50 + "\n")
        
        # 添加数据预览（前10行）
        content.append("数据预览（前10行）:")
        content.append(str(self.filtered_data.head(10).to_string()))
        
        if len(self.filtered_data) > 10:
            content.append(f"\n... 还有 {len(self.filtered_data) - 10} 行数据 ...")
            
        return "\n".join(content)
