# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import json
import os

class MappingWindow(tk.Toplevel):
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.title("数据映射配置")
        self.geometry("700x550") # 调整窗口大小以容纳更多内容
        self.transient(parent)
        self.grab_set()

        self.mapping_entries = [] # 用于存储每行映射的 (csv_combo, logic_name_entry)
        self.current_csv_headers = []
        if self.app_data.imported_data is not None:
            self.current_csv_headers = [""] + self.app_data.imported_data.columns.tolist()

        # --- UI 构建 ---
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        # --- 时间列选择 ---
        time_frame = ttk.Frame(main_frame)
        time_frame.pack(fill=tk.X, pady=5)
        ttk.Label(time_frame, text="时间列 (CSV):").pack(side=tk.LEFT, padx=5)
        self.time_column_var = tk.StringVar()
        self.time_column_combo = ttk.Combobox(time_frame, textvariable=self.time_column_var, values=self.current_csv_headers, state="readonly", width=30)
        self.time_column_combo.pack(side=tk.LEFT, padx=5)
        if self.app_data.data_mapping_config and self.app_data.data_mapping_config.get("time_column"):
            self.time_column_var.set(self.app_data.data_mapping_config.get("time_column"))
        elif self.current_csv_headers:
             # 尝试自动匹配常见的实践列名
            common_time_cols = [col for col in self.current_csv_headers if isinstance(col, str) and ('time' in col.lower() or '日期' in col or '时间' in col)]
            if common_time_cols:
                self.time_column_var.set(common_time_cols[0])


        # --- 映射行控制按钮 ---
        mapping_controls_frame = ttk.Frame(main_frame)
        mapping_controls_frame.pack(fill=tk.X, pady=5)
        ttk.Button(mapping_controls_frame, text="增加映射行", command=self.add_mapping_row_ui).pack(side=tk.LEFT, padx=5)
        ttk.Button(mapping_controls_frame, text="删除最后一行", command=self.remove_last_mapping_row_ui).pack(side=tk.LEFT, padx=5)

        # --- 映射区域 (滚动) ---
        canvas_frame = ttk.Frame(main_frame)
        canvas_frame.pack(expand=True, fill=tk.BOTH, pady=5)

        self.canvas = tk.Canvas(canvas_frame)
        self.canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.canvas.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.canvas.configure(yscrollcommand=scrollbar.set)
        self.canvas.bind('<Configure>', lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))

        self.scrollable_frame = ttk.Frame(self.canvas)
        self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        # --- 底部按钮 ---
        bottom_button_frame = ttk.Frame(main_frame)
        bottom_button_frame.pack(fill=tk.X, pady=(10,0))

        ttk.Button(bottom_button_frame, text="导入配置", command=self.load_config_ui).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_button_frame, text="保存配置", command=self.save_config_ui).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_button_frame, text="应用并返回", command=self.apply_and_return).pack(side=tk.RIGHT, padx=5)
        ttk.Button(bottom_button_frame, text="返回主页", command=self.destroy).pack(side=tk.RIGHT, padx=5)

        self.load_initial_mappings()
        self.protocol("WM_DELETE_WINDOW", self.destroy) # Ensure clean close

    def add_mapping_row_ui(self, csv_header_val=None, logic_name_val=""):
        """在UI上添加新的映射关系行"""
        row_frame = ttk.Frame(self.scrollable_frame)
        row_frame.pack(fill=tk.X, pady=2)

        ttk.Label(row_frame, text="CSV列:").pack(side=tk.LEFT, padx=5)
        csv_var = tk.StringVar()
        if csv_header_val and csv_header_val in self.current_csv_headers:
            csv_var.set(csv_header_val)
        csv_combo = ttk.Combobox(row_frame, textvariable=csv_var, values=self.current_csv_headers, state="readonly", width=25)
        csv_combo.pack(side=tk.LEFT, padx=5)

        ttk.Label(row_frame, text="映射为程序逻辑名:").pack(side=tk.LEFT, padx=5)
        logic_name_var = tk.StringVar(value=logic_name_val)
        logic_name_entry = ttk.Entry(row_frame, textvariable=logic_name_var, width=25)
        logic_name_entry.pack(side=tk.LEFT, padx=5)

        self.mapping_entries.append({"frame": row_frame, "csv_var": csv_var, "logic_var": logic_name_var})
        self.canvas.update_idletasks() # Force canvas to update scrollregion
        self.canvas.yview_moveto(1) # Scroll to bottom

    def remove_last_mapping_row_ui(self):
        """从UI上删除最后一行映射关系"""
        if self.mapping_entries:
            last_entry = self.mapping_entries.pop()
            last_entry["frame"].destroy()
            self.canvas.update_idletasks()

    def get_current_mappings_from_ui(self):
        """从UI收集当前的映射配置"""
        mappings = {}
        for entry in self.mapping_entries:
            csv_col = entry["csv_var"].get()
            logic_name = entry["logic_var"].get()
            if csv_col and logic_name: # 只有当两者都非空时才认为是有效映射
                mappings[csv_col] = logic_name
        return mappings

    def save_config_ui(self):
        """保存映射配置到JSON文件"""
        current_mappings = self.get_current_mappings_from_ui()
        time_col = self.time_column_var.get()

        if not current_mappings and not time_col:
            messagebox.showwarning("无配置", "没有可保存的映射或时间列配置。", parent=self)
            return

        filepath = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON 配置文件", "*.json"), ("所有文件", "*.*")],
            title="保存数据映射配置",
            initialfile="data_mapping_config.json",
            parent=self
        )
        if not filepath:
            return

        config_data = {
            "time_column": time_col,
            "mappings": current_mappings
        }

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=4, ensure_ascii=False)
            messagebox.showinfo("成功", f"映射配置已成功保存到:\n{filepath}", parent=self)
        except Exception as e:
            messagebox.showerror("保存失败", f"无法保存配置文件: {e}", parent=self)

    def load_config_ui(self):
        """从JSON文件加载映射配置"""
        filepath = filedialog.askopenfilename(
            filetypes=[("JSON 配置文件", "*.json"), ("所有文件", "*.*")],
            title="加载数据映射配置",
            parent=self
        )
        if not filepath:
            return

        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # 清空现有UI行
            for entry in self.mapping_entries:
                entry["frame"].destroy()
            self.mapping_entries.clear()

            # 加载时间列
            loaded_time_col = config_data.get("time_column")
            if loaded_time_col and loaded_time_col in self.current_csv_headers:
                self.time_column_var.set(loaded_time_col)
            elif loaded_time_col:
                 messagebox.showwarning("时间列警告", f"配置文件中的时间列 '{loaded_time_col}' 在当前CSV中未找到。", parent=self)
                 self.time_column_var.set("") # 清空或设为默认
            else:
                self.time_column_var.set("")


            # 加载映射行
            loaded_mappings = config_data.get("mappings", {})
            for csv_col, logic_name in loaded_mappings.items():
                if csv_col in self.current_csv_headers:
                    self.add_mapping_row_ui(csv_header_val=csv_col, logic_name_val=logic_name)
                else:
                    # 如果CSV列在当前数据中不存在，可以选择添加一个禁用的行或提示用户
                    messagebox.showwarning("列名不匹配", f"配置文件中的CSV列 '{csv_col}' 在当前导入的数据中未找到，该映射将不会被加载。", parent=self)

            messagebox.showinfo("成功", f"映射配置已从:\n{filepath}\n成功加载。", parent=self)
        except Exception as e:
            messagebox.showerror("加载失败", f"无法加载配置文件: {e}", parent=self)
        finally:
            self.canvas.update_idletasks()
            self.canvas.yview_moveto(0) # Scroll to top

    def load_initial_mappings(self):
        """加载程序启动时或数据导入时的默认/已存映射"""
        # 清空现有UI行 (以防万一)
        for entry in self.mapping_entries:
            entry["frame"].destroy()
        self.mapping_entries.clear()

        if self.app_data.data_mapping_config:
            time_col = self.app_data.data_mapping_config.get("time_column")
            if time_col and time_col in self.current_csv_headers:
                self.time_column_var.set(time_col)

            mappings = self.app_data.data_mapping_config.get("mappings", {})
            for csv_col, logic_name in mappings.items():
                 if csv_col in self.current_csv_headers: # 只加载当前CSV中存在的列的映射
                    self.add_mapping_row_ui(csv_header_val=csv_col, logic_name_val=logic_name)

        # 如果没有任何映射，可以添加几行空的作为引导
        if not self.mapping_entries:
            self.add_mapping_row_ui()
            self.add_mapping_row_ui()

        self.canvas.update_idletasks()
        self.canvas.yview_moveto(0)


    def apply_and_return(self):
        """应用当前映射配置到app_data并关闭窗口"""
        current_mappings = self.get_current_mappings_from_ui()
        time_col = self.time_column_var.get()

        self.app_data.data_mapping_config = {
            "time_column": time_col,
            "mappings": current_mappings
        }
        # 可以在主程序中提示用户映射已更新
        if hasattr(self.parent, 'update_info_display'):
            self.parent.update_info_display(f"数据映射配置已更新。\n时间列: {time_col or '未设置'}\n映射数量: {len(current_mappings)}")

        messagebox.showinfo("应用成功", "数据映射配置已应用。", parent=self)
        self.destroy()

if __name__ == '__main__':
    # --- 用于独立测试 MappingWindow ---
    class MockAppData:
        def __init__(self):
            import pandas as pd
            # 创建一个模拟的DataFrame
            self.imported_data = pd.DataFrame({
                'Timestamp': pd.to_datetime(['2023-01-01 10:00:00', '2023-01-01 10:00:01', '2023-01-01 10:00:02']),
                'Sensor1_Temp[C]': [25.1, 25.2, 25.3],
                'Sensor2_Volt': [3.2, 3.21, 3.22],
                'BMS_SOCDis': [80.0, 80.1, 80.2],
                'SD7_BBAT_SOC_HVS': [80.1, 80.2, 80.3],
                'Notes': ['A', 'B', 'C']
            })
            # 清理列名
            self.imported_data.columns = [col.replace('[C]', '').strip() for col in self.imported_data.columns]

            self.data_mapping_config = {
                 "time_column": "Timestamp",
                 "mappings": {
                    "Sensor1_Temp": "Cell_T1",
                    "BMS_SOCDis": "SOC_Display"
                 }
            }
            self.imported_filepath = "dummy_data.csv"

    class MockParent(tk.Tk):
        def __init__(self):
            super().__init__()
            self.title("Mock Parent")
            self.geometry("300x200")
            self.app_data_instance = MockAppData()
            ttk.Button(self, text="Open Mapping", command=self.open_mapping).pack(pady=20)

        def open_mapping(self):
            MappingWindow(self, self.app_data_instance)

        def update_info_display(self, message):
            print(f"MockParent Info: {message}")

    app = MockParent()
    app.mainloop()
