# coding=utf-8
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import numpy as np
import threading
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

# 在文件开头导入模块后添加字体配置
import matplotlib.pyplot as plt
from matplotlib import rcParams

# 设置中文字体和负号显示
try:
    # Windows系统常用字体
    plt.rcParams['font.sans-serif'] = ['SimHei']  
    # Mac系统常用字体
    # plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  
except:
    pass  # 如果找不到字体会使用默认字体

plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

class ChargingAnalyzerPro:
    def __init__(self, root):
        self.root = root
        self.root.title("智能充电分析系统 v4.0")
        self.root.geometry("1000x800")
        
        # 初始化变量
        self.charging_path = tk.StringVar()
        self.input_path = tk.StringVar()
        self.progress = tk.DoubleVar()
        self.df = None
        self.input_df = None
        
        self.create_widgets()
        self.setup_layout()
    
    def create_widgets(self):
        """创建界面组件"""
        # 文件选择区域
        self.file_frame = ttk.LabelFrame(self.root, text="数据文件选择")
        self.charging_entry = ttk.Entry(self.file_frame, textvariable=self.charging_path, width=50)
        self.input_entry = ttk.Entry(self.file_frame, textvariable=self.input_path, width=50)
        
        # 进度与状态
        self.progress_bar = ttk.Progressbar(self.root, variable=self.progress, maximum=100)
        self.status_label = ttk.Label(self.root, text="就绪", anchor="w")
        
        # 操作按钮
        self.btn_frame = ttk.Frame(self.root)
        self.process_btn = ttk.Button(self.btn_frame, text="开始分析", command=self.start_processing)
        self.plot_btn = ttk.Button(self.btn_frame, text="显示图表", command=self.show_analysis_plot)
        self.report_btn = ttk.Button(self.btn_frame, text="生成报告", command=self.generate_report)
        
        # 图表区域
        self.plot_frame = ttk.Frame(self.root)
    
    def setup_layout(self):
        """布局设置"""
        # 文件选择布局
        self.file_frame.pack(pady=10, padx=20, fill="x")
        ttk.Label(self.file_frame, text="充电基准文件:").grid(row=0, column=0, padx=5)
        self.charging_entry.grid(row=0, column=1)
        ttk.Button(self.file_frame, text="浏览...", command=self.select_charging_file).grid(row=0, column=2, padx=5)
        
        ttk.Label(self.file_frame, text="输入参数文件:").grid(row=1, column=0, padx=5)
        self.input_entry.grid(row=1, column=1)
        ttk.Button(self.file_frame, text="浏览...", command=self.select_input_file).grid(row=1, column=2, padx=5)
        
        # 进度条布局
        self.progress_bar.pack(pady=10, fill="x", padx=20)
        
        # 状态栏布局
        self.status_label.pack(fill="x", padx=20, pady=5)
        
        # 按钮布局
        self.btn_frame.pack(pady=10)
        self.process_btn.pack(side="left", padx=5)
        self.plot_btn.pack(side="left", padx=5)
        self.report_btn.pack(side="left", padx=5)
        
        # 图表区域布局
        self.plot_frame.pack(pady=10, fill="both", expand=True, padx=20)

    def select_charging_file(self):
        """选择充电基准文件"""
        path = filedialog.askopenfilename(
            title="选择充电特性文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx")]
        )
        if path:
            self.charging_path.set(path)
            self.update_status(f"已加载充电基准文件: {path}")

    def select_input_file(self):
        """选择输入参数文件"""
        path = filedialog.askopenfilename(
            title="选择输入参数文件",
            filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx")]
        )
        if path:
            self.input_path.set(path)
            self.update_status(f"已加载输入参数文件: {path}")

    def create_marker_column(self):
        """创建温度标记列（恢复区间格式）"""
        a_list = []
        for i in range(len(self.input_df)):
            current_val = self.input_df['MinT'].iloc[i]
            
            if i == 0:
                # 处理首行数据
                prev_base = -np.inf
            else:
                prev_val = self.input_df['MinT'].iloc[i-1]
                prev_base = (prev_val // 5) * 5
                
            current_base = (current_val // 5) * 5
            
            # 标记条件：跨越5的倍数边界且当前值≥基准值
            condition = (current_val >= current_base) and (prev_base < current_base)
            a = 1 if condition else 0
            a_list.append(a)
        
        self.input_df['a'] = a_list
        
        # 恢复温度区间格式（如20-25℃）
        self.input_df['温度区间'] = self.input_df['MinT'].apply(
            lambda x: f"{int((x//5)*5)}-{int((x//5)*5+5)}℃"
        )
        
        # 新增温度基准值列（用于统计和基准线标注）
        self.input_df['温度基准值'] = self.input_df['MinT'].apply(
            lambda x: int((x//5)*5)
        )


    def process_data(self):
        """核心数据处理流程"""
        try:
            # === 数据加载阶段 ===
            self.update_progress(10, "正在加载基准数据...")
            self.df = pd.read_csv(
                self.charging_path.get(),
                dtype={'temp': str},
                usecols=['temp', 'voltage', 'current']
            )
            
            # 数据清洗
            self.df = self.df[
                ~self.df['temp'].str.contains('<-30|>65', na=False) &
                self.df['temp'].notna()
            ]
            self.df['temp'] = pd.to_numeric(
                self.df['temp'].str.replace('[^0-9.-]', '', regex=True),
                errors='coerce'
            )
            self.df = self.df.dropna(subset=['temp'])
            
            # === 输入数据处理 ===
            self.update_progress(30, "正在处理输入数据...")
            self.input_df = pd.read_csv(self.input_path.get()).drop(0)
            
            # 列存在性检查
            required_cols = ['MinT', 'MaxV']
            missing_cols = [col for col in required_cols if col not in self.input_df.columns]
            if missing_cols:
                raise ValueError(f"缺少必要列: {', '.join(missing_cols)}")
            
            # 创建温度标记列（改进后的方法）
            self.create_marker_column()
            
            # === 电流计算阶段 ===
            current_list = []
            voltage_used_list = []
            prev_voltage = 0
            
            for idx, row in self.input_df.iterrows():
                self.update_progress(30 + int(60*(idx/len(self.input_df))), 
                                   f"计算进度: {idx+1}/{len(self.input_df)}...")
                
                temp = row['MinT']
                voltage = row['MaxV']
                
                # 电压更新策略
                if voltage >= prev_voltage or row['a'] == 1:
                    current, voltage_used = self.calculate_current(temp, voltage)
                    prev_voltage = voltage_used
                else:
                    current, voltage_used = self.calculate_current(temp, prev_voltage-0.001)
                
                current_list.append(current)
                voltage_used_list.append(voltage_used)
            
            # === 结果保存 ===
            self.input_df['计算电流'] = current_list
            self.input_df['使用电压'] = voltage_used_list
            
            self.update_progress(95, "正在保存结果...")
            save_path = filedialog.asksaveasfilename(
                title="保存分析结果",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("Excel文件", "*.xlsx")]
            )
            
            if save_path:
                self.input_df.to_csv(save_path, index=False)
                messagebox.showinfo("分析完成", f"结果已保存至:\n{save_path}")
                
        except Exception as e:
            messagebox.showerror("处理错误", f"发生错误:\n{str(e)}")
        finally:
            self.update_progress(0, "就绪")
            self.toggle_buttons(True)

    def calculate_current(self, temp, voltage):
        """改进后的电流计算算法"""
        try:
            temp = float(temp)
            voltage = float(voltage)
            
            # 温度层选择
            temp_filtered = self.df[self.df['temp'] <= temp]
            if len(temp_filtered) < 23:
                temp_filtered = self.df.nsmallest(23, 'temp')
            
            temp_sorted = temp_filtered.iloc[(temp_filtered['temp'] - temp).abs().argsort()[:23]]
            common_temp = temp_sorted['temp'].mode()[0]
            temp_df = temp_sorted[temp_sorted['temp'] == common_temp].sort_values('voltage')
            
            # 电压筛选
            try:
                voltage_used = temp_df[temp_df['voltage'] > voltage]['voltage'].iloc[0]
            except IndexError:
                voltage_used = temp_df['voltage'].max()
            
            # 温度插值
            temp_below = self.df[self.df['temp'] <= temp].nlargest(23, 'temp')
            temp_above = self.df[self.df['temp'] > temp].nsmallest(23, 'temp')
            
            if temp_below.empty or temp_above.empty:
                nearest = self.df.iloc[(self.df['temp'] - temp).abs().argsort()[0]]
                return nearest['current'], nearest['voltage']
            
            # 安全获取插值点
            df_below = temp_below[temp_below['voltage'] >= voltage_used].iloc[0] if not temp_below.empty else temp_below
            df_above = temp_above[temp_above['voltage'] >= voltage_used].iloc[0] if not temp_above.empty else temp_above
            
            current = np.interp(
                temp,
                [df_below['temp'], df_above['temp']],
                [df_below['current'], df_above['current']]
            )
            
            return current, voltage_used
            
        except Exception as e:
            raise RuntimeError(f"电流计算失败: {str(e)}")

    def show_analysis_plot(self):
        """显示分析图表（恢复区间格式）"""
        if self.input_df is None or '计算电流' not in self.input_df.columns:
            messagebox.showwarning("警告", "请先完成数据分析！")
            return
        
        # 清除旧图表
        for widget in self.plot_frame.winfo_children():
            widget.destroy()
        
        # 创建图表
        fig = plt.Figure(figsize=(10, 6), dpi=100)
        ax = fig.add_subplot(111)
        
        # 绘制主曲线
        x = self.input_df.index
        ax.plot(x, self.input_df['计算电流'], 'b-', label='计算电流', linewidth=2)
        if 'curr' in self.input_df.columns:
            ax.plot(x, self.input_df['curr'], 'r--', label='实际电流', alpha=0.7)
        
        # 添加温度基准线（使用温度基准值列）
        unique_bases = self.input_df['温度基准值'].unique()
        for base in unique_bases:
            ax.axhline(y=base, color='gray', linestyle=':', alpha=0.3)
        
        # 标记关键点
        markers = self.input_df[self.input_df['a'] == 1]
        ax.scatter(markers.index, markers['计算电流'], 
                  c='green', s=50, zorder=5, 
                  label='温度阈值点')
        
        # 添加统计信息
        stats_text = self.generate_statistics()
        ax.text(0.02, 0.98, stats_text, 
               transform=ax.transAxes,
               verticalalignment='top',
               bbox=dict(facecolor='white', alpha=0.8))
        
        # 图表装饰
        ax.set_title("电流分析报告（带温度基准线）", fontsize=14)
        ax.set_xlabel("数据序号", fontsize=10)
        ax.set_ylabel("电流值 (A)", fontsize=10)
        ax.legend(loc='upper left')
        ax.grid(True, alpha=0.3)
        
        # 嵌入图表
        canvas = FigureCanvasTkAgg(fig, self.plot_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill="both", expand=True)


    def generate_report(self):
        """生成分析报告"""
        # 实现报告生成逻辑（可根据需要扩展）
        messagebox.showinfo("功能提示", "报告生成功能正在开发中")

    def start_processing(self):
        """启动处理线程"""
        if not all([self.charging_path.get(), self.input_path.get()]):
            messagebox.showerror("错误", "请先选择充电基准文件和输入参数文件！")
            return
        
        self.toggle_buttons(False)
        threading.Thread(target=self.process_data, daemon=True).start()


    def generate_statistics(self):
        """生成温度区间统计信息（使用区间格式）"""
        stats = self.input_df.groupby('温度区间')['计算电流'].agg([
            ('平均电流', 'mean'),
            ('电流标准差', 'std'),
            ('数据点数', 'count')
        ]).reset_index()
        
        stats_text = "温度区间统计:\n"
        for _, row in stats.iterrows():
            stats_text += (
                f"{row['温度区间']}: "
                f"均值={row['平均电流']:.2f}A ± {row['电流标准差']:.2f}A "
                f"({row['数据点数']}点)\n"
            )
        return stats_text

    def toggle_buttons(self, state):
        """切换按钮状态"""
        state = "normal" if state else "disabled"
        for btn in [self.process_btn, self.plot_btn, self.report_btn]:
            btn.config(state=state)

    def update_progress(self, value, message=None):
        """更新进度和状态"""
        self.progress.set(value)
        if message:
            self.status_label.config(text=message)
        self.root.update_idletasks()

    def update_status(self, message):
        """更新状态栏"""
        self.status_label.config(text=message)

if __name__ == "__main__":
    root = tk.Tk()
    app = ChargingAnalyzerPro(root)
    root.mainloop()