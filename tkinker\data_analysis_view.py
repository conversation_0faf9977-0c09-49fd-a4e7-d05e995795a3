# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from itertools import cycle
import numpy as np # <-- 确保 numpy 被导入，用于测试部分

# 尝试配置Matplotlib以支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题
except Exception as e:
    print(f"无法设置Matplotlib中文字体: {e}")

class AutocompleteEntry(ttk.Entry):
    """带自动补全的输入框"""
    def __init__(self, master, app_data, **kwargs):
        super().__init__(master, **kwargs)
        self.app_data = app_data
        self.headers = [] # 将在 refresh_headers 中更新

        self.var = tk.StringVar()
        self.configure(textvariable=self.var)

        self.listbox = tk.Listbox(master, width=self["width"])

        self.var.trace_add('write', self.on_text_changed)
        self.bind("<Up>", self.on_arrow_up)
        self.bind("<Down>", self.on_arrow_down)
        self.bind("<Return>", self.on_enter_pressed)
        self.bind("<Escape>", lambda _: self.hide_listbox())
        self.listbox.bind("<Button-1>", self.on_listbox_click)
        self.listbox.bind("<Double-Button-1>", self.on_listbox_select)

    def refresh_headers(self):
        """从 app_data 更新列名列表"""
        if self.app_data and self.app_data.imported_data is not None:
            self.headers = list(self.app_data.imported_data.columns)
        else:
            self.headers = []
        self.update_listbox_content()

    def on_text_changed(self, name, index, mode):
        text = self.var.get()
        if text:
            self.update_listbox_content()
            if self.listbox.size() > 0:
                self.show_listbox()
            else:
                self.hide_listbox()
        else:
            self.hide_listbox()
            self.update_listbox_content() # 确保在文本为空时也更新（例如显示所有header）

    def update_listbox_content(self):
        self.listbox.delete(0, tk.END)
        text = self.var.get().lower()
        if not self.headers: # 如果没有列名，则不显示任何内容
            return

        if text:
            for header in self.headers:
                if text in header.lower():
                    self.listbox.insert(tk.END, header)
        else: # 如果输入框为空，显示所有列名
            for header in self.headers:
                self.listbox.insert(tk.END, header)

        if self.listbox.size() > 0:
             self.listbox.select_clear(0, tk.END)
             self.listbox.select_set(0) # 默认选中第一个
             self.listbox.activate(0)


    def show_listbox(self):
        if not self.listbox.winfo_viewable():
            x = self.winfo_x()
            y = self.winfo_y() + self.winfo_height()
            self.listbox.place(x=x, y=y, width=self.winfo_width())
            self.listbox.lift()

    def hide_listbox(self):
        self.listbox.place_forget()

    def on_arrow_up(self, event):
        if self.listbox.winfo_viewable():
            current_selection = self.listbox.curselection()
            if not current_selection:
                self.listbox.select_set(tk.END)
            else:
                index = current_selection[0]
                if index > 0:
                    self.listbox.select_clear(index)
                    self.listbox.select_set(index - 1)
                    self.listbox.activate(index - 1)
            return "break" #阻止事件传播

    def on_arrow_down(self, event):
        if self.listbox.winfo_viewable():
            current_selection = self.listbox.curselection()
            if not current_selection:
                self.listbox.select_set(0)
            else:
                index = current_selection[0]
                if index < self.listbox.size() - 1:
                    self.listbox.select_clear(index)
                    self.listbox.select_set(index + 1)
                    self.listbox.activate(index + 1)
            return "break"

    def on_enter_pressed(self, event):
        if self.listbox.winfo_viewable() and self.listbox.curselection():
            self.on_listbox_select(event)
        else: # 如果列表框不可见或没有选择，则隐藏列表框
            self.hide_listbox()
        return "break"


    def on_listbox_click(self, event):
        # 这个函数确保单击时也会选择，主要用于鼠标操作
        # on_listbox_select 将处理双击和回车
        pass

    def on_listbox_select(self, event):
        if self.listbox.winfo_viewable() and self.listbox.curselection():
            selected_value = self.listbox.get(self.listbox.curselection())
            self.var.set(selected_value)
            self.hide_listbox()
            self.focus_set() # 将焦点移回输入框
            self.icursor(tk.END) # 将光标移到文本末尾
        return "break"

class DataAnalysisView(tk.Toplevel):
    def __init__(self, master, app_data):
        super().__init__(master)
        self.master = master
        self.app_data = app_data
        self.title("数据分析与可视化")
        self.geometry("1200x800")
        self.protocol("WM_DELETE_WINDOW", self.on_close)

        self.headers = []
        self.x_axis_data_col = tk.StringVar()
        self.y_axis_data_cols = []  # 存储格式: {'name': col_name, 'side': 'left'/'right'}
        self.marked_points_x = [] # 存储标记点的x坐标

        self.fig, self.ax = None, None # Matplotlib figure and axes
        self.ax2 = None # For second y-axis
        self.canvas_widget = None
        # Store the base list of colors
        self.base_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        self.color_cycle = cycle(self.base_colors) # Initial cycle for the first plot if any


        self.create_widgets()
        self.load_data_for_analysis()

        self.transient(master) # 设置为master的瞬态窗口
        self.grab_set() # 捕获所有事件

    def load_data_for_analysis(self):
        if self.app_data.imported_data is not None:
            self.headers = list(self.app_data.imported_data.columns)
            self.x_axis_entry.refresh_headers()
            self.y_axis_entry.refresh_headers()
        else:
            messagebox.showwarning("无数据", "没有导入数据可供分析。", parent=self)
            self.destroy()

    def create_widgets(self):
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        # --- 左侧控制面板 ---
        left_panel = ttk.Frame(main_frame, width=350)
        left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_panel.pack_propagate(False) # 防止自动调整大小

        # --- 输入区 ---
        input_frame = ttk.LabelFrame(left_panel, text="1. 选择数据列", padding="10")
        input_frame.pack(fill=tk.X, pady=5)

        ttk.Label(input_frame, text="X轴数据:").grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)
        self.x_axis_entry = AutocompleteEntry(input_frame, self.app_data, width=25)
        self.x_axis_entry.grid(row=0, column=1, padx=5, pady=5, sticky=tk.EW)

        ttk.Label(input_frame, text="Y轴数据:").grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)
        self.y_axis_entry = AutocompleteEntry(input_frame, self.app_data, width=25)
        self.y_axis_entry.grid(row=1, column=1, padx=5, pady=5, sticky=tk.EW)

        self.add_y_btn = ttk.Button(input_frame, text="添加Y轴", command=self.add_y_axis, width=10)
        self.add_y_btn.grid(row=1, column=2, padx=5, pady=5)

        input_frame.columnconfigure(1, weight=1)

        # --- Y轴管理区 ---
        y_manage_frame = ttk.LabelFrame(left_panel, text="2. 管理Y轴", padding="10")
        y_manage_frame.pack(fill=tk.X, pady=5)

        self.y_axis_listbox = tk.Listbox(y_manage_frame, height=5, exportselection=False)
        self.y_axis_listbox.pack(fill=tk.X, expand=True, pady=(0,5))

        y_btn_frame = ttk.Frame(y_manage_frame)
        y_btn_frame.pack(fill=tk.X)
        self.toggle_y_side_btn = ttk.Button(y_btn_frame, text="切换左右轴", command=self.toggle_y_axis_side, width=12)
        self.toggle_y_side_btn.pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)
        self.remove_y_btn = ttk.Button(y_btn_frame, text="移除选中Y轴", command=self.remove_y_axis, width=12)
        self.remove_y_btn.pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)

        # --- 图表控制区 ---
        plot_control_frame = ttk.LabelFrame(left_panel, text="3. 图表设置与操作", padding="10")
        plot_control_frame.pack(fill=tk.X, pady=5)

        ttk.Label(plot_control_frame, text="图表标题:").grid(row=0, column=0, padx=5, pady=3, sticky=tk.W)
        self.plot_title_entry = ttk.Entry(plot_control_frame, width=30)
        self.plot_title_entry.grid(row=0, column=1, padx=5, pady=3, sticky=tk.EW)
        self.plot_title_entry.insert(0, "数据分析图表")

        ttk.Label(plot_control_frame, text="X轴标签:").grid(row=1, column=0, padx=5, pady=3, sticky=tk.W)
        self.x_label_entry = ttk.Entry(plot_control_frame, width=30)
        self.x_label_entry.grid(row=1, column=1, padx=5, pady=3, sticky=tk.EW)

        ttk.Label(plot_control_frame, text="Y1轴(左)标签:").grid(row=2, column=0, padx=5, pady=3, sticky=tk.W)
        self.y1_label_entry = ttk.Entry(plot_control_frame, width=30)
        self.y1_label_entry.grid(row=2, column=1, padx=5, pady=3, sticky=tk.EW)

        ttk.Label(plot_control_frame, text="Y2轴(右)标签:").grid(row=3, column=0, padx=5, pady=3, sticky=tk.W)
        self.y2_label_entry = ttk.Entry(plot_control_frame, width=30)
        self.y2_label_entry.grid(row=3, column=1, padx=5, pady=3, sticky=tk.EW)

        plot_control_frame.columnconfigure(1, weight=1)

        self.draw_plot_btn = ttk.Button(plot_control_frame, text="绘制/更新图表", command=self.plot_data)
        self.draw_plot_btn.grid(row=4, column=0, columnspan=2, pady=10, sticky=tk.EW)

        # --- 标记点管理区 ---
        marker_frame = ttk.LabelFrame(left_panel, text="4. 标记点管理与导出", padding="10")
        marker_frame.pack(fill=tk.X, pady=5, expand=True) # expand to fill remaining space

        self.marked_points_listbox = tk.Listbox(marker_frame, height=6, exportselection=False)
        self.marked_points_listbox.pack(fill=tk.BOTH, expand=True, pady=(0,5))

        marker_btn_frame = ttk.Frame(marker_frame)
        marker_btn_frame.pack(fill=tk.X)
        self.remove_marker_btn = ttk.Button(marker_btn_frame, text="删除标记点", command=self.remove_marked_point, width=12)
        self.remove_marker_btn.pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)
        self.export_data_btn = ttk.Button(marker_btn_frame, text="导出标记数据", command=self.export_analyzed_data, width=12)
        self.export_data_btn.pack(side=tk.LEFT, padx=2, expand=True, fill=tk.X)

        # --- 右侧图表显示区 ---
        right_panel = ttk.Frame(main_frame)
        right_panel.pack(side=tk.LEFT, expand=True, fill=tk.BOTH)

        self.fig, self.ax = plt.subplots(figsize=(8,6), dpi=100)
        self.canvas_widget = FigureCanvasTkAgg(self.fig, master=right_panel)
        self.canvas_widget_tk = self.canvas_widget.get_tk_widget()
        self.canvas_widget_tk.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
        self.canvas_widget.mpl_connect('button_press_event', self.on_plot_click)

        # 初始绘制一个空图表
        self.ax.set_xlabel("X轴")
        self.ax.set_ylabel("Y轴 (左)")
        self.ax.set_title("请选择数据并绘制图表")
        self.ax.grid(True)
        self.fig.tight_layout()
        self.canvas_widget.draw()

    def add_y_axis(self):
        """添加Y轴数据列"""
        col_name = self.y_axis_entry.var.get()
        if not col_name:
            messagebox.showwarning("选择Y轴", "请输入或选择一个Y轴数据列。", parent=self)
            return
        if col_name not in self.headers:
            messagebox.showerror("错误", f"列名 '{col_name}' 不存在于数据中。", parent=self)
            return
        if any(y_col['name'] == col_name for y_col in self.y_axis_data_cols):
            messagebox.showinfo("重复添加", f"列 '{col_name}' 已经作为Y轴存在。", parent=self)
            return

        self.y_axis_data_cols.append({'name': col_name, 'side': 'left'}) # 默认添加到左轴
        self.update_y_axis_listbox()
        self.y_axis_entry.var.set("") # 清空输入框

    def update_y_axis_listbox(self):
        """更新Y轴列表框的显示"""
        self.y_axis_listbox.delete(0, tk.END)
        for y_col in self.y_axis_data_cols:
            display_name = f"{y_col['name']} ({'左轴' if y_col['side'] == 'left' else '右轴'})"
            self.y_axis_listbox.insert(tk.END, display_name)

    def toggle_y_axis_side(self):
        """切换选中Y轴的左右位置"""
        selected_indices = self.y_axis_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("选择Y轴", "请在列表中选择一个Y轴进行切换。", parent=self)
            return

        idx = selected_indices[0]
        current_side = self.y_axis_data_cols[idx]['side']
        self.y_axis_data_cols[idx]['side'] = 'right' if current_side == 'left' else 'left'
        self.update_y_axis_listbox()
        self.y_axis_listbox.select_set(idx) # 保持选中状态

    def remove_y_axis(self):
        """删除选中的Y轴数据列"""
        selected_indices = self.y_axis_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("选择Y轴", "请在列表中选择一个Y轴进行移除。", parent=self)
            return

        idx = selected_indices[0]
        del self.y_axis_data_cols[idx]
        self.update_y_axis_listbox()

    def plot_data(self):
        """绘制双Y轴图表"""
        x_col_name = self.x_axis_entry.var.get()
        if not x_col_name:
            messagebox.showwarning("选择X轴", "请选择X轴数据列。", parent=self)
            return
        if x_col_name not in self.headers:
             messagebox.showerror("错误", f"X轴列名 '{x_col_name}' 不存在。", parent=self)
             return

        if not self.y_axis_data_cols:
            messagebox.showwarning("选择Y轴", "请至少添加一个Y轴数据列。", parent=self)
            return

        df = self.app_data.imported_data
        if df is None:
            messagebox.showerror("无数据", "没有导入数据。", parent=self)
            return

        try:
            # 转换X轴数据为数值类型，非数值转为NaN并移除
            x_values_raw = pd.to_numeric(df[x_col_name], errors='coerce')
            valid_indices = x_values_raw.notna()
            x_values = x_values_raw[valid_indices]

            if x_values.empty:
                messagebox.showerror("X轴数据错误", f"X轴列 '{x_col_name}' 不包含有效的数值数据或转换后为空。", parent=self)
                return

            self.ax.clear()
            if self.ax2:
                self.ax2.clear()
                self.ax2.set_frame_on(False) # 隐藏旧的ax2框架
                self.ax2 = None # 重置ax2

            has_left_axis = any(y_col['side'] == 'left' for y_col in self.y_axis_data_cols)
            has_right_axis = any(y_col['side'] == 'right' for y_col in self.y_axis_data_cols)

            current_color_cycle = cycle(self.base_colors) # 使用基础颜色列表重置颜色循环器

            # 绘制左轴数据
            for y_col_info in self.y_axis_data_cols:
                if y_col_info['side'] == 'left':
                    y_col_name = y_col_info['name']
                    y_values_raw = pd.to_numeric(df[y_col_name], errors='coerce')
                    y_values = y_values_raw[valid_indices]
                    if not y_values.empty:
                         self.ax.plot(x_values, y_values, label=y_col_name, color=next(current_color_cycle))

            self.ax.set_xlabel(self.x_label_entry.get() if self.x_label_entry.get() else x_col_name)
            self.ax.set_ylabel(self.y1_label_entry.get() if self.y1_label_entry.get() else "Y轴 (左)")
            self.ax.grid(True)

            # 绘制右轴数据
            if has_right_axis:
                self.ax2 = self.ax.twinx() # 创建新的ax2
                for y_col_info in self.y_axis_data_cols:
                    if y_col_info['side'] == 'right':
                        y_col_name = y_col_info['name']
                        y_values_raw = pd.to_numeric(df[y_col_name], errors='coerce')
                        y_values = y_values_raw[valid_indices]
                        if not y_values.empty:
                            self.ax2.plot(x_values, y_values, label=f"{y_col_name} (右)", color=next(current_color_cycle), linestyle='--')
                self.ax2.set_ylabel(self.y2_label_entry.get() if self.y2_label_entry.get() else "Y轴 (右)")
                self.ax2.spines["right"].set_position(("outward", 0)) # 确保右轴可见

            # 合并图例
            lines, labels = self.ax.get_legend_handles_labels()
            if self.ax2:
                lines2, labels2 = self.ax2.get_legend_handles_labels()
                lines.extend(lines2)
                labels.extend(labels2)

            if lines: # 只有在有可绘制的线条时才显示图例
                self.ax.legend(lines, labels, loc='best')

            self.ax.set_title(self.plot_title_entry.get())

            # 重新绘制标记线
            self.redraw_marked_lines()

            self.fig.tight_layout() # 调整布局以防止标签重叠
            self.canvas_widget.draw()

        except Exception as e:
            messagebox.showerror("绘图错误", f"绘制图表时发生错误: {e}", parent=self)

    def on_plot_click(self, event):
        """处理图表点击事件，添加标记点"""
        if event.inaxes == self.ax or event.inaxes == self.ax2: # 确保点击在绘图区域内
            if event.xdata is not None:
                x_clicked = float(event.xdata)
                self.marked_points_x.append(x_clicked)
                self.marked_points_x.sort() # 保持排序
                self.update_marked_points_listbox()
                self.draw_marked_line(x_clicked)
                self.canvas_widget.draw()

    def draw_marked_line(self, x_coord, color='red', linestyle='--', alpha=0.7):
        """在图表上绘制单条垂直标记线"""
        if self.ax:
            self.ax.axvline(x=x_coord, color=color, linestyle=linestyle, alpha=alpha)

    def redraw_marked_lines(self):
        """重新绘制所有已存储的标记线 (通常在重绘图表后调用)"""
        if not self.ax: return
        for x_coord in self.marked_points_x:
            self.draw_marked_line(x_coord)

    def update_marked_points_listbox(self):
        self.marked_points_listbox.delete(0, tk.END)
        for x_val in self.marked_points_x:
            self.marked_points_listbox.insert(tk.END, f"{x_val:.4f}")

    def remove_marked_point(self):
        selected_indices = self.marked_points_listbox.curselection()
        if not selected_indices:
            messagebox.showwarning("选择标记点", "请在列表中选择一个标记点进行删除。", parent=self)
            return

        idx = selected_indices[0]
        del self.marked_points_x[idx] # 从数据中删除
        self.update_marked_points_listbox() # 更新列表显示

        if self.x_axis_entry.var.get() and self.y_axis_data_cols:
             self.plot_data()
        else:
            self.ax.clear()
            if self.ax2: self.ax2.clear(); self.ax2=None
            self.ax.set_xlabel(self.x_label_entry.get() if self.x_label_entry.get() else "X轴")
            self.ax.set_ylabel(self.y1_label_entry.get() if self.y1_label_entry.get() else "Y轴 (左)")
            self.ax.set_title(self.plot_title_entry.get())
            self.ax.grid(True)
            self.redraw_marked_lines()
            self.fig.tight_layout()
            self.canvas_widget.draw()


    def export_analyzed_data(self):
        """导出标记点相关的数据"""
        if not self.marked_points_x:
            messagebox.showinfo("无标记点", "没有标记点可供导出。", parent=self)
            return

        if self.app_data.imported_data is None or self.x_axis_entry.var.get() == "":
            messagebox.showerror("数据错误", "无法导出，原始数据或X轴未设置。", parent=self)
            return

        filepath = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV 文件", "*.csv"), ("所有文件", "*.*")],
            title="保存分析数据为CSV",
            parent=self
        )
        if not filepath:
            return

        df = self.app_data.imported_data.copy()
        x_axis_col = self.x_axis_entry.var.get()

        try:
            df[x_axis_col] = pd.to_numeric(df[x_axis_col], errors='coerce')
            df.dropna(subset=[x_axis_col], inplace=True)
        except Exception as e:
            messagebox.showerror("数据转换错误", f"X轴列 '{x_axis_col}' 转换数值失败: {e}", parent=self)
            return

        result_rows = []

        # 1. 查找最接近标记点的数据行
        for x_mark in self.marked_points_x:
            df['diff_to_mark'] = (df[x_axis_col] - x_mark).abs()
            closest_idx = df['diff_to_mark'].idxmin()
            closest_row = df.loc[closest_idx].copy()
            closest_row['备注'] = f"最接近标记点 {x_mark:.4f} 的原始数据"
            result_rows.append(closest_row.drop('diff_to_mark'))
        
        # 添加插值数据的部分（这里简化，可以根据需要扩展）
        # 此处可以添加一个分隔符
        # result_rows.append(pd.Series({'备注': '--- 标记点插值数据 ---'}))
        # ... 插值计算逻辑 ...

        result_df = pd.DataFrame(result_rows)

        # 2. 计算标记点区间的统计数据
        if len(self.marked_points_x) > 1:
            stats_summary_list = []
            sorted_marks = sorted(list(set(self.marked_points_x)))

            for i in range(len(sorted_marks) - 1):
                lower_bound = sorted_marks[i]
                upper_bound = sorted_marks[i+1]

                interval_data = df[
                    (df[x_axis_col] >= lower_bound) & (df[x_axis_col] <= upper_bound)
                ]

                if not interval_data.empty:
                    y_cols_to_analyze = [yc['name'] for yc in self.y_axis_data_cols]
                    numeric_y_cols = []
                    for col in y_cols_to_analyze:
                        try:
                            pd.to_numeric(interval_data[col], errors='raise')
                            numeric_y_cols.append(col)
                        except:
                            continue

                    # --- START: CORRECTED BLOCK ---
                    if numeric_y_cols:
                        # 正确地计算统计量。 .T 会将结果转置，使列名（如'temp1'）成为索引，
                        # 而统计函数名（'mean', 'min'等）成为列名。
                        stats = interval_data[numeric_y_cols].agg(['mean', 'min', 'max', 'std', 'median']).T

                        # 创建一个字典来保存当前区间的统计摘要
                        interval_stat_summary = {'区间': f"{lower_bound:.4f} 至 {upper_bound:.4f}"}
                        
                        # 遍历每个需要分析的Y轴列
                        for y_col_name in numeric_y_cols:
                            # 遍历每种统计量
                            for stat_name in ['mean', 'min', 'max', 'std', 'median']:
                                # 创建描述性的键，例如 'temp1_mean', 'temp1_min'
                                key = f'{y_col_name}_{stat_name}'
                                
                                # 从stats DataFrame中通过行(y_col_name)和列(stat_name)获取正确的统计值
                                value = stats.loc[y_col_name, stat_name]
                                
                                # 将键值对存入字典
                                interval_stat_summary[key] = value
                        
                        # 将这个区间的完整统计摘要添加到列表中
                        stats_summary_list.append(interval_stat_summary)
                    # --- END: CORRECTED BLOCK ---

            if stats_summary_list:
                stats_df = pd.DataFrame(stats_summary_list)
                
                empty_row_data = {col: '' for col in result_df.columns}
                empty_row_data['备注'] = "--- 区间统计 ---"
                result_df = pd.concat([result_df, pd.DataFrame([empty_row_data])], ignore_index=True)
                result_df = pd.concat([result_df, stats_df], ignore_index=True)

        try:
            result_df.to_csv(filepath, index=False, encoding='utf-8-sig')
            messagebox.showinfo("导出成功", f"分析数据已保存到:\n{filepath}", parent=self)
        except Exception as e:
            messagebox.showerror("导出失败", f"保存文件时发生错误: {e}", parent=self)


    def on_close(self):
        if self.fig:
            plt.close(self.fig)
        self.destroy()

if __name__ == '__main__':
    root = tk.Tk()
    root.title("主应用 (测试)")
    root.geometry("300x200")

    class MockAppData:
        def __init__(self):
            self.imported_data = None
            self.imported_filepath = None
            data = {
                'Time (s)': np.linspace(0, 10, 100),
                'Sensor A': np.sin(np.linspace(0, 10, 100)) + np.random.rand(100) * 0.2,
                'Sensor B': np.cos(np.linspace(0, 10, 100)) + np.random.rand(100) * 0.3,
                'Temperature': np.linspace(20, 25, 100) + np.random.rand(100) * 0.5,
                'Voltage': 3.3 + np.sin(np.linspace(0,5,100)) * 0.1
            }
            self.imported_data = pd.DataFrame(data)
            self.imported_filepath = "mock_data.csv"

    mock_app_data = MockAppData()

    def open_analysis_window():
        DataAnalysisView(root, mock_app_data)

    ttk.Button(root, text="打开数据分析窗口", command=open_analysis_window).pack(pady=20)
    
    root.mainloop()