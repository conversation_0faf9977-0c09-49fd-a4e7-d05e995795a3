# 数据可视化程序 - 快速参考指南

## 🚀 快速启动

### 环境要求
- Python 3.8+
- PyQt6 6.4.0+

### 一键启动
```bash
# 安装依赖
pip install -r requirements_pyqt.txt

# 启动程序
python main_pyqt.py
```

## 📁 核心文件速查

| 文件名 | 功能 | 关键类 |
|--------|------|--------|
| `main_pyqt.py` | 主程序入口 | `MainWindow`, `AppData` |
| `charging_analysis_pyqt.py` | 充电分析 | `ChargingAnalysisDialog` |
| `data_mapping_pyqt.py` | 数据映射 | `MappingWindow` |
| `data_filtering_pyqt.py` | 数据筛选 | `DataFilteringDialog` |
| `sensor_layout_pyqt.py` | 温感布置 | `SensorLayoutDialog` |
| `data_analysis_view_pyqt.py` | 数据分析视图 | `DataAnalysisViewDialog` |
| `export_report_pyqt.py` | 报告导出 | `ExportReportDialog` |

## 🔧 常用功能速查

### 数据导入
```python
# 在main_pyqt.py中
def import_csv_data(self):
    # 文件选择 → 编码检测 → 数据加载 → 预览显示
```

### 数据映射
```python
# 在data_mapping_pyqt.py中
def apply_mapping(self):
    # 列名映射 → 自定义变量 → 配置保存
```

### 充电分析
```python
# 在charging_analysis_pyqt.py中
class ChargingAnalysisWorker:
    # 四组合查询 → 电流计算 → 结果生成
```

### 数据筛选
```python
# 在data_filtering_pyqt.py中
def apply_filter(self):
    # 列选择 → 数据筛选 → 全局应用
```

## 📊 数据流程

```
CSV导入 → 数据映射 → 数据筛选 → 分析处理 → 结果导出
   ↓         ↓         ↓         ↓         ↓
main_pyqt  mapping   filtering  analysis  export
```

## 🎯 关键配置

### AppData共享数据
```python
class AppData:
    imported_data = None          # 原始数据
    filtered_data = None          # 筛选数据
    data_mapping_config = {}      # 映射配置
    charging_analysis_results = None  # 分析结果
```

### 数据映射配置格式
```json
{
  "time_column": "Time",
  "mappings": {
    "CSV列名": "逻辑名称"
  },
  "custom_variables": [
    {
      "name": "变量名",
      "formula": "计算公式"
    }
  ]
}
```

## 🔍 常见问题解决

### 1. 导入数据失败
**问题**: CSV文件编码问题
**解决**: 程序自动尝试多种编码（utf-8, gbk, gb2312）

### 2. 充电分析无结果
**问题**: 缺少必要的数据列
**解决**: 检查数据映射配置，确保包含所需信号

### 3. 图表显示异常
**问题**: Matplotlib中文字体问题
**解决**: 程序自动配置SimHei字体

### 4. 模块间数据不同步
**问题**: 数据状态不一致
**解决**: 使用AppData类统一管理数据状态

## 🛠️ 开发扩展

### 新增模块模板
```python
from PyQt6.QtWidgets import QDialog, QVBoxLayout
from PyQt6.QtCore import pyqtSignal

class NewModuleDialog(QDialog):
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.app_data = app_data
        self.setup_ui()
    
    def setup_ui(self):
        layout = QVBoxLayout(self)
        # 添加UI组件
```

### 工作线程模板
```python
from PyQt6.QtCore import QThread, pyqtSignal

class WorkerThread(QThread):
    progress_updated = pyqtSignal(int)
    finished = pyqtSignal(bool)
    
    def run(self):
        # 执行耗时操作
        pass
```

## 📋 调试技巧

### 1. 数据检查
```python
# 检查数据状态
print(f"数据形状: {self.app_data.imported_data.shape}")
print(f"列名: {self.app_data.imported_data.columns.tolist()}")
```

### 2. 配置检查
```python
# 检查映射配置
print(f"映射配置: {self.app_data.data_mapping_config}")
```

### 3. 异常处理
```python
try:
    # 操作代码
    pass
except Exception as e:
    QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
```

## 🎨 UI样式

### 统一样式应用
```python
# 在各模块中应用统一样式
self.setStyleSheet("""
    QDialog {
        background-color: #f0f0f0;
    }
    QPushButton {
        background-color: #4CAF50;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
    }
""")
```

## 📦 依赖管理

### 核心依赖
```
PyQt6>=6.4.0          # GUI框架
pandas>=1.5.0         # 数据处理
numpy>=1.21.0         # 数值计算
matplotlib>=3.5.0     # 图表绘制
```

### 可选依赖
```
openpyxl>=3.0.0       # Excel支持
chardet>=5.0.0        # 编码检测
```

## 🔄 版本控制

### 重要文件
- 源代码: `*_pyqt.py`
- 配置: `*.json`
- 文档: `*.md`
- 依赖: `requirements_pyqt.txt`

### 备份建议
- 定期备份配置文件
- 保存重要的数据映射配置
- 记录自定义修改

## 📞 技术支持

### 日志查看
- 控制台输出包含详细错误信息
- 各模块都有异常处理和用户提示

### 性能优化
- 大数据文件使用分块处理
- 耗时操作使用工作线程
- 及时释放不需要的数据

---

## 📝 更新记录
- **2025-06-30**: 创建快速参考指南
- **版本**: PyQt6重构版
- **状态**: 功能完善，持续维护

*此指南提供了程序使用和开发的快速参考，详细信息请查看对应的源代码和文档。*
