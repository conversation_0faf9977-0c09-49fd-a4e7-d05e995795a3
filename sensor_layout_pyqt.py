# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
import os
import json
import re
import traceback  # 修复 #1: 导入 traceback 模块
from pathlib import Path

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView,
        QGroupBox, QFileDialog, QMessageBox, QInputDialog, QTreeWidget,
        QTreeWidgetItem, QSlider, QFrame, QScrollArea, QSizePolicy
    )
    from PyQt6.QtCore import Qt, pyqtSignal, QRectF
    from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QBrush, QColor
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6 pillow")
    sys.exit(1)


class ImageCanvas(QLabel):
    """可缩放和拖拽的图片画布"""
    point_added = pyqtSignal(int, int, str)  # x, y (image coords), name
    point_dragged = pyqtSignal(str, int, int) # name, new_x (image coords), new_y (image coords)
    point_selected = pyqtSignal(str) # name of selected point
    add_point_requested = pyqtSignal(float, float) # screen_x, screen_y

    def __init__(self):
        super().__init__()
        self.setMinimumSize(600, 400)
        self.setStyleSheet("border: 1px solid gray; background-color: lightgray;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMouseTracking(True)

        # 图片相关
        self.original_pixmap = None
        self.zoom_factor = 1.0
        self.pan_offset_x = 0.0
        self.pan_offset_y = 0.0
        self.last_pan_pos = None
        self.is_panning = False

        # 温度点 (由父窗口管理，这里只保留引用)
        self.temp_points = {}

        # 点拖拽相关
        self.dragging_point_name = None
        self.selected_point_name = None

        self.setText("请导入背景图片")

    def set_points_data(self, points_data):
        """从父窗口接收点的引用，用于绘制"""
        self.temp_points = points_data

    def get_point_at_position(self, screen_pos):
        """检查指定屏幕坐标是否有温度点. screen_pos is QPointF."""
        rect_size = 20
        # 从后往前遍历，这样顶层的点会被先选中
        for name, point_data in reversed(list(self.temp_points.items())):
            point_screen_center_x, point_screen_center_y = self.image_to_screen_coords(point_data['x'], point_data['y'])
            # 增加点击区域的容差
            if (abs(screen_pos.x() - point_screen_center_x) <= rect_size / 2 and
                abs(screen_pos.y() - point_screen_center_y) <= rect_size / 2):
                return name
        return None

    def screen_to_image_coords(self, screen_x, screen_y):
        """将屏幕坐标 (QLabel widget coords) 转换为图片上点中心的坐标 (original image coords)"""
        if not self.original_pixmap or self.zoom_factor == 0:
            return screen_x, screen_y
        image_x = (screen_x - self.pan_offset_x) / self.zoom_factor
        image_y = (screen_y - self.pan_offset_y) / self.zoom_factor
        return image_x, image_y

    def image_to_screen_coords(self, image_x, image_y):
        """将图片上点中心的坐标 (original image coords) 转换为屏幕坐标 (QLabel widget coords)"""
        if not self.original_pixmap:
            return image_x, image_y
        screen_x = image_x * self.zoom_factor + self.pan_offset_x
        screen_y = image_y * self.zoom_factor + self.pan_offset_y
        return screen_x, screen_y

    def get_color_for_value(self, value):
        """根据数值获取颜色梯度 - 蓝色到红色"""
        if value is None or value == 'N/A' or pd.isna(value) or value == '':
            return QColor(128, 128, 128)

        try:
            current_value_float = float(value)
        except (ValueError, TypeError):
            return QColor(128, 128, 128)

        valid_values = []
        # 修复 #2: 循环收集所有有效值，然后再进行判断和计算
        for p_data in self.temp_points.values():
            pval = p_data.get('value')
            if pval is not None and pval != 'N/A' and pval != '' and not pd.isna(pval):
                try:
                    valid_values.append(float(pval))
                except (ValueError, TypeError):
                    continue
        
        # 修复 #2: 将判断逻辑移到循环外部
        if not valid_values:
            return QColor(150, 150, 255)

        min_val = min(valid_values)
        max_val = max(valid_values)

        if max_val == min_val:
            return QColor(128, 0, 128) # 所有点值相同时显示紫色

        # 归一化处理
        try:
            normalized = (current_value_float - min_val) / (max_val - min_val)
            normalized = max(0.0, min(1.0, normalized)) # 限制在 0-1 之间

            # 蓝 -> 绿 -> 红 的颜色梯度
            if normalized <= 0.5:
                ratio = normalized * 2
                red = 0
                green = int(255 * ratio)
                blue = int(255 * (1 - ratio))
            else:
                ratio = (normalized - 0.5) * 2
                red = int(255 * ratio)
                green = int(255 * (1 - ratio))
                blue = 0
            return QColor(red, green, blue)
        except Exception as e:
            print(f"颜色计算错误: {e}, value: {value}, type: {type(value)}")
            return QColor(128, 128, 128)

    def load_image(self, image_path):
        """加载图片"""
        pixmap = QPixmap(image_path)
        if pixmap.isNull():
            QMessageBox.critical(self, "错误", f"无法加载图片: {image_path}")
            self.original_pixmap = None
            self.update_display()
            return False
        
        self.original_pixmap = pixmap
        self.zoom_factor = 1.0
        self.pan_offset_x = 0.0
        self.pan_offset_y = 0.0
        self.selected_point_name = None
        self.update_display()
        return True

    def update_display(self):
        """更新显示"""
        if self.original_pixmap is None or self.original_pixmap.isNull():
            self.setText("请导入背景图片")
            return

        if self.width() <= 0 or self.height() <= 0:
            return

        self.setText("")
        display_pixmap = QPixmap(self.size())
        display_pixmap.fill(Qt.GlobalColor.lightGray)
        painter = QPainter(display_pixmap)

        source_rect = QRectF(
            -self.pan_offset_x / self.zoom_factor,
            -self.pan_offset_y / self.zoom_factor,
            self.width() / self.zoom_factor,
            self.height() / self.zoom_factor
        )
        target_rect = QRectF(0.0, 0.0, float(self.width()), float(self.height()))
        painter.drawPixmap(target_rect, self.original_pixmap, source_rect)

        rect_size = 20
        for name, point_data in self.temp_points.items():
            screen_center_x, screen_center_y = self.image_to_screen_coords(point_data['x'], point_data['y'])

            # 简单的视锥剔除
            if not (-rect_size < screen_center_x < self.width() + rect_size and
                    -rect_size < screen_center_y < self.height() + rect_size):
                continue

            color = self.get_color_for_value(point_data.get('value'))
            pen_color = QColor("black")
            pen_width = 1
            if name == self.selected_point_name:
                pen_color = QColor("orange")
                pen_width = 2

            painter.setPen(QPen(pen_color, pen_width))
            painter.setBrush(QBrush(color))

            rect_top_left_x = screen_center_x - rect_size / 2.0
            rect_top_left_y = screen_center_y - rect_size / 2.0
            painter.drawRect(QRectF(rect_top_left_x, rect_top_left_y, float(rect_size), float(rect_size)))

            # 绘制文本
            font = painter.font()
            font.setPointSize(max(6, int(8 * self.zoom_factor)))
            painter.setFont(font)
            painter.setPen(QColor("black"))

            value = point_data.get('value', 'N/A')
            value_text = "N/A"
            if pd.notna(value) and value != 'N/A' and value != '':
                try:
                    value_text = f"{float(value):.1f}"
                except (ValueError, TypeError):
                    value_text = str(value)
            
            painter.drawText(int(rect_top_left_x + 2), int(rect_top_left_y + rect_size * 0.45), name)
            painter.drawText(int(rect_top_left_x + 2), int(rect_top_left_y + rect_size * 0.9), value_text)

        painter.end()
        self.setPixmap(display_pixmap)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.original_pixmap is None or self.original_pixmap.isNull():
            return

        pos = event.position()

        # 修复 #3: 始终将右键点击用于平移
        if event.button() == Qt.MouseButton.RightButton:
            self.is_panning = True
            self.last_pan_pos = pos
            return # 事件处理完毕，直接返回

        if event.button() == Qt.MouseButton.LeftButton:
            clicked_point_name = self.get_point_at_position(pos)
            self.selected_point_name = clicked_point_name
            
            if clicked_point_name:
                self.dragging_point_name = clicked_point_name
                self.last_pan_pos = pos
                self.point_selected.emit(clicked_point_name)
            else:
                # 左键点击空白处，请求添加新点
                self.add_point_requested.emit(pos.x(), pos.y())
            
            self.update_display()

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        pos = event.position()
        # 拖拽点
        if self.dragging_point_name and self.last_pan_pos:
            img_dx = (pos.x() - self.last_pan_pos.x()) / self.zoom_factor
            img_dy = (pos.y() - self.last_pan_pos.y()) / self.zoom_factor

            point = self.temp_points[self.dragging_point_name]
            new_x = point['x'] + img_dx
            new_y = point['y'] + img_dy
            
            # 更新内部坐标以便实时显示
            point['x'] = new_x
            point['y'] = new_y
            
            self.last_pan_pos = pos
            self.update_display()
            # 发射信号，让父窗口更新其主数据
            self.point_dragged.emit(self.dragging_point_name, int(new_x), int(new_y))

        # 平移画布
        elif self.is_panning and self.last_pan_pos:
            dx = pos.x() - self.last_pan_pos.x()
            dy = pos.y() - self.last_pan_pos.y()
            self.pan_offset_x += dx
            self.pan_offset_y += dy
            self.last_pan_pos = pos
            self.update_display()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.MouseButton.LeftButton:
            self.dragging_point_name = None
        
        if event.button() == Qt.MouseButton.RightButton:
            self.is_panning = False

        self.last_pan_pos = None

    def wheelEvent(self, event):
        """鼠标滚轮事件 - 缩放"""
        if self.original_pixmap is None or self.original_pixmap.isNull():
            return

        delta = event.angleDelta().y()
        zoom_factor_delta = 1.1 if delta > 0 else (1.0 / 1.1)

        mouse_pos = event.position()
        img_coord_x_before_zoom = (mouse_pos.x() - self.pan_offset_x) / self.zoom_factor
        img_coord_y_before_zoom = (mouse_pos.y() - self.pan_offset_y) / self.zoom_factor

        new_zoom_factor = self.zoom_factor * zoom_factor_delta
        new_zoom_factor = max(0.05, min(20.0, new_zoom_factor))

        self.pan_offset_x = mouse_pos.x() - (img_coord_x_before_zoom * new_zoom_factor)
        self.pan_offset_y = mouse_pos.y() - (img_coord_y_before_zoom * new_zoom_factor)
        self.zoom_factor = new_zoom_factor

        self.update_display()

    # 添加点的核心逻辑移至主对话框，画布只负责发出请求
    def remove_point(self, point_name):
        """删除温度点"""
        if point_name in self.temp_points:
            del self.temp_points[point_name]
            if self.selected_point_name == point_name:
                self.selected_point_name = None
            self.update_display()

    def update_point_value(self, point_name, value):
        """更新温度点数值"""
        if point_name in self.temp_points:
            self.temp_points[point_name]['value'] = value


class SensorLayoutDialog(QDialog):
    """温感布置分析窗口"""

    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.setWindowTitle("温感布置分析")
        self.setGeometry(200, 200, 1200, 800) # 窗口稍大一些
        self.setModal(True)

        self.df_imported = None
        self.data_mapping_config = {}

        self.display_mode = "slider"
        self.current_time_index = 0
        
        # 将 temp_points_data 作为唯一的数据源
        self.temp_points_data = {}
        self.image_path = None

        self.init_ui()
        # 将画布中的点数据引用指向主对话框的数据源
        self.image_canvas.set_points_data(self.temp_points_data)
        
        self.load_data_and_mappings()
        self.update_time_slider_config()

    def load_data_and_mappings(self):
        """从 app_data 加载数据并处理映射"""
        current_data_source = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data
        self.df_imported = current_data_source.copy() if current_data_source is not None else None

        config_from_app = getattr(self.app_data, 'data_mapping_config', {})
        self.data_mapping_config = config_from_app if isinstance(config_from_app, dict) else {}

        if self.df_imported is None and not self.isHidden():
            QMessageBox.warning(self, "无数据", "主数据未导入或为空。")

        for name in list(self.temp_points_data.keys()):
            self._update_point_series(name)

        self.calculate_and_redraw_based_on_mode()

    def init_ui(self):
        """初始化用户界面"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)

        # 工具栏
        toolbar_layout = QHBoxLayout()
        toolbar_layout.addWidget(QPushButton("导入图片", clicked=self.import_image_ui))
        toolbar_layout.addWidget(QPushButton("删除选中点", clicked=self.delete_selected_point_ui))
        
        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        toolbar_layout.addWidget(separator1)

        toolbar_layout.addWidget(QPushButton("显示温差", clicked=lambda: self.set_summary_display_mode_ui("delta_t")))
        toolbar_layout.addWidget(QPushButton("最大值", clicked=lambda: self.set_summary_display_mode_ui("max_val")))
        toolbar_layout.addWidget(QPushButton("最小值", clicked=lambda: self.set_summary_display_mode_ui("min_val")))
        toolbar_layout.addWidget(QPushButton("时间点值", clicked=lambda: self.set_summary_display_mode_ui("slider")))

        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        toolbar_layout.addWidget(separator2)
        
        toolbar_layout.addWidget(QPushButton("保存项目", clicked=self.save_project_ui))
        toolbar_layout.addWidget(QPushButton("加载项目", clicked=self.load_project_ui))
        toolbar_layout.addStretch()
        toolbar_layout.addWidget(QPushButton("返回主页", clicked=self.accept))
        main_layout.addLayout(toolbar_layout)

        content_layout = QHBoxLayout()
        self.image_canvas = ImageCanvas()
        self.image_canvas.point_added.connect(self.on_point_added_canvas) # 仍然需要连接以处理名称等
        self.image_canvas.add_point_requested.connect(self.add_temperature_point) # 新的信号连接
        self.image_canvas.point_selected.connect(self.on_point_selected_canvas)
        self.image_canvas.point_dragged.connect(self.on_point_dragged_canvas)
        content_layout.addWidget(self.image_canvas, 3)

        right_panel = QFrame()
        right_panel.setFixedWidth(350) # 稍微加宽
        right_layout = QVBoxLayout(right_panel)

        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["温度点", "值", "映射CSV列"])
        self.tree.setColumnWidth(0, 120)
        self.tree.setColumnWidth(1, 80)
        self.tree.itemSelectionChanged.connect(self.on_tree_selection_changed)
        right_layout.addWidget(self.tree)

        value_group = QGroupBox("当前数值显示")
        value_layout = QVBoxLayout(value_group)
        self.current_value_label = QLabel("选择温度点查看实时数值")
        self.current_value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_value_label.setStyleSheet("border: 1px solid gray; padding: 10px; background-color: white; font-size: 14px;")
        value_layout.addWidget(self.current_value_label)
        right_layout.addWidget(value_group)

        content_layout.addWidget(right_panel)
        main_layout.addLayout(content_layout)

        slider_frame = QFrame()
        slider_layout = QHBoxLayout(slider_frame)
        slider_layout.addWidget(QLabel("时间点:"))
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setMinimum(0)
        self.time_slider.setMaximum(0)
        self.time_slider.valueChanged.connect(self.on_time_slider_value_changed)
        slider_layout.addWidget(self.time_slider)
        self.time_label = QLabel("时间索引: N/A")
        slider_layout.addWidget(self.time_label)
        main_layout.addWidget(slider_frame)

    def _generate_new_point_name(self):
        """修复 #4: 生成唯一的 P<n> 格式的测温点名称"""
        base_name = "P"
        max_num = 0
        # 使用正则表达式从现有名称中提取数字
        p_name_pattern = re.compile(f"^{base_name}(\\d+)$")
        for name in self.temp_points_data.keys():
            match = p_name_pattern.match(name)
            if match:
                num = int(match.group(1))
                if num > max_num:
                    max_num = num
        return f"{base_name}{max_num + 1}"

    def add_temperature_point(self, screen_x, screen_y):
        """处理画布发送的添加点请求"""
        if self.image_canvas.original_pixmap is None:
            QMessageBox.warning(self, "无图片", "请先导入背景图片。")
            return

        img_x, img_y = self.image_canvas.screen_to_image_coords(screen_x, screen_y)
        
        point_name_suggestion = self._generate_new_point_name()

        point_name, ok = QInputDialog.getText(
            self, "设置测温点名称", "请输入测温点名称:", text=point_name_suggestion
        )

        if ok and point_name:
            point_name = point_name.strip()
            if not point_name:
                QMessageBox.warning(self, "名称无效", "测温点名称不能为空。")
                return
            if point_name in self.temp_points_data:
                QMessageBox.warning(self, "名称已存在", f"测温点名称 '{point_name}' 已存在。")
                return

            # 直接在主数据结构中创建点
            self.temp_points_data[point_name] = {
                'x': img_x, 'y': img_y, 'value': np.nan,
                "mapped_csv_col": None, "raw_series": None, "display_value": np.nan
            }
            
            self._update_point_series(point_name)
            self.image_canvas.selected_point_name = point_name
            self.image_canvas.point_added.emit(int(img_x), int(img_y), point_name)

    def import_image_ui(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择背景图片", "", "图片文件 (*.jpg *.jpeg *.png *.bmp)")
        if file_path:
            # 询问是否保留现有测点
            reply = QMessageBox.question(self, '确认', '是否保留当前已布置的测温点?',
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
                                       QMessageBox.StandardButton.No)

            if self.image_canvas.load_image(file_path):
                self.image_path = file_path
                if reply == QMessageBox.StandardButton.No:
                    self.temp_points_data.clear()
                self.update_treeview()
                self.calculate_and_redraw_based_on_mode()

    def delete_selected_point_ui(self):
        point_name = self.image_canvas.selected_point_name
        if point_name:
            if point_name in self.temp_points_data:
                del self.temp_points_data[point_name]
            
            # 画布不再维护自己的点列表，但需要更新选中状态
            self.image_canvas.selected_point_name = None
            self.update_treeview()
            self.image_canvas.update_display()
            self.current_value_label.setText("选择温度点查看实时数值")
        else:
            QMessageBox.warning(self, "无选中点", "请先在图片上选择一个点。")

    def on_point_added_canvas(self, img_x, img_y, name):
        """当点被成功添加并命名后，更新UI"""
        self.update_treeview()
        self.calculate_and_redraw_based_on_mode()
        # 选中新添加的点
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            if item.text(0) == name:
                self.tree.setCurrentItem(item)
                break

    def on_point_selected_canvas(self, name):
        """当画布上的点被选中时，同步更新右侧树视图和信息标签"""
        self.image_canvas.selected_point_name = name
        # 寻找并选中树中的对应项
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            if item.text(0) == name:
                self.tree.setCurrentItem(item)
                break
        self.update_current_value_label(name)
        self.image_canvas.update_display() # 重绘以高亮显示

    def on_point_dragged_canvas(self, name, new_img_x, new_img_y):
        """当点被拖拽时，更新主数据结构中的坐标"""
        if name in self.temp_points_data:
            self.temp_points_data[name]['x'] = new_img_x
            self.temp_points_data[name]['y'] = new_img_y

    def _update_point_series(self, point_name):
        """根据映射关系，为指定的点更新其数据序列"""
        if point_name not in self.temp_points_data: return
        
        point_info = self.temp_points_data[point_name]
        mapped_col = None
        series = None
        current_df = self.df_imported

        if current_df is not None:
            # 优先从映射配置中查找
            mappings = self.data_mapping_config.get('mappings', {})
            for csv_col, logical_name in mappings.items():
                if logical_name == point_name:
                    if csv_col in current_df.columns:
                        mapped_col = csv_col
                        series = current_df[csv_col]
                    else:
                        point_info["mapped_csv_col"] = f"{csv_col} (列不存在)"
                    break
            
            # 如果映射中没有，尝试将点名直接作为列名
            if not mapped_col and point_name in current_df.columns:
                mapped_col = point_name
                series = current_df[point_name]

        point_info["mapped_csv_col"] = mapped_col
        point_info["raw_series"] = series

    def update_treeview(self):
        """更新右侧的树状视图，显示所有点的信息"""
        self.tree.clear()
        selected_item = None
        for name, data in sorted(self.temp_points_data.items()):
            val_str = "N/A"
            display_val = data.get('display_value')
            if pd.notnull(display_val):
                val_str = f"{display_val:.2f}"

            col_str = data.get('mapped_csv_col') or "未映射"
            
            item = QTreeWidgetItem([name, val_str, col_str])
            self.tree.addTopLevelItem(item)
            if name == self.image_canvas.selected_point_name:
                selected_item = item
        
        if selected_item:
            self.tree.setCurrentItem(selected_item)

    def on_tree_selection_changed(self):
        """当在树视图中选择一个点时，同步更新画布和信息标签"""
        current_item = self.tree.currentItem()
        if current_item:
            point_name = current_item.text(0)
            if self.image_canvas.selected_point_name != point_name:
                self.image_canvas.selected_point_name = point_name
                self.update_current_value_label(point_name)
                self.image_canvas.update_display()
        else:
            if self.image_canvas.selected_point_name is not None:
                self.image_canvas.selected_point_name = None
                self.current_value_label.setText("选择温度点查看实时数值")
                self.image_canvas.update_display()

    def update_current_value_label(self, point_name):
        """更新显示当前选中点数值的标签"""
        if point_name in self.temp_points_data:
            data = self.temp_points_data[point_name]
            value = data.get('display_value')
            val_text = "N/A"
            if pd.notnull(value):
                val_text = f"{value:.2f}"
            self.current_value_label.setText(f"<b>{point_name}:</b> {val_text}")
        else:
            self.current_value_label.setText("选择温度点查看实时数值")

    def calculate_and_redraw_based_on_mode(self):
        """根据当前的显示模式（最大值、最小值等），计算每个点的值并重绘所有内容"""
        current_df = self.df_imported
        if current_df is None:
            for name in self.temp_points_data:
                self.temp_points_data[name]["display_value"] = np.nan
                self.image_canvas.update_point_value(name, np.nan)
            self.update_treeview()
            self.image_canvas.update_display()
            return

        if self.display_mode == "slider":
            self.on_time_slider_value_changed(self.time_slider.value())
            return

        for name, point_info in self.temp_points_data.items():
            series = point_info.get("raw_series")
            value_to_display = np.nan
            if series is not None and not series.empty:
                numeric_series = pd.to_numeric(series, errors='coerce').dropna()
                if not numeric_series.empty:
                    if self.display_mode == "delta_t":
                        value_to_display = numeric_series.iloc[-1] - numeric_series.iloc[0] if len(numeric_series) >= 2 else np.nan
                    elif self.display_mode == "max_val":
                        value_to_display = numeric_series.max()
                    elif self.display_mode == "min_val":
                        value_to_display = numeric_series.min()
            
            point_info["display_value"] = value_to_display
            self.image_canvas.update_point_value(name, value_to_display)

        self.update_treeview()
        self.image_canvas.update_display()

    def set_summary_display_mode_ui(self, mode):
        """设置显示模式并更新UI"""
        self.display_mode = mode
        is_slider_mode = (mode == "slider")
        self.time_slider.setEnabled(is_slider_mode)
        self.time_label.setVisible(is_slider_mode)

        if is_slider_mode:
            self.update_time_slider_config()
        self.calculate_and_redraw_based_on_mode()

    def on_time_slider_value_changed(self, value_idx):
        """当时间滑块值改变时，更新所有点在对应时间点的值"""
        if self.display_mode != "slider": return

        self.current_time_index = value_idx
        time_label_text = f"时间索引: {self.current_time_index}"
        current_df = self.df_imported

        if current_df is not None and not current_df.empty and self.current_time_index < len(current_df):
            time_col_name = self.data_mapping_config.get('time_column')
            if time_col_name and time_col_name in current_df.columns:
                actual_time_val = current_df[time_col_name].iloc[self.current_time_index]
                time_label_text += f" ({actual_time_val})"

            for name, point_info in self.temp_points_data.items():
                series = point_info.get("raw_series")
                value_at_time = np.nan
                if series is not None:
                    numeric_series = pd.to_numeric(series, errors='coerce')
                    if self.current_time_index < len(numeric_series):
                        value_at_time = numeric_series.iloc[self.current_time_index]
                
                point_info["display_value"] = value_at_time
                self.image_canvas.update_point_value(name, value_at_time)
            
            self.update_treeview()
            self.image_canvas.update_display()
            self.update_current_value_label(self.image_canvas.selected_point_name)

        self.time_label.setText(time_label_text)

    def update_time_slider_config(self):
        """根据导入的数据更新时间滑块的范围"""
        current_df = self.df_imported
        if current_df is not None and not current_df.empty:
            max_index = len(current_df) - 1
            self.time_slider.setMaximum(max_index if max_index >= 0 else 0)
            
            new_time_index = min(self.current_time_index, max_index) if max_index >= 0 else 0
            self.current_time_index = max(0, new_time_index)

            self.time_slider.setValue(self.current_time_index)
            self.time_slider.setEnabled(True)
            if self.display_mode == "slider":
                self.on_time_slider_value_changed(self.current_time_index)
        else:
            self.time_slider.setMaximum(0)
            self.time_slider.setValue(0)
            self.current_time_index = 0
            self.time_slider.setEnabled(False)
            self.time_label.setText("时间索引: N/A")

    def save_project_ui(self):
        """保存项目文件"""
        file_path, _ = QFileDialog.getSaveFileName(self, "保存项目", "", "温感布置项目 (*.slt_proj.json)")
        if not file_path: return
        
        try:
            # 数据源现在统一了，直接保存 temp_points_data
            project_points_data = {
                name: {"x": data['x'], "y": data['y'], "mapped_csv_col": data.get("mapped_csv_col")}
                for name, data in self.temp_points_data.items()
            }
            
            project_data = {
                "version": "1.1",
                "image_path": self.image_path,
                "csv_filepath": getattr(self.app_data, 'imported_filepath', None),
                "temp_points": project_points_data,
                "view_state": {
                    "zoom_factor": self.image_canvas.zoom_factor,
                    "pan_offset_x": self.image_canvas.pan_offset_x,
                    "pan_offset_y": self.image_canvas.pan_offset_y,
                    "display_mode": self.display_mode,
                    "current_time_index": self.current_time_index,
                    "selected_point_name": self.image_canvas.selected_point_name
                }
            }
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=4)
            QMessageBox.information(self, "成功", "项目已保存。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"无法保存项目: {str(e)}\n{traceback.format_exc()}")

    def load_project_ui(self):
        """加载项目文件"""
        file_path, _ = QFileDialog.getOpenFileName(self, "加载项目", "", "温感布置项目 (*.slt_proj.json)")
        if not file_path: return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                proj_data = json.load(f)

            # 先加载主程序的数据和映射配置
            self.load_data_and_mappings()

            self.image_path = proj_data.get("image_path")
            if self.image_path and os.path.exists(self.image_path):
                self.image_canvas.load_image(self.image_path)
            elif self.image_path:
                QMessageBox.warning(self, "图片缺失", f"图片 '{self.image_path}' 未找到。")
                self.image_canvas.original_pixmap = None

            self.temp_points_data.clear()
            
            loaded_points = proj_data.get("temp_points", {})
            for name, data in loaded_points.items():
                coords = data.get("coords") # 兼容旧格式
                if coords is None: # 适配新格式
                    coords = (data.get('x', 0), data.get('y', 0))

                self.temp_points_data[name] = {
                    'x': coords[0], 'y': coords[1], 'value': np.nan,
                    "mapped_csv_col": data.get("mapped_csv_col"),
                    "display_value": np.nan, "raw_series": None
                }
                self._update_point_series(name)

            vs = proj_data.get("view_state", {})
            self.image_canvas.zoom_factor = vs.get("zoom_factor", 1.0)
            self.image_canvas.pan_offset_x = vs.get("pan_offset_x", 0.0)
            self.image_canvas.pan_offset_y = vs.get("pan_offset_y", 0.0)
            self.display_mode = vs.get("display_mode", "slider")
            self.current_time_index = vs.get("current_time_index", 0)
            self.image_canvas.selected_point_name = vs.get("selected_point_name")

            self.update_time_slider_config()
            self.set_summary_display_mode_ui(self.display_mode) # 这会触发重绘
            
            QMessageBox.information(self, "成功", "项目已加载。")
        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"无法加载项目: {str(e)}\n{traceback.format_exc()}")


def show_sensor_layout_window(parent, app_data):
    """显示温感布置分析窗口的入口函数"""
    dialog = SensorLayoutDialog(parent, app_data)
    dialog.exec()