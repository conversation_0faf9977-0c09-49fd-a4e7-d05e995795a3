# -*- coding: utf-8 -*-
"""
测试PyQt6模块导入和基本功能
"""

import sys
import traceback

def test_module_import(module_name):
    """测试模块导入"""
    try:
        __import__(module_name)
        print(f"✓ {module_name} - 导入成功")
        return True
    except Exception as e:
        print(f"✗ {module_name} - 导入失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=== PyQt6模块导入测试 ===\n")
    
    modules_to_test = [
        "main_pyqt",
        "charging_analysis_pyqt", 
        "data_mapping_pyqt",
        "sensor_layout_pyqt",
        "data_analysis_view_pyqt",
        "data_filtering_pyqt"
    ]
    
    success_count = 0
    total_count = len(modules_to_test)
    
    for module in modules_to_test:
        if test_module_import(module):
            success_count += 1
    
    print(f"\n=== 测试结果 ===")
    print(f"成功: {success_count}/{total_count}")
    print(f"失败: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有模块导入成功！")
        return True
    else:
        print("❌ 部分模块导入失败，请检查错误信息。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
