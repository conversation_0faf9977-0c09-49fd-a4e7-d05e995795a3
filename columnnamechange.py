import tkinter as tk
from tkinter import filedialog, messagebox
import pandas as pd
import numpy as np

def load_data():
    """加载用户选择的CSV文件和固定CSV文件"""
    file_path = filedialog.askopenfilename(
        initialdir="/",
        title="选择CSV文件",
        filetypes=(("CSV文件", "*.csv"), ("所有文件", "*.*"))
    )
    if not file_path:
        messagebox.showinfo("提示", "未选择文件，程序退出。")
        return None, None

    df_B = pd.read_csv(file_path, header=0)
    df_A = pd.read_csv('E0XEH3signalchange.csv', header=0)

         # Check if 'SOC_real' exists in df_B
    if 'SD7_BBAT_SOC_HVS' not in df_B.columns:
        # Check if 'BMS_SOCDis' exists
        if 'BMS_SOCDis' in df_B.columns:
            # Ask user if they want to use 'BMS_SOCDis' as 'SOC_real'
            use_bms_socdis = messagebox.askyesno(
                "选择列",
                " 'SOC_real' 列不存在。\n是否将 'BMS_SOCDis' 用作 'SOC_real'?"
            )
            if use_bms_socdis:
               df_B['SD7_BBAT_SOC_HVS'] = df_B['BMS_SOCDis'] 
            else:
                messagebox.showerror("错误", "缺少必要的列 'SD7_BBAT_SOC_HVS'。")
                return None, None
        else:
            messagebox.showerror("错误", "缺少必要的列 'SD7_BBAT_SOC_HVS' 和 'BMS_SOCDis'。")
            return None, None






    return df_B, df_A

def rename_columns(df_B, df_A):
    """根据用户选择替换列名"""
    column_A = df_A['Signal_inuse']
    signal = messagebox.askyesno("选择信号列", "是否选择 'E0X Signal_DBC' 列？")
    column_B = df_A['E0X Signal_DBC'] if signal else df_A['EH3 Signal_DBC']



    for col in df_B.columns:
        matching_cols = [x for x in column_B.values if x in col]
        if matching_cols:
            new_cols = column_A[column_B.isin(matching_cols)].values
            for matching_col, new_col in zip(matching_cols, new_cols):
                if matching_col == col:
                    df_B.rename(columns={col: new_col}, inplace=True)
    return df_B

def process_data(df_B):
    """处理数据：删除行、计算新列"""
     # 计算所有带 'CellU' 的列的最大值和最小值，排除大于 60 的数据
    cellu_columns = [col for col in df_B.columns if 'CellU' in col]
    if cellu_columns:
        df_cellu = df_B[cellu_columns].applymap(lambda x: np.nan if x > 60 else x)
        df_B['BMS_HvBattCellU_max'] = df_cellu.max(axis=1)
        df_B['BMS_HvBattCellU_min'] = df_cellu.min(axis=1)
    else:
        messagebox.showinfo("提示", "未找到包含 'CellU' 的列，无法计算 'BMS_HvBattCellU_max' 和 'BMS_HvBattCellU_min'。")

    # 计算所有带 'TempSensorTemp' 的列的最大值和最小值，排除大于 100 的数据
    tempsensortemp_columns = [col for col in df_B.columns if 'TempSensorTemp' in col]
    if tempsensortemp_columns:
        df_tempsensortemp = df_B[tempsensortemp_columns].applymap(lambda x: np.nan if x > 100 else x)
        df_B['BMS_HvBattTempSensorTemp_max'] = df_tempsensortemp.max(axis=1)
        df_B['BMS_HvBattTempSensorTemp_min'] = df_tempsensortemp.min(axis=1)
    else:
        messagebox.showinfo("提示", "未找到包含 'TempSensorTemp' 的列，无法计算 'BMS_HvBattTempSensorTemp_max' 和 'BMS_HvBattTempSensorTemp_min'。")

    # 检查并替换 'BMS_HvBattCellVoltMax'
    if 'CellVoltage_Max' not in df_B.columns or df_B['CellVoltage_Max'].eq(0).all():
        if 'BMS_HvBattCellU_max' in df_B.columns:
            df_B.rename(columns={'CellVoltage_Max': 'CellVoltage_Max_old'}, inplace=True)
            df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellU_max']
            #df_B.rename(columns={'BMS_HvBattCellU_max': 'CellVoltage_Max'}, inplace=True)
        else:
            messagebox.showerror("错误", "列 'BMS_HvBattCellU_max' 不存在，无法替换 'BMS_HvBattCellVoltMax'。")
            return df_B
    else:
        df_B.rename(columns={'BMS_HvBattCellVoltMax': 'CellVoltage_Max'}, inplace=True)
    
    # 检查并替换 'BMS_HvBattCellVoltMin'
    if 'CellVoltage_Min' not in df_B.columns or df_B['CellVoltage_Min'].eq(0).all():
        if 'BMS_HvBattCellU_min' in df_B.columns:
            df_B.rename(columns={'CellVoltage_Min': 'CellVoltage_Min_old'}, inplace=True)
            df_B['CellVoltage_Min'] = df_B['BMS_HvBattCellU_min']
            #df_B.rename(columns={'BMS_HvBattCellU_min': 'CellVoltage_Min'}, inplace=True)
        else:
            messagebox.showerror("错误", "列 'BMS_HvBattCellU_min' 不存在，无法替换 'BMS_HvBattCellVoltMin'。")
            return df_B
    else:
        df_B.rename(columns={'BMS_HvBattCellVoltMin': 'CellVoltage_Min'}, inplace=True)
    
    df_B = df_B[df_B['Batt_Current_req'] < 0]  # 删除 Batt_Current_req >= 0 的行
    df_B = df_B[df_B['CellVoltage_Max'] != 0]  # 删除 CellVoltage_Max == 0 的行

    df_B['time'] = range(len(df_B))
    df_B['CellVoltage_diff'] = abs(df_B['CellVoltage_Max'] - df_B['CellVoltage_Min'])
    df_B['Temp_diff'] = abs(df_B['BattT_Max'] - df_B['BattT_Min'])
    df_B['TempWater_diff'] = abs(df_B['Outlet'] - df_B['Inlet'])
    df_B['Batt_Current'] = abs(df_B['Batt_Current'])
    df_B['Batt_Current_req'] = abs(df_B['Batt_Current_req'])
    df_B['POWER'] = -df_B['Batt_Current'] * df_B['Batt_Voltage'] / 1000

   
    return df_B



def calculate_time_differences(df_B):
    """计算SOC从98到99和99到100的时间差"""
    time_difference_1, time_difference_2 = 0, 0

    if not df_B[df_B['SOC'] >= 98].empty:
        first_row_soc_ge_98 = df_B[df_B['SOC'] >= 98].iloc[0]
        time_first = first_row_soc_ge_98['time']
        last_row_soc_lt_99 = df_B[df_B['SOC'] < 99].iloc[-1]
        time_last = last_row_soc_lt_99['time']
        time_difference_1 = time_last - time_first

    if not df_B[df_B['SOC'] >= 99].empty:
        first_row_soc_ge_99 = df_B[df_B['SOC'] >= 99].iloc[0]
        time_first2 = first_row_soc_ge_99['time']
        last_row_soc_ge_99 = df_B[df_B['SOC'] >= 99].iloc[-1]
        time_last2 = last_row_soc_ge_99['time']
        time_difference_2 = time_last2 - time_first2

    return time_difference_1, time_difference_2

def generate_results(df_B, time_difference_1, time_difference_2):
    """生成分段统计结果"""
    columns = ['CellVoltage_Max', 'CellVoltage_Min', 'CellVoltage_diff', 
               'SOC_real', 'BattT_Max', 'BattT_Min', 'Temp_diff',
               'Batt_Current_req', 'Batt_Current', 'Batt_Voltage', 
               'Inlet', 'Outlet', 'TempWater_diff', 'Time_remain', 'time',
               'BMS_HvBattCellU_max', 'BMS_HvBattCellU_min',  # 新增列
               'BMS_HvBattTempSensorTemp_max', 'BMS_HvBattTempSensorTemp_min']  # 新增列

    results = []
    curr_soc = 0

    while curr_soc < max(df_B['SOC']):
        soc_range = df_B.loc[(df_B['SOC'] >= curr_soc) & (df_B['SOC'] < curr_soc + 10)]
        row = []

        for column in columns:
            column_values = soc_range[column]
            if len(column_values) > 0:
                start = column_values.iloc[0]
                end = column_values.iloc[-1]
                max_value = column_values.max()
            else:
                start = end = max_value = None
            row.extend([start, end, max_value])

        results.append(row)
        curr_soc += 10

    new_columns = [f"{col}_{stat}" for col in columns for stat in ['start', 'end', 'max']]
    results_df = pd.DataFrame(results, columns=new_columns)

    # 补充缺失的行
    rows_to_insert = max(0, 11 - len(results_df))
    if rows_to_insert > 0:
        new_rows = pd.DataFrame({col: [None] for col in results_df.columns}, index=[0] * rows_to_insert)
        results_df = pd.concat([new_rows, results_df], axis=0, ignore_index=True)

    # 添加时间差列
    results_df['98-99'] = time_difference_1
    results_df['99-100'] = time_difference_2

    

    return results_df

def save_results(results_df, df_B):
    """保存结果到CSV文件"""
    save_path = filedialog.asksaveasfilename(
        initialdir="/",
        title="保存结果文件",
        filetypes=(("CSV文件", "*.csv"), ("所有文件", "*.*")),
        defaultextension="csv"
    )
    if save_path:
        results_df.transpose().to_csv(save_path, index=True)
        messagebox.showinfo("提示", f"结果已保存至 {save_path}")

    save_path_plotdata = filedialog.asksaveasfilename(
        initialdir="/",
        title="保存plotdata结果文件",
        filetypes=(("CSV文件", "*.csv"), ("所有文件", "*.*")),
        defaultextension="csv"
    )
    if save_path_plotdata:
        df_B[['SOC', 'POWER', 'BattT_Max', 'time', 'Batt_Current', 'Batt_Voltage']].to_csv(save_path_plotdata, index=False)
        messagebox.showinfo("提示", f"plotdata已保存至 {save_path_plotdata}")

def main():
    root = tk.Tk()
    root.withdraw()

    df_B, df_A = load_data()
    if df_B is None:
        return

    df_B = rename_columns(df_B, df_A)
    df_B = process_data(df_B)
    time_difference_1, time_difference_2 = calculate_time_differences(df_B)
    results_df = generate_results(df_B, time_difference_1, time_difference_2)
    save_results(results_df, df_B)

    root.quit()

if __name__ == "__main__":
    main()