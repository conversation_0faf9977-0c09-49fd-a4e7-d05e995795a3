# -*- coding: utf-8 -*-
"""
数据映射配置窗口 - PyQt6版本
参考data_mapping.py的功能和算法
"""

import json
import os
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QComboBox, QLineEdit, QGroupBox, QScrollArea, QWidget,
    QFrame, QMessageBox, QFileDialog
)
from PyQt6.QtCore import Qt

class MappingWindow(QDialog):
    """数据映射配置窗口 - 参考data_mapping.py"""
    
    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.setWindowTitle("数据映射配置")
        self.setGeometry(200, 200, 700, 550)
        self.setModal(True)
        
        self.mapping_entries = []  # 用于存储每行映射的组件
        self.current_csv_headers = []
        if self.app_data.imported_data is not None:
            self.current_csv_headers = [""] + self.app_data.imported_data.columns.tolist()
        
        self.setup_ui()
        self.load_initial_mappings()
        
    def setup_ui(self):
        """设置用户界面 - 参考data_mapping.py的UI布局"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 时间列选择 - 参考data_mapping.py的time_frame
        time_group = QGroupBox("时间列选择")
        time_layout = QHBoxLayout(time_group)
        
        time_layout.addWidget(QLabel("时间列 (CSV):"))
        self.time_column_combo = QComboBox()
        self.time_column_combo.addItems(self.current_csv_headers)
        self.time_column_combo.setMinimumWidth(200)
        time_layout.addWidget(self.time_column_combo)
        
        # 自动匹配常见的时间列名
        if self.app_data.data_mapping_config and self.app_data.data_mapping_config.get("time_column"):
            time_col = self.app_data.data_mapping_config.get("time_column")
            if time_col in self.current_csv_headers:
                self.time_column_combo.setCurrentText(time_col)
        elif self.current_csv_headers:
            common_time_cols = [col for col in self.current_csv_headers 
                              if isinstance(col, str) and ('time' in col.lower() or '日期' in col or '时间' in col)]
            if common_time_cols:
                self.time_column_combo.setCurrentText(common_time_cols[0])
        
        time_layout.addStretch()
        main_layout.addWidget(time_group)
        
        # 映射行控制按钮 - 参考data_mapping.py的mapping_controls_frame
        controls_group = QGroupBox("映射控制")
        controls_layout = QHBoxLayout(controls_group)
        
        add_btn = QPushButton("增加映射行")
        add_btn.clicked.connect(self.add_mapping_row_ui)
        controls_layout.addWidget(add_btn)
        
        remove_btn = QPushButton("删除最后一行")
        remove_btn.clicked.connect(self.remove_last_mapping_row_ui)
        controls_layout.addWidget(remove_btn)
        
        controls_layout.addStretch()
        main_layout.addWidget(controls_group)
        
        # 映射区域 (滚动) - 参考data_mapping.py的canvas_frame
        mapping_group = QGroupBox("数据映射关系")
        mapping_layout = QVBoxLayout(mapping_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMinimumHeight(200)
        
        self.scrollable_widget = QWidget()
        self.scrollable_layout = QVBoxLayout(self.scrollable_widget)
        self.scrollable_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        scroll_area.setWidget(self.scrollable_widget)
        mapping_layout.addWidget(scroll_area)
        main_layout.addWidget(mapping_group)

        # 自定义变量组
        custom_vars_group = QGroupBox("自定义变量")
        custom_vars_layout = QVBoxLayout(custom_vars_group)

        # 自定义变量说明
        info_label = QLabel("定义新变量，支持基本算术运算 (+, -, *, /)，使用已映射的逻辑名称")
        info_label.setWordWrap(True)
        custom_vars_layout.addWidget(info_label)

        # 自定义变量滚动区域
        custom_scroll = QScrollArea()
        custom_scroll.setWidgetResizable(True)
        custom_scroll.setMaximumHeight(150)

        self.custom_vars_widget = QWidget()
        self.custom_vars_layout = QVBoxLayout(self.custom_vars_widget)
        custom_scroll.setWidget(self.custom_vars_widget)
        custom_vars_layout.addWidget(custom_scroll)

        # 添加自定义变量按钮
        add_custom_btn = QPushButton("添加自定义变量")
        add_custom_btn.clicked.connect(self.add_custom_variable)
        custom_vars_layout.addWidget(add_custom_btn)

        main_layout.addWidget(custom_vars_group)

        # 初始化自定义变量列表
        self.custom_variable_entries = []

        # 底部按钮 - 参考data_mapping.py的bottom_button_frame
        button_layout = QHBoxLayout()
        
        load_btn = QPushButton("导入配置")
        load_btn.clicked.connect(self.load_config_ui)
        button_layout.addWidget(load_btn)
        
        save_btn = QPushButton("保存配置")
        save_btn.clicked.connect(self.save_config_ui)
        button_layout.addWidget(save_btn)
        
        button_layout.addStretch()
        
        apply_btn = QPushButton("应用并返回")
        apply_btn.clicked.connect(self.apply_and_return)
        button_layout.addWidget(apply_btn)
        
        cancel_btn = QPushButton("返回主页")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        main_layout.addLayout(button_layout)
        
    def add_mapping_row_ui(self, csv_header_val=None, logic_name_val=""):
        """在UI上添加新的映射关系行 - 参考data_mapping.py"""
        row_frame = QFrame()
        row_layout = QHBoxLayout(row_frame)
        row_layout.setContentsMargins(5, 5, 5, 5)
        
        # CSV列选择
        row_layout.addWidget(QLabel("CSV列:"))
        csv_combo = QComboBox()
        csv_combo.addItems(self.current_csv_headers)
        csv_combo.setMinimumWidth(150)
        if csv_header_val and csv_header_val in self.current_csv_headers:
            csv_combo.setCurrentText(csv_header_val)
        row_layout.addWidget(csv_combo)
        
        # 逻辑名输入
        row_layout.addWidget(QLabel("映射为程序逻辑名:"))
        logic_entry = QLineEdit()
        logic_entry.setText(logic_name_val)
        logic_entry.setMinimumWidth(150)
        row_layout.addWidget(logic_entry)
        
        row_layout.addStretch()
        
        # 存储映射条目
        mapping_entry = {
            "frame": row_frame,
            "csv_combo": csv_combo,
            "logic_entry": logic_entry
        }
        self.mapping_entries.append(mapping_entry)
        
        # 添加到滚动布局
        self.scrollable_layout.addWidget(row_frame)
        
    def remove_last_mapping_row_ui(self):
        """从UI上删除最后一行映射关系 - 参考data_mapping.py"""
        if self.mapping_entries:
            last_entry = self.mapping_entries.pop()
            last_entry["frame"].setParent(None)
            last_entry["frame"].deleteLater()
            
    def load_initial_mappings(self):
        """加载初始映射配置 - 参考data_mapping.py"""
        if self.app_data.data_mapping_config and "mappings" in self.app_data.data_mapping_config:
            mappings = self.app_data.data_mapping_config["mappings"]
            for csv_col, logic_name in mappings.items():
                self.add_mapping_row_ui(csv_col, logic_name)

            # 加载时间列配置
            if "time_column" in self.app_data.data_mapping_config:
                time_col = self.app_data.data_mapping_config["time_column"]
                if time_col in self.current_csv_headers:
                    self.time_column_combo.setCurrentText(time_col)

            # 加载自定义变量配置
            if "custom_variables" in self.app_data.data_mapping_config:
                for var_config in self.app_data.data_mapping_config["custom_variables"]:
                    var_name = var_config.get("name", "")
                    formula = var_config.get("formula", "")
                    if var_name and formula:
                        self.add_custom_variable_with_values(var_name, formula)
        else:
            # 添加一些默认映射行
            self.add_mapping_row_ui()
            self.add_mapping_row_ui()
            
    def load_config_ui(self):
        """导入配置文件 - 参考data_mapping.py"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择映射配置文件", "", "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
                
            # 清除现有映射
            while self.mapping_entries:
                self.remove_last_mapping_row_ui()

            # 清除现有自定义变量
            while self.custom_variable_entries:
                row_widget, _, _ = self.custom_variable_entries[0]
                self.remove_custom_variable(row_widget)

            # 加载时间列配置
            if "time_column" in config:
                time_col = config["time_column"]
                if time_col in self.current_csv_headers:
                    self.time_column_combo.setCurrentText(time_col)

            # 加载映射配置
            if "mappings" in config:
                for csv_col, logic_name in config["mappings"].items():
                    self.add_mapping_row_ui(csv_col, logic_name)

            # 加载自定义变量配置
            if "custom_variables" in config:
                for var_config in config["custom_variables"]:
                    var_name = var_config.get("name", "")
                    formula = var_config.get("formula", "")
                    if var_name and formula:
                        self.add_custom_variable_with_values(var_name, formula)

            QMessageBox.information(self, "成功", "配置文件加载成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载配置文件失败: {str(e)}")
            
    def save_config_ui(self):
        """保存配置文件 - 参考data_mapping.py"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存映射配置", "mapping_config.json", "JSON文件 (*.json);;所有文件 (*.*)"
        )
        
        if not file_path:
            return
            
        try:
            config = self.get_current_config()
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            QMessageBox.information(self, "成功", "配置文件保存成功！")
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置文件失败: {str(e)}")
            
    def get_current_config(self):
        """获取当前配置 - 参考data_mapping.py"""
        config = {
            "time_column": self.time_column_combo.currentText(),
            "mappings": {},
            "custom_variables": []
        }

        for entry in self.mapping_entries:
            csv_col = entry["csv_combo"].currentText()
            logic_name = entry["logic_entry"].text().strip()

            if csv_col and logic_name:
                config["mappings"][csv_col] = logic_name

        # 添加自定义变量配置
        for row_widget, name_edit, formula_edit in self.custom_variable_entries:
            var_name = name_edit.text().strip()
            formula = formula_edit.text().strip()

            if var_name and formula:
                config["custom_variables"].append({
                    "name": var_name,
                    "formula": formula
                })

        return config
        
    def apply_and_return(self):
        """应用配置并返回 - 参考data_mapping.py"""
        try:
            config = self.get_current_config()
            
            # 验证配置
            if not config["mappings"]:
                QMessageBox.warning(self, "警告", "请至少配置一个映射关系。")
                return
                
            # 保存到应用数据
            self.app_data.data_mapping_config = config
            
            QMessageBox.information(self, "成功", "数据映射配置已应用！")
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"应用配置时发生错误: {str(e)}")

    def add_custom_variable(self):
        """添加自定义变量行"""
        self.add_custom_variable_with_values("", "")

    def add_custom_variable_with_values(self, var_name="", formula=""):
        """添加自定义变量行并设置初始值"""
        row_widget = QWidget()
        row_layout = QHBoxLayout(row_widget)
        row_layout.setContentsMargins(0, 0, 0, 0)

        # 变量名输入
        name_edit = QLineEdit()
        name_edit.setPlaceholderText("变量名")
        name_edit.setMaximumWidth(120)
        name_edit.setText(var_name)
        row_layout.addWidget(QLabel("变量名:"))
        row_layout.addWidget(name_edit)

        # 公式输入
        formula_edit = QLineEdit()
        formula_edit.setPlaceholderText("例: CellVoltage_Max + CellVoltage_Min")
        formula_edit.setText(formula)
        row_layout.addWidget(QLabel("公式:"))
        row_layout.addWidget(formula_edit)

        # 删除按钮
        delete_btn = QPushButton("删除")
        delete_btn.setMaximumWidth(60)
        delete_btn.clicked.connect(lambda: self.remove_custom_variable(row_widget))
        row_layout.addWidget(delete_btn)

        # 添加到布局
        self.custom_vars_layout.addWidget(row_widget)

        # 保存引用
        self.custom_variable_entries.append((row_widget, name_edit, formula_edit))

    def remove_custom_variable(self, row_widget):
        """删除自定义变量行"""
        # 从布局中移除
        self.custom_vars_layout.removeWidget(row_widget)
        row_widget.deleteLater()

        # 从列表中移除
        self.custom_variable_entries = [
            entry for entry in self.custom_variable_entries
            if entry[0] != row_widget
        ]
