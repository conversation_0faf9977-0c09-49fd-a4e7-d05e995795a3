# 数据可视化程序 PyQt6 升级指南

## 📋 升级概述

本次升级将原有的Tkinter界面全面升级为PyQt6，提供更专业的用户界面和更强大的功能。

### 🎯 主要改进

1. **GUI框架升级**: 从Tkinter升级到PyQt6
2. **数据筛选优化**: 实现"返回并应用"功能
3. **充电分析算法优化**: 多种查表方式组合分析
4. **数据导出扩展**: 更丰富的导出选项和格式

## 🛠️ 安装要求

### 系统要求
- Python 3.8 或更高版本
- Windows 10/11, macOS 10.14+, 或 Linux

### 依赖安装

#### 1. 安装PyQt6
```bash
pip install PyQt6>=6.4.0
```

#### 2. 安装其他依赖
```bash
pip install -r requirements.txt
```

#### 3. 验证安装
```python
python -c "from PyQt6.QtWidgets import QApplication; print('PyQt6安装成功')"
```

## 🚀 运行程序

### 启动PyQt6版本
```bash
python main_pyqt.py
```

### 启动原Tkinter版本（备用）
```bash
python main_opt.py
```

## 🔧 新功能说明

### 1. 现代化界面设计

#### 主要特性
- **专业外观**: 现代化的GUI设计
- **响应式布局**: 自适应窗口大小
- **主题样式**: 统一的视觉风格
- **菜单栏**: 完整的菜单系统
- **状态栏**: 实时状态信息

#### 界面组件
- **数据控制面板**: 导入数据和状态显示
- **数据预览表格**: 支持行选择和删除
- **功能按钮区**: 各分析模块入口
- **筛选状态指示**: 显示当前筛选状态

### 2. 数据筛选功能优化

#### 核心改进
- **返回并应用**: 筛选后直接应用到主界面
- **全局数据状态**: 所有模块使用筛选后数据
- **配置管理**: 保存和加载筛选配置
- **预览功能**: 筛选前预览结果

#### 使用流程
1. 点击"数据筛选"按钮
2. 选择要保留的信号列
3. 可选：预览筛选结果
4. 点击"返回并应用筛选"
5. 主界面和所有分析模块自动使用筛选后数据

### 3. 充电分析算法优化

#### 新算法特性
- **多种查表方式**: 
  - C(MaxT, MinV) - 最高温度 + 最低电压
  - C(MaxT, MaxV) - 最高温度 + 最高电压
  - C(MinT, MinV) - 最低温度 + 最低电压
  - C(MinT, MaxV) - 最低温度 + 最高电压
- **最优值计算**: min(方式1, 方式2, 方式3, 方式4)
- **详细结果**: 显示所有方式的计算结果
- **智能建议**: 基于结果提供分析建议

#### 分析界面
- **标签页设计**: 分析设置、结果显示、导出设置
- **实时进度**: 进度条和状态显示
- **结果可视化**: 表格高亮最优结果
- **多线程处理**: 后台分析不阻塞界面

### 4. 数据导出扩展

#### 导出格式
- **Excel (.xlsx)**: 多工作表结构化导出
- **CSV (.csv)**: 兼容性最佳的格式
- **JSON (.json)**: 程序化处理友好

#### 导出内容选项
- **分析摘要**: 关键指标和统计信息
- **详细结果**: 完整的分析数据
- **原始数据**: 用于分析的源数据
- **分析建议**: 基于结果的专业建议

#### Excel导出结构
```
工作表1: 分析摘要
工作表2: 详细结果  
工作表3: 原始数据
工作表4: 分析建议
```

## 📊 功能对比

| 功能 | Tkinter版本 | PyQt6版本 | 改进说明 |
|------|-------------|-----------|----------|
| 界面设计 | 基础GUI | 现代化专业界面 | 视觉效果大幅提升 |
| 数据筛选 | 返回主页 | 返回并应用 | 筛选结果全局生效 |
| 充电分析 | 单一查表 | 多种查表组合 | 分析更全面准确 |
| 数据导出 | 基础导出 | 多格式多内容 | 导出选项丰富 |
| 用户体验 | 功能性 | 专业性 | 操作更直观便捷 |

## 🔄 迁移指南

### 从Tkinter版本迁移

1. **数据兼容性**: 完全兼容原有CSV数据格式
2. **配置文件**: 筛选配置可在两版本间通用
3. **功能对应**: 所有原有功能在新版本中都有对应

### 配置文件位置
- **筛选配置**: 用户自定义位置
- **数据映射**: `E08-data_mapping_config.json`
- **程序设置**: 自动保存到系统配置目录

## 🐛 故障排除

### 常见问题

#### 1. PyQt6安装失败
```bash
# 尝试升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install PyQt6 -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2. 模块导入错误
- 确认Python版本 >= 3.8
- 检查虚拟环境是否正确激活
- 重新安装依赖包

#### 3. 界面显示异常
- 检查系统显示缩放设置
- 更新显卡驱动程序
- 尝试不同的Qt样式主题

#### 4. 数据处理错误
- 确认CSV文件编码为UTF-8
- 检查数据格式是否正确
- 查看错误日志获取详细信息

### 性能优化建议

1. **大数据处理**: 
   - 使用数据筛选减少处理量
   - 分批处理大型数据集
   - 关闭不必要的可视化效果

2. **内存管理**:
   - 及时清理不需要的数据
   - 使用数据筛选功能
   - 定期重启程序释放内存

## 📞 技术支持

### 获取帮助
- **文档**: 查看项目目录中的详细文档
- **示例**: 参考示例数据和配置文件
- **日志**: 查看程序运行日志获取错误信息

### 反馈渠道
- 功能建议和问题反馈
- 性能优化建议
- 界面改进意见

## 🔮 未来规划

### 计划功能
- **数据可视化增强**: 更多图表类型
- **分析算法扩展**: 更多专业分析方法
- **云端集成**: 支持云端数据存储
- **协作功能**: 多用户协作分析

### 技术升级
- **性能优化**: 更快的数据处理速度
- **界面美化**: 更现代的设计风格
- **跨平台**: 更好的跨平台兼容性

---

## 📝 版本信息

- **当前版本**: PyQt6 2.0
- **发布日期**: 2025-06-26
- **兼容性**: 向下兼容Tkinter版本数据

---

*本升级指南提供了从Tkinter到PyQt6的完整迁移路径。如有任何问题，请参考故障排除部分或查看详细文档。*
